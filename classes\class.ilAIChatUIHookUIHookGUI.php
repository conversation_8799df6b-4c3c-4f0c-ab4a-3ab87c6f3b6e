<?php
declare(strict_types=1);

class ilAIChatUIHookUIHookGUI extends ilUIHookPluginGUI
{
    /**
     * @throws ilTemplateException
     * @throws ilCtrlException
     */
    public function getHTML($a_comp, $a_part, $a_par = array()): array
    {
        global $DIC;
        $ctrl = $DIC->ctrl();

        if (isset($DIC["tpl"])) {
            $tpl = $DIC["tpl"];

            // Verifica si el componente es el que quieres modificar (en este caso, el repositorio)
            if ($a_comp == 'Services/Container' && $a_part == 'right_column') {
                $js_path = "Customizing/global/plugins/Services/UIComponent/UserInterfaceHook/AIChatUIHook/templates/js/script.js";
                $js_aiChat_path = "Customizing/global/plugins/Services/UIComponent/UserInterfaceHook/AIChatUIHook/templates/js/index.js";

                $tpl->addCss($this->plugin_object->getDirectory() . "/templates/css/styles.css");
                $tpl->addJavaScript($js_path);

                // CSS y JS de la carpeta del plugin AIChat.
                $tpl->addCss($this->plugin_object->getDirectory() . "/templates/css/index.css");
                $tpl->addJavaScript($js_aiChat_path);

                $apiUrl = $ctrl->getLinkTargetByClass("ilObjAIChatGUI", "apiCall");
                $apiUrl .= "&src=UIHook";

                return [
                    "mode" => ilUIHookPluginGUI::APPEND,
                    "html" => "<div id='root' class='floatWindow' apiurl='$apiUrl'></div>"
                ];
            }
        }

        return ["mode" => ilUIHookPluginGUI::KEEP, "html" => ""];
    }
}
