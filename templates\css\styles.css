.principalButton {
    position: fixed;
    bottom: 30px;
    right: 40px;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: #fdfdfd;
    color: white;
    border: none;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    font-size: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.principalButton:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 15px rgba(0,0,0,0.3);
}

.principalButton:active {
    transform: scale(0.95);
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

.principalButton img {
    width: 75%;
    height: 75%;
    object-fit: contain;
}

.floatWindow {
    position: fixed;
    bottom: 100px;
    right: 40px;
    width: 30dvw;
    height: 55dvh;
    background-color: #fff;
    border-radius: 12px;
    margin-bottom: 7px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    z-index: 10000;
    /* display: none; */
    visibility: hidden;
    transform: scale(0.9) translateY(73px);
    opacity: 0;
    transition: 0.15s ease-in-out all;
    transform-origin: right;
}

.active {
    visibility: visible;
    opacity: 1;
    transform: scale(1) translateY(0);
    }

@media (width <= 990px) {
    .principalButton {
        bottom: 95px;
    }
}

@media (width <= 800px) {
    .floatWindow {
        width: 50dvw;
    }
}

@media (width <= 400px) {
    .floatWindow {
        width: 80dvw;
    }
}