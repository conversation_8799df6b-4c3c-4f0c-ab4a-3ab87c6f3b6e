<?php

use objects\Chat;
use objects\Message;
use platform\AIChatDatabase;
use platform\AIChatException;

class UIChat extends Chat
{
    private array $messages = array();
    private ?int $max_messages = null;


    public function __construct(?int $id = null, bool $anon = false)
    {
        parent::__construct($id, $anon);
    }

    public function loadFromDB(): void
    {
        $database = new AIChatDatabase();

        $result = $database->select("xaiuh_chats", ["id" => $this->getId()]);

        if (isset($result[0])) {
            $this->setObjId((int)$result[0]["obj_id"]);
            $this->setTitle($result[0]["title"]);
            $this->setCreatedAt(new DateTime($result[0]["created_at"]));
            $this->setUserId((int)$result[0]["user_id"]);
            $this->setLastUpdate(new DateTime($result[0]["last_update"]));
        }

        $messages = $database->select("xaiuh_messages", ["chat_id" => $this->getId()], ["id"], "ORDER BY date ASC");

        foreach ($messages as $message) {
            $this->addMessage(new Message((int)$message["id"]));
        }
    }

    /**
     * @throws AIChatException
     */
    public function save(): void
    {
        $database = new AIChatDatabase();

        $data = [
            "obj_id" => $this->getObjId(),
            "title" => $this->getTitle(),
            "created_at" => $this->getCreatedAt()->format("Y-m-d H:i:s"),
            "user_id" => $this->getUserId(),
            "last_update" => $this->getLastUpdate()->format("Y-m-d H:i:s")
        ];

        if ($this->getId() > 0) {
            $database->update("xaiuh_chats", $data, ["id" => $this->getId()]);
        } else {
            $id = $database->nextId("xaiuh_chats");

            $this->setId($id);

            $data["id"] = $id;

            $database->insert("xaiuh_chats", $data);
        }
    }

    /**
     * @throws AIChatException
     */
    public function toArray(): array
    {
        $messages = array();

        foreach ($this->messages as $message) {
            $messages[] = $message->toArray();
        }

        $max_memory_messages = $this->getMaxMessages();

        if (isset($max_memory_messages)) {
            $max_memory_messages = intval($max_memory_messages);
        } else {
            $max_memory_messages = 0;
        }

        if ($max_memory_messages > 0) {
            $messages = array_slice($messages, -$max_memory_messages);
        }

        return [
            "id" => $this->getId(),
            "obj_id" => $this->getObjId(),
            "title" => $this->getTitle(),
            "created_at" => $this->getCreatedAt()->format("Y-m-d H:i:s"),
            "user_id" => $this->getUserId(),
            "messages" => $messages,
            "last_update" => $this->getLastUpdate()->format("Y-m-d H:i:s")
        ];
    }
}

//    private string $text = "";
//    private int $id = 0;
//    private int $user_id = 0;
//    private string $role = "user";
//    private ?DateTime $date = null;
//    private array $messages = [];
//
//    public function __construct(?int $id = null)
//    {
//        if ($id !== null && $id > 0) {
//            $this->id = $id;
//            $this->loadFromDB(); // Cargar si se proporciona un ID
//        }
//    }
//
//    /**
//     * @throws AIChatException
//     * @throws Exception
//     */
//    public function loadFromDB(): void
//    {
//        GLOBAL $DIC;
//        $database = $DIC->database();
//
//        if ($this->getId() <= 0) {
//            return; // No se puede cargar sin un ID válido
//        }
//
//        $query = $database->queryF("SELECT * FROM xaiuh_messages WHERE id = %s ORDER BY date ASC",
//            ["integer"],
//            [$this->getId()]
//        );
//
//        while($row = $database->fetchAssoc($query)) {
//            $this->setId((int)$row["id"]);
//            $this->setUserId((int)$row["user_id"]);
//            $this->setDate(new DateTime($row["date"]));
//            $this->setRole($row["role"]);
//            $this->setText($row["message"]);
//        }
//    }
//
//    /**
//     * @throws AIChatException
//     */
//    public function save(): void
//    {
//        GLOBAL $DIC;
//        $database = $DIC->database();
//
//        if ($this->getDate() === null) {
//            $this->setDate(new DateTime());
//        }
//
//        $data = [
//            "user_id" => $this->getUserId(),
//            "date" => $this->getDate()->format("Y-m-d H:i:s"),
//            "role" => $this->getRole(),
//            "message" => $this->getText(), // Guardar el contenido de 'text' en la columna 'message'
//        ];
//
//        if ($this->getId() > 0) {
//            $database->update("xaiuh_messages", $data, ["id" => $this->getId()]);
//        } else {
//
//            $id = $database->nextId("xaiuh_messages");
//            $this->setId($id);
//            $data["id"] = $id;
//            //self::sendApiResponse(["error" => json_encode($data)], 500);
//
//            try {
////                $database->insert("xaiuh_messages", $data);
//                $database->manipulateF(
//                    "INSERT INTO xaiuh_messages (id, user_id, date, role, message) VALUES (%s, %s, %s, %s, %s)",
//                    ['integer', 'integer', 'timestamp', 'text', 'text'],
//                    [
//                        $data['id'], $data['user_id'], $data['date'], $data['role'], $data['message']
//                    ]
//                );
//            } catch (Exception $e) {
//                self::sendApiResponse(["error" => $e->getMessage()], 500);
//                throw new AIChatException("Error saving message: " . $e->getMessage());
//            }
//        }
//
//    }
//
//    public static function sendApiResponse($data, int $httpCode = 200): void
//    {
//        http_response_code($httpCode);
//        header('Content-type: application/json');
//        echo json_encode($data);
//        exit();
//    }
//
//    /**
//     * @throws AIChatException
//     */
//    public function toArray(): array
//    {
//        return [
//            "id" => $this->getId(),
//            "user_id" => $this->getUserId(),
//            "date" => $this->date ? $this->date->format("Y-m-d H:i:s") : null,
//            "role" => $this->getRole(),
//            "message" => $this->getText() ?? [],
//        ];
//    }
//
//    public function loadMessages(int $user_id)
//    {
//        global $DIC;
//        $db = $DIC->database();
//
//        $this->setMaxMessages(AIChatConfig::get("max_memory_messages"));
//
//        $query = $db->queryF("SELECT * FROM xaiuh_messages WHERE user_id = %s ORDER BY date DESC",
//            ["integer"],
//            [$user_id]
//        );
//
//        $messages = [];
//        while ($row = $db->fetchAssoc($query)) {
//            $messages[] = [
//                'id' => (int)$row['id'],
//                'user_id' => $user_id,
//                'role' => $row['role'],
//                'date' => $row['date'],
//                'content' => $row['message'],
//            ];
//        }
//
//        $messages = array_reverse($messages);
//
//        $max_memory_messages = $this->getMaxMessages();
//
//        if (isset($max_memory_messages)) {
//            $max_memory_messages = intval($max_memory_messages);
//        } else {
//            $max_memory_messages = 0;
//        }
//
//        if ($max_memory_messages > 0) {
//            $messages_array_for_frontend = array_slice($messages, -$max_memory_messages);
//        }
//
//        return $messages_array_for_frontend;
//    }
//
//    public function saveResponse(array $llm_response): void
//    {
//        global $DIC;
//        $database = $DIC->database();
//        $llm_response["id"] = $database->nextId("xaiuh_messages");
//        $llm_response["user_id"] = $this->getUserId();
//        $llm_response["date"] = date("Y-m-d H:i:s");
//
//        try {
//            $database->manipulateF(
//                "INSERT INTO xaiuh_messages (id, user_id, date, role, message) VALUES (%s, %s, %s, %s, %s)",
//                ['integer', 'integer', 'timestamp', 'text', 'text'],
//                [
//                    $llm_response['id'], $llm_response['user_id'], $llm_response['date'], $llm_response['role'], $llm_response['content']
//                ]
//            );
//        } catch (Exception $e) {
//            self::sendApiResponse(["error" => $e->getMessage()], 500);
//            throw new AIChatException("Error saving message: " . $e->getMessage());
//        }
//    }
//
//    /**
//     * @throws AIChatException
//     */
//    public function delete(): void
//    {
//        global $DIC;
//        $database = $DIC->database();
//        $user_id = $DIC->user()->getId();
//
//        $database->manipulateF(
//            "DELETE FROM xaiuh_messages WHERE user_id = %s",
//            ["integer"],
//            [$user_id]
//        );
//    }
//
//    public function getId(): int
//    {
//        return $this->id;
//    }
//
//    public function setId(int $id): void
//    {
//        $this->id = $id;
//    }
//
//    public function getUserId(): int
//    {
//        return $this->user_id;
//    }
//
//    public function setUserId(int $user_id): void
//    {
//        $this->user_id = $user_id;
//    }
//
//    public function getDate(): ?DateTime
//    {
//        return $this->date;
//    }
//
//    public function setDate(DateTime $date): void
//    {
//        $this->date = $date;
//    }
//
//    public function getMessages(): array
//    {
//        return $this->messages;
//    }
//
//    public function setMessages(Message $messages): void
//    {
//        $this->messages[] = $messages;
//    }
//
//    public function getMaxMessages(): ?int
//    {
//        return $this->max_messages;
//    }
//
//    public function setMaxMessages(?int $max_messages): void
//    {
//        $this->max_messages = $max_messages;
//    }
//
//    public function getRole(): string
//    {
//        return $this->role;
//    }
//
//    public function setRole(string $role): void
//    {
//        $this->role = $role;
//    }
//
//    public function getText(): string
//    {
//        return $this->text;
//    }
//
//    public function setText(string $text): void
//    {
//        $this->text = $text;
//    }
//
//    public function jsonSerialize(): mixed // <--- Implementación del método de la interfaz
//    {
//        // Devuelve un array asociativo con los datos que quieres en el JSON.
//        // Esencialmente, es lo mismo que tu método toArray().
//        return [
//            "id" => $this->getId(),
//            "user_id" => $this->getUserId(),
//            "date" => $this->date ? $this->date->format("Y-m-d H:i:s") : null,
//            "role" => $this->getRole(),
//            "message" => $this->getText(), // Si getText() devuelve un array de objetos Message, y Message es JsonSerializable, funcionará bien.
//            // Si getText() devuelve un string simple, también está bien.
//            // Si quisieras incluir el array $this->messages y sus elementos son objetos Message:
//            // "messages_list" => array_map(function($msg) { return $msg->jsonSerialize(); }, $this->messages), // Ejemplo si Message implementa JsonSerializable
//            // o simplemente:
//            // "messages_list" => $this->messages, // Si los objetos Message ya son JsonSerializable
//        ];
//    }
