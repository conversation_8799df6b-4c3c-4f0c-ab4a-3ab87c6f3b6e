<#1>
<?php
global $DIC;
$ilDB = $DIC->database();

if(!$ilDB->tableExists('xaiuh_chats'))
{
    $fields = array(
        'id' => array(
            'type' => 'integer',
            'notnull' => true,
            'default' => 0
        ),
        'obj_id' => array(
            'type' => 'integer',
            'notnull' => true,
            'default' => 0
        ),
        'title' => array(
            'type' => 'text',
            'notnull' => true,
            'length' => 255
        ),
        'created_at' => array(
            'type' => 'timestamp',
            'notnull' => true,
        ),
        'user_id' => array(
            'type' => 'integer',
            'notnull' => true,
            'default' => 0
        ),
        'last_update' => array(
            'type' => 'timestamp',
            'notnull' => true,
        ),
    );
    $ilDB->createTable('xaiuh_chats', $fields);
    $ilDB->addPrimaryKey('xaiuh_chats', array('id'));
    $ilDB->createSequence('xaiuh_chats');
}

if(!$ilDB->tableExists('xaiuh_messages'))
{
    $fields = array(
        'id' => array(
            'type' => 'integer',
            'notnull' => true,
            'default' => 0
        ),
        'user_id' => array(
            'type' => 'integer',
            'notnull' => true,
            'default' => 0
        ),
        'date' => array(
            'type' => 'timestamp',
            'notnull' => true,
        ),
        'role' => array(
            'type' => 'text',
            'notnull' => true,
            'length' => 20
        ),
        'message' => array(
            'type' => 'blob',
            'notnull' => true,
        ),
    );
    $ilDB->createTable('xaiuh_messages', $fields);
    $ilDB->addPrimaryKey('xaiuh_messages', array('id'));
    $ilDB->createSequence('xaiuh_messages');
}

if(!$ilDB->tableExists('xaiuh_config'))
{
    $fields = array(
        'name' => array(
            'type' => 'text',
            'notnull' => true,
            'length' => 100
        ),
        'value' => array(
            'type' => 'text',
            'notnull' => true,
        ),
    );
    $ilDB->createTable('xaiuh_config', $fields);
    $ilDB->addPrimaryKey('xaiuh_config', array('name'));
}
?>

