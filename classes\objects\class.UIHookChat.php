<?php

use ai\GWDG;
use ai\LLM;
use ai\Ollama;
use ai\OpenAI;
use objects\Chat;
use objects\Message;
use platform\AIChatConfig;
use platform\AIChatDatabase;
use platform\AIChatException;

class UIHookChat
{
    private int $id = 0;
    private bool $online = false;
    private string $prompt = "";
    private string $disclaimer = "";
    private int $max_memory_messages = 0;
    private int $characters_limit = 0;
    private string $openai_model = "";
    private string $openai_api_key = "";
    private bool $openai_streaming = false;
    private string $ollama_model = "";
    private string $service_to_use = "";
    private string $gwdg_model = "";
    private bool $gwdg_streaming = false;
    private ?LLM $llm = null;

    /**
     * @throws AIChatException
     */
    public function __construct(?int $id = null)
    {
        if ($id !== null && $id > 0) {
            $this->id = $id;
        }

        $this->loadFromDB();
        $this->loadLLM();
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): void
    {
        $this->id = $id;
    }

    public function isOnline(): bool
    {
        return $this->online;
    }

    public function setOnline(bool $online): void
    {
        $this->online = $online;
    }

    /**
     * @throws AIChatException
     */
    public function getPrompt(bool $strict = false): string
    {
        if ($this->prompt != "" || $strict) {
            return $this->prompt;
        }

        return AIChatConfig::get("prompt");
    }

    public function setPrompt(string $prompt): void
    {
        $this->prompt = $prompt;
    }

    /**
     * @throws AIChatException
     */
    public function getDisclaimer(bool $strict = false): string
    {
        if ($this->disclaimer != "" || $strict) {
            return $this->disclaimer;
        }

        return AIChatConfig::get("disclaimer");
    }

    public function setDisclaimer(string $disclaimer): void
    {
        $this->disclaimer = $disclaimer;
    }

    /**
     * @throws AIChatException
     */
    public function getMaxMemoryMessages(bool $strict = false): int
    {
        if ($this->max_memory_messages != 0 || $strict) {
            return $this->max_memory_messages;
        }

        if (!empty(AIChatConfig::get("max_memory_messages"))) {
            return AIChatConfig::get("max_memory_messages");
        }

        return 100;
    }

    public function setMaxMemoryMessages(int $max_memory_messages): void
    {
        $this->max_memory_messages = $max_memory_messages;
    }

    /**
     * @throws AIChatException
     */
    public function getCharactersLimit(bool $strict = false): int
    {
        if ($this->characters_limit != 0 || $strict) {
            return $this->characters_limit;
        }

        if (!empty(AIChatConfig::get("characters_limit"))) {
            return AIChatConfig::get("characters_limit");
        }

        return 2000;
    }

    public function setCharactersLimit(int $characters_limit): void
    {
        $this->characters_limit = $characters_limit;
    }

    /**
     * @throws AIChatException
     */
    public function getOpenaiModel(bool $strict = false): string
    {
        GLOBAL $DIC;
        $nameColumn = "openai_model";

        $query = $DIC->database()->queryF('SELECT `value` FROM xaiuh_config WHERE name = %s',
            ['text'],
            [$nameColumn]
        );
        $currentModel = $DIC->database()->fetchAssoc($query) ?? false;

//        dump(OpenAI::MODEL_TYPES);exit();
//        dump(AIChatConfig::get("openai_model"));exit();
        if ($currentModel || $strict) {
            return $currentModel["value"] ?? "Please Select";
        }

        return AIChatConfig::get("openai_model");
    }

    public function setOpenaiModel(string $openai_model): void
    {
        $this->openai_model = $openai_model;

        $this->createOrCheckField("openai_model", $openai_model);
    }

    /**
     * @throws AIChatException
     */
    public function getOpenaiApiKey(bool $strict = false): string
    {
        GLOBAL $DIC;
        $nameColumn = "openai_api_key";

        $query = $DIC->database()->queryF('SELECT `value` FROM xaiuh_config WHERE name = %s',
            ['text'],
            [$nameColumn]
        );

        $existsApi = $DIC->database()->fetchAssoc($query) ?? "";

        if (!empty($existsApi) || $strict) {
            return $existsApi["value"] ?? "";
        }

        return AIChatConfig::get("openai_api_key");
    }

    public function setOpenaiApiKey(string $openai_api_key): void
    {
        $this->openai_api_key = $openai_api_key;

        $this->createOrCheckField("openai_api_key", $openai_api_key);
    }

    /**
     * @throws AIChatException
     */
    public function isOpenaiStreaming(bool $strict = false): bool
    {
        if ($this->getServiceToUse() != "openai") {
            return false;
        }

        GLOBAL $DIC;
        $nameColumn = "openai_streaming";

        $query = $DIC->database()->queryF('SELECT `value` FROM xaiuh_config WHERE name = %s',
            ['text'],
            [$nameColumn]
        );

        $isStreaming = $DIC->database()->fetchAssoc($query) ?? -1;

        if ($isStreaming > -1 || $strict) {
            return ($isStreaming["value"] ?? 0) == "1";
        }

        return AIChatConfig::get("openai_streaming") == "1";
    }

    public function setOpenaiStreaming(bool $openai_streaming): void
    {
        $this->openai_streaming = $openai_streaming;

        $this->createOrCheckField("openai_streaming", $openai_streaming);
    }

    public function getOpenAIModelsList()
    {
        if (!empty(AIChatConfig::get("openai_models"))) {
            return AIChatConfig::get("openai_models");
        }

        return [];
    }

    /**
     * @throws AIChatException
     */
    public function getOllamaModel(bool $strict = false): string
    {
        GLOBAL $DIC;
        $nameColumn = "ollama_model";

        $query = $DIC->database()->queryF('SELECT `value` FROM xaiuh_config WHERE name = %s',
            ['text'],
            [$nameColumn]
        );

        $currentModel = $DIC->database()->fetchAssoc($query) ?? false;

        if ($this->ollama_model != "" || $strict) {
            return $currentModel["value"] ?? "Please Select";
        }

        return AIChatConfig::get("ollama_model");
    }

    public function setOllamaModel(string $ollama_model): void
    {
        $this->ollama_model = $ollama_model;

        $this->createOrCheckField("ollama_model", $ollama_model);
    }

    /**
     * @throws AIChatException
     */
    public function getOllamaModelsList(): array
    {
        if (!empty(AIChatConfig::get("ollama_models"))) {
            return AIChatConfig::get("ollama_models");
        }

        return [];
    }

    public function getServiceToUse(bool $strict = false): string
    {
        $available_services = AIChatConfig::get("available_services");

        if (($this->service_to_use != "" && isset($available_services[$this->service_to_use]) && $available_services[$this->service_to_use]) || $strict) {
            return $this->service_to_use;
        }

        GLOBAL $DIC;
        $nameColumn = "service_to_use";

        $query = $DIC->database()->queryF('SELECT `value` FROM xaiuh_config WHERE name = %s',
            ['text'],
            [$nameColumn]
        );

        $currentModel = $DIC->database()->fetchAssoc($query);

        return $currentModel["value"] ?? "";
    }

    public function setServiceToUse(string $service_to_use): void
    {
        $this->service_to_use = $service_to_use;

        global $DIC;
        $nameColumn = "service_to_use";

        $this->createOrCheckField($nameColumn, $service_to_use);
    }

    public function getGwdgModel(bool $strict = false): string
    {
        GLOBAL $DIC;
        $nameColumn = "gwdg_model";

        $query = $DIC->database()->queryF('SELECT `value` FROM xaiuh_config WHERE name = %s',
            ['text'],
            [$nameColumn]
        );

        $currentModel = $DIC->database()->fetchAssoc($query) ?? false;

        if ($this->gwdg_model != "" || $strict) {
            return $currentModel["value"] ?? "Please Select";
        }

        return AIChatConfig::get("gwdg_model");
    }

    public function setGwdgModel(string $gwdg_model): void
    {
        $this->gwdg_model = $gwdg_model;

        $this->createOrCheckField("gwdg_model", $gwdg_model);
    }

    public function isGwdgStreaming(bool $strict = false): bool
    {
        if ($this->getServiceToUse() != "gwdg") {
            return false;
        }

        if ($this->gwdg_streaming || $strict) {
            return $this->gwdg_streaming;
        }

        return AIChatConfig::get("gwdg_streaming") == "1";
    }

    public function setGwdgStreaming(bool $gwdg_streaming): void
    {
        $this->gwdg_streaming = $gwdg_streaming;
    }

    public function getGwdgModelsList(): array
    {
        if (!empty(AIChatConfig::get("gwdg_models"))) {
            return AIChatConfig::get("gwdg_models");
        }

        return [];
    }

    public function getLlm()
    {
        return $this->llm;
        GLOBAL $DIC;
        $nameColumn = "openai_model";

        $query = $DIC->database()->queryF('SELECT `value` FROM xaiuh_config WHERE name = %s',
            ['text'],
            [$nameColumn]
        );

        return $DIC->database()->fetchAssoc($query);
    }

    public function setLlm(?LLM $llm = null): void
    {
        $this->llm = $llm;

    }

    /**
     * @throws AIChatException
     */
    public function loadFromDB(): void
    {
        global $DIC;
        $database = $DIC->database();

        $query = $DIC->database()->queryF('SELECT * FROM xaiuh_config', [], []);

        $rows = [];

        while ($row = $database->fetchAssoc($query)) {
            $rows[] = $row;
        }

        if (isset($result[0])) {
            $this->setPrompt((string) AIChatConfig::get("prompt"));
            $this->setDisclaimer((string) AIChatConfig::get("disclaimer"));
            $this->setMaxMemoryMessages((int) AIChatConfig::get("max_memory_messages"));
            $this->setCharactersLimit((int) AIChatConfig::get("characters_limit"));
            $this->setOpenaiModel((string) AIChatConfig::get("openai_model"));
            $this->setOpenaiApiKey((string) AIChatConfig::get("openai_api_key"));
            $this->setOpenaiStreaming((bool) AIChatConfig::get("openai_streaming"));
            $this->setOllamaModel((string) $result[0]["ollama_model"]);
            $this->setServiceToUse((string) $result[0]["service_to_use"]);
            $this->setGwdgModel((string) $result[0]["gwdg_model"] ?? "Please Select");
            $this->setGwdgStreaming((bool) AIChatConfig::get("gwdg_streaming"));
        }
    }

    /**
     * @throws AIChatException
     */
    public function save(): void
    {
        if (!isset($this->id) || $this->id == 0) {
            throw new AIChatException("AIChat::save() - AIChat ID is 0");
        }

        $database = new AIChatDatabase();

        $database->insertOnDuplicatedKey("xaic_objects", array(
            "id" => $this->id,
            "online" => (int) $this->online,
            "prompt" => $this->prompt,
            "disclaimer" => $this->disclaimer,
            "max_memory_messages" => $this->max_memory_messages,
            "characters_limit" => $this->characters_limit,
            "openai_model" => $this->openai_model,
            "openai_api_key" => $this->openai_api_key,
            "openai_streaming" => (int) $this->openai_streaming,
            "ollama_model" => $this->ollama_model,
            "service_to_use" => $this->service_to_use,
            "gwdg_model" => $this->gwdg_model,
            "gwdg_streaming" => (int) $this->gwdg_streaming,
        ));
    }

    /**
     * @throws AIChatException
     */
    public function delete(): void
    {
        $database = new AIChatDatabase();

        $database->delete("xaic_objects", ["id" => $this->id]);

        $chats = $database->select("xaic_chats", ["obj_id" => $this->id]);

        foreach ($chats as $chat) {
            $chat_obj = new Chat($chat["id"]);

            $chat_obj->delete();
        }
    }

    /**
     * @throws AIChatException
     */
    private function loadLLM()
    {
        $service_to_use = $this->getServiceToUse();

        if (!empty($service_to_use)) {
            switch ($service_to_use) {
                case "openai":
                    $this->llm = new OpenAI($this->getOpenaiModel());
                    $this->llm->setApiKey($this->getOpenaiApiKey());
                    $this->llm->setMaxMemoryMessages($this->getMaxMemoryMessages());
                    $this->llm->setPrompt($this->getPrompt());
                    $this->llm->setStreaming($this->isOpenaiStreaming());
                    break;
                case "ollama":
                    $models = $this->getOllamaModelsList();
                    $model = $this->getOllamaModel();

                    if (in_array($model, $models)) {
                        $this->llm = new Ollama($model);
                        $this->llm->setEndpoint(AIChatConfig::get("ollama_endpoint"));
                        $this->llm->setMaxMemoryMessages($this->getMaxMemoryMessages());
                        $this->llm->setPrompt($this->getPrompt());
                    }
                    break;
                case "gwdg":
                    $models = $this->getGwdgModelsList();
                    $model = $this->getGwdgModel();

                    if (in_array($model, $models) || array_key_exists($model, $models)) {
                        $this->llm = new GWDG($model);
                        $this->llm->setApiKey(AIChatConfig::get("gwdg_api_key"));
                        $this->llm->setMaxMemoryMessages($this->getMaxMemoryMessages());
                        $this->llm->setPrompt($this->getPrompt());
                        $this->llm->setStreaming($this->isGwdgStreaming());
                    }
                    break;
                default:
                    throw new AIChatException("AIChat::loadLLM() - LLM service to use not valid (Service: " . $service_to_use . ")");
            }
        }
    }

    /**
     * @throws AIChatException
     */
    public function getLLMResponse(\UIChat $chat): Message
    {
        $llm_response = $this->llm->sendChat($chat);

        $response = new Message();

        $response->setChatId($chat->getId());
        $response->setDate(new DateTime());
        $response->setRole("assistant");
        $response->setMessage($llm_response);

        $response->save();

        return $response;
    }

    public function createOrCheckField($nameColumn, $value) {
        GLOBAL $DIC;

        $query = $DIC->database()->queryF('SELECT name FROM xaiuh_config WHERE name = %s',
            ['text'],
            [$nameColumn]
        );

        if ($DIC->database()->fetchAssoc($query)) {
            $DIC->database()->manipulateF(
                'UPDATE xaiuh_config SET value = %s WHERE name = %s',
                ['text', 'text'],
                [$value, $nameColumn]
            );
        } else {
            $DIC->database()->manipulateF(
                'INSERT INTO xaiuh_config (name, value) VALUES (%s, %s)',
                ['text', 'text'],
                [$nameColumn, $value]
            );
        }
    }
}