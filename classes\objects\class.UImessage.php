<?php

use platform\AIChatDatabase;
use platform\AIChatException;

class U<PERSON>message extends \objects\Message
{
    public function __construct(?int $id = null, bool $anon = false)
    {
        parent::__construct($id, $anon);
    }

    /**
     * @throws AIChatException
     * @throws Exception
     */
    public function loadFromDB(): void
    {
        $database = new AIChatDatabase();

        $result = $database->select("xaiuh_messages", ["id" => $this->getId()]);

        if (isset($result[0])) {
            $this->setChatId((int)$result[0]["chat_id"]);
            $this->setDate(new DateTime($result[0]["date"]));
            $this->setRole($result[0]["role"]);
            $this->setMessage($result[0]["message"]);
        }
    }
}