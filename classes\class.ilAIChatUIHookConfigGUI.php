<?php
declare(strict_types=1);
/**
 *  This file is part of the AI Chat Repository Object plugin for ILIAS, which allows your platform's users
 *  To connect with an external LLM service
 *  This plugin is created and maintained by SURLABS.
 *
 *  The AI Chat Repository Object plugin for ILIAS is open-source and licensed under GPL-3.0.
 *  For license details, visit https://www.gnu.org/licenses/gpl-3.0.en.html.
 *
 *  To report bugs or participate in discussions, visit the Mantis system and filter by
 *  the category "AI Chat" at https://mantis.ilias.de.
 *
 *  More information and source code are available at:
 *  https://github.com/surlabs/AIChat
 *
 *  If you need support, please contact the maintainer of this software at:
 *  <EMAIL>
 *
 */

use Customizing\global\plugins\Services\Repository\RepositoryObject\AIChat\classes\components\Hint;
use ILIAS\UI\Component\Input\Container\Form\Standard;
use ILIAS\UI\Component\Input\Field\Section;
use ILIAS\UI\Factory;
use ILIAS\UI\Renderer;
use platform\AIChatConfig;
use platform\AIChatException;
use ai\OpenAI;
use objects\AIChat;
use objects\AIChatUIHookConfig;

/**
 * Class ilAIChatUIHookConfigGUI
 * @authors Jesús Copado, Daniel Cazalla, Saúl Díaz, Juan Aguilar <<EMAIL>>
 * @ilCtrl_IsCalledBy  ilAIChatUIHookConfigGUI: ilObjComponentSettingsGUI
 */
class ilAIChatUIHookConfigGUI extends ilPluginConfigGUI
{
    public function performCommand($cmd): void
    {
        global $DIC;
        $this->factory = $DIC->ui()->factory();
        $this->renderer = $DIC->ui()->renderer();
        $this->refinery = $DIC->refinery();
        $this->control = $DIC->ctrl();
        $this->tpl = $DIC->ui()->mainTemplate();
        $this->request = $DIC->http()->request();

        switch ($cmd) {
            case "configure":
                $rendered = $this->renderForm();
                break;
            default:
                throw new ilException("command not defined");
        }

        $this->tpl->setContent($rendered);
    }

    private function buildForm(): Standard
    {
        return $this->factory->input()->container()->form()->standard(
            "#",
            [
                "API Configuration" => $this->buildApiSection(),
                "general" => $this->buildGeneral(),
            ]
        );
    }

    private function renderForm(): string
    {
        $form = $this->buildForm();

        if ($this->request->getMethod() == "POST") {
            $form = $form->withRequest($this->request);
            $result = $form->getData();
            if ($result) {
                $this->save($result);
                $form = $this->buildForm();
            }
        }

        return $this->renderer->render($form);
    }

    private function buildGeneral(): Section
    {
        $general = [];

        $general["prompt"] = $this->factory->input()->field()->textarea(
            $this->plugin_object->txt("config_prompt_selection"),
            $this->plugin_object->txt("config_prompt_selection_info")
        )->withValue(AIChatConfig::get("prompt"))->withRequired(true)->withDisabled(true);

        $general["characters_limit"] = $this->factory->input()->field()->numeric(
            $this->plugin_object->txt("config_characters_limit_label"),
            $this->plugin_object->txt("config_characters_limit_info")
        )->withValue(AIChatConfig::get("characters_limit"))->withDisabled(true);

        $general["max_memory_messages"] = $this->factory->input()->field()->numeric(
            $this->plugin_object->txt("config_max_memory_messages_label"),
            $this->plugin_object->txt("config_max_memory_messages_info"),
        )->withValue(AIChatConfig::get("max_memory_messages"))->withDisabled(true);

        $general["disclaimer"] = $this->factory->input()->field()->textarea(
            $this->plugin_object->txt("config_disclaimer_text"),
            $this->plugin_object->txt("config_disclaimer_text_info"),
        )->withValue(AIChatConfig::get("disclaimer"))->withRequired(true)->withDisabled(true);

        return $this->factory->input()->field()->section(
            $general,
            $this->plugin_object->txt("config_general_section")
        );
    }

    private function buildApiSection(): Section
    {
        $apiControls = [];
        $aiChatUIHook = new AIChatUIHookConfig();
        $available_services = AIChatConfig::get("available_services");

        $service_to_use = $this->factory->input()->field()->radio(
            $this->plugin_object->txt("config_service_label"),
            $this->plugin_object->txt("config_service_info")
        );

        if (isset($available_services["openai"]) && $available_services["openai"]) {
            $service_to_use = $service_to_use->withOption("openai", "OpenAI");
        }

        if (isset($available_services["ollama"]) && $available_services["ollama"]) {
            $service_to_use = $service_to_use->withOption("ollama", "Ollama");
        }

        if (isset($available_services["gwdg"]) && $available_services["gwdg"]) {
            $service_to_use = $service_to_use->withOption("gwdg", "GWDG");
        }

        $current_service = $aiChatUIHook->getServiceToUse(true);


        if (isset($available_services[$current_service]) && $available_services[$current_service]) {
            $service_to_use = $service_to_use->withValue($current_service);
        }

        $apiControls[] = $service_to_use->withAdditionalTransformation($this->refinery->custom()->transformation(
            function ($v) use ($aiChatUIHook) {
                $aiChatUIHook->setServiceToUse($v);
            }
        ));
//        var_dump($aiChatUIHook->getServiceToUse());exit();

        switch ($aiChatUIHook->getServiceToUse()) {
            case "openai":
                $models = OpenAI::MODEL_TYPES;
                $apiControls[] = $this->factory->input()->field()->select(
                    $this->plugin_object->txt('config_openai_models_label'),
                    $models
                )->withValue($aiChatUIHook->getOpenaiModel())->withAdditionalTransformation($this->refinery->custom()->transformation(
                    function ($v) use ($aiChatUIHook) {
                        $aiChatUIHook->setOpenaiModel($v);
                    }
                ));

                $apiControls[] = $this->factory->input()->field()->text(
                    $this->plugin_object->txt('config_openai_key_label'),
                    $this->plugin_object->txt('config_openai_key_info')
                )->withValue($aiChatUIHook->getOpenaiApiKey(true))->withAdditionalTransformation($this->refinery->custom()->transformation(
                    function ($v) use ($aiChatUIHook) {
                        $aiChatUIHook->setOpenaiApiKey($v);
                    }
                ));

                $apiControls[] = $this->factory->input()->field()->checkbox(
                    $this->plugin_object->txt('config_openai_stream_label'),
                    $this->plugin_object->txt('config_openai_stream_info')
                )->withValue($aiChatUIHook->isOpenaiStreaming(true))->withAdditionalTransformation($this->refinery->custom()->transformation(
                    function ($v) use ($aiChatUIHook) {
                        $aiChatUIHook->setOpenaiStreaming($v);
                    }
                ));

                break;
            case "ollama":
                $ollamaModels = $aiChatUIHook->getOllamaModelsList();

                $model = $this->factory->input()->field()->select(
                    $this->plugin_object->txt('config_ollama_models_label'),
                    $ollamaModels,
                )->withAdditionalTransformation($this->refinery->custom()->transformation(
                    function ($v) use ($aiChatUIHook) {
                        $current_model = $v ?? "Please Select";
                        $aiChatUIHook->setOllamaModel($current_model);
                    }
                ))->withRequired(true);

                if (in_array($aiChatUIHook->getOllamaModel(true), $ollamaModels)) {
                    $model = $model->withValue($aiChatUIHook->getOllamaModel(true));
                }

                $apiControls[] = $model;

                break;
            case "gwdg":
                $gwdgModels = $aiChatUIHook->getGWDGModelsList();

                $model = $this->factory->input()->field()->select(
                    $this->plugin_object->txt('config_gwdg_models_label'),
                    $gwdgModels,
                )->withAdditionalTransformation($this->refinery->custom()->transformation(
                    function ($v) use ($aiChatUIHook) {
                        $current_model = $v ?? "Please Select";
                        $aiChatUIHook->setGWDGModel($current_model);
                    }
                ))->withRequired(true);

                if (in_array($aiChatUIHook->getGWDGModel(true), $gwdgModels) || array_key_exists($aiChatUIHook->getGWDGModel(true), $gwdgModels)) {
                    $model = $model->withValue($aiChatUIHook->getGWDGModel(true));
                }

                $apiControls[] = $model;

                $apiControls[] = $this->factory->input()->field()->checkbox(
                    $this->plugin_object->txt('config_gwdg_stream_label'),
                    $this->plugin_object->txt('config_gwdg_stream_info')
                )->withValue($aiChatUIHook->isGWDGStreaming(true))->withAdditionalTransformation($this->refinery->custom()->transformation(
                    function ($v) use ($aiChatUIHook) {
                        $aiChatUIHook->setGWDGStreaming($v);
                    }
                ));

                break;
        }

        $api_section = $this->factory->input()->field()->section(
            $apiControls,
            $this->plugin_object->txt('config_api_section')
        );

        return $api_section;
    }

    public function save(array $data): void
    {
        if (!empty($data["services"])) {
            $available_services = [];

            foreach ($data["services"] as $service => $values) {
                if ($service == "no-save") {
                    continue;
                }

                if ($values) {
                    $this->saveService($service, $values);

                    $available_services[$service] = true;
                } else {
                    $available_services[$service] = false;
                }
            }

            AIChatConfig::set("available_services", $available_services);
        }

        if (!empty($data["general"])) {
            foreach ($data["general"] as $key => $value) {
                if ($key == "no-save") {
                    continue;
                }

                AIChatConfig::set($key, $value);
            }
        }

        AIChatConfig::save();

        $this->tpl->setOnScreenMessage("success", $this->plugin_object->txt('config_msg_success'), true);
        $this->control->redirect($this, "configure");
    }

    private function getOpenAIModels(): array
    {
        return OpenAI::MODEL_TYPES;
    }

    private function saveService(string $service, array $values): void
    {
        foreach ($values as $key => $value) {
            if ($key == "no-save") {
                continue;
            }

            if ($key == "models") {
                $models_tags = [];

                switch ($service) {
                    case "openai":
                        $models_tags = $this->getOpenAIModels();
                        break;
                    case "ollama":
                        $models_tags = $this->getOLlamaModels($values["endpoint"]);
                        break;
                    case "gwdg":
                        $models_tags = $this->getGWDGModels($values["api_key"]);
                        break;
                }

                $models = [];

                foreach ($value as $model => $selected) {
                    if ($selected) {
                        $models[$model] = $models_tags[$model];
                    }
                }

                $value = $models;
            }

            AIChatConfig::set($service . "_" . $key, $value);
        }
    }

    private function getOLlamaModels(string $llama_endpoint): array
    {
        $llama_endpoint = rtrim($llama_endpoint, '/') . '/api/tags';

        $curlSession = curl_init();
        curl_setopt($curlSession, CURLOPT_URL, $llama_endpoint);
        curl_setopt($curlSession, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curlSession, CURLOPT_TIMEOUT, 10);

        if (ilProxySettings::_getInstance()->isActive()) {
            $proxyHost = ilProxySettings::_getInstance()->getHost();
            $proxyPort = ilProxySettings::_getInstance()->getPort();
            $proxyURL = $proxyHost . ":" . $proxyPort;
            curl_setopt($curlSession, CURLOPT_PROXY, $proxyURL);
        }

        $response = curl_exec($curlSession);

        $models = [];

        if (!curl_errno($curlSession)) {
            $response = json_decode($response, true);


            if (isset($response["models"])) {
                foreach ($response["models"] as $model) {
                    $models[$model['model']] = $model['name'];
                }
            }
        }

        curl_close($curlSession);

        return $models;
    }

    private function getGWDGModels(string $api_key): array
    {
        $curlSession = curl_init();
        curl_setopt($curlSession, CURLOPT_URL, "https://chat-ai.academiccloud.de/v1/models");
        curl_setopt($curlSession, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curlSession, CURLOPT_TIMEOUT, 10);
        curl_setopt($curlSession, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $api_key
        ]);

        if (ilProxySettings::_getInstance()->isActive()) {
            $proxyHost = ilProxySettings::_getInstance()->getHost();
            $proxyPort = ilProxySettings::_getInstance()->getPort();
            $proxyURL = $proxyHost . ":" . $proxyPort;
            curl_setopt($curlSession, CURLOPT_PROXY, $proxyURL);
        }

        $response = curl_exec($curlSession);

        $models = [];

        if (!curl_errno($curlSession)) {
            $response = json_decode($response, true);

            if (isset($response["data"])) {
                foreach ($response["data"] as $model) {
                    $models[$model['id']] = $model['name'];
                }
            }
        }

        curl_close($curlSession);

        return $models;
    }
}