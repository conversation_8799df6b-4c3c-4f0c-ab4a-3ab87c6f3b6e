<?php
declare(strict_types=1);

class ilAIChatUIHookPlugin extends ilUserInterfaceHookPlugin
{
    const PLUGIN_ID = 'xaiuh';

    const PLUGIN_NAME = 'AIChatUIHook';
    protected function uninstallCustom(): void
    {
    }

    public function getPluginName(): string
    {
        return self::PLUGIN_NAME;
    }
    public function allowCopy(): bool
    {
        return true;
    }

    public function uninstall(): bool
    {
        GLOBAL $DIC;

        $DIC->database()->dropSequence('xaiuh_messages');
        $DIC->database()->dropTable('xaiuh_messages');
        $DIC->database()->dropTable('xaiuh_config');

        return parent::uninstall(); // TODO: Change the autogenerated stub
    }
}