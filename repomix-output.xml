This file is a merged representation of the entire codebase, combined into a single document by Repomix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

</file_summary>

<directory_structure>
.gitattributes
classes/class.AIChatUIHookConfig.php
classes/class.ilAIChatUIHookConfigGUI.php
classes/class.ilAIChatUIHookPlugin.php
classes/class.ilAIChatUIHookUIHookGUI.php
classes/objects/class.UIChat.php
classes/objects/class.UImessage.php
lang/ilias_en.lang
plugin.php
sql/dbupdate.php
templates/css/index.css
templates/css/styles.css
templates/js/index.js
templates/js/script.js
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path=".gitattributes">
# Auto detect text files and perform LF normalization
* text=auto
</file>

<file path="classes/class.AIChatUIHookConfig.php">
<?php
declare(strict_types=1);
/**
 *  This file is part of the AI Chat Repository Object plugin for ILIAS, which allows your platform's users
 *  To connect with an external LLM service
 *  This plugin is created and maintained by SURLABS.
 *
 *  The AI Chat Repository Object plugin for ILIAS is open-source and licensed under GPL-3.0.
 *  For license details, visit https://www.gnu.org/licenses/gpl-3.0.en.html.
 *
 *  To report bugs or participate in discussions, visit the Mantis system and filter by
 *  the category "AI Chat" at https://mantis.ilias.de.
 *
 *  More information and source code are available at:
 *  https://github.com/surlabs/AIChat
 *
 *  If you need support, please contact the maintainer of this software at:
 *  <EMAIL>
 *
 */

namespace objects;

use ai\GWDG;
use ai\LLM;
use ai\OpenAI;
use ai\Ollama;
use DateTime;
use platform\AIChatConfig;
use platform\AIChatDatabase;
use platform\AIChatException;

/**
 * Class AIChat
 * @authors Jesús Copado, Daniel Cazalla, Saúl Díaz, Juan Aguilar <<EMAIL>>
 */
class AIChatUIHookConfig
{
//    public function __construct(?int $id = null)
//    {
//        parent::__construct($id);
//    }

    private int $id = 0;
    private bool $online = false;
    private string $prompt = "";
    private string $disclaimer = "";
    private int $max_memory_messages = 0;
    private int $characters_limit = 0;
    private string $openai_model = "";
    private string $openai_api_key = "";
    private bool $openai_streaming = false;
    private string $ollama_model = "";
    private string $service_to_use = "";
    private string $gwdg_model = "";
    private bool $gwdg_streaming = false;
    private ?LLM $llm = null;

    /**
     * @throws AIChatException
     */
    public function __construct(?int $id = null)
    {
        if ($id !== null && $id > 0) {
            $this->id = $id;
        }

        $this->loadFromDB();
        $this->loadLLM();
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): void
    {
        $this->id = $id;
    }

    public function isOnline(): bool
    {
        return $this->online;
    }

    public function setOnline(bool $online): void
    {
        $this->online = $online;
    }

    /**
     * @throws AIChatException
     */
    public function getPrompt(bool $strict = false): string
    {
        if ($this->prompt != "" || $strict) {
            return $this->prompt;
        }

        return AIChatConfig::get("prompt");
    }

    public function setPrompt(string $prompt): void
    {
        $this->prompt = $prompt;
    }

    /**
     * @throws AIChatException
     */
    public function getDisclaimer(bool $strict = false): string
    {
        if ($this->disclaimer != "" || $strict) {
            return $this->disclaimer;
        }

        return AIChatConfig::get("disclaimer");
    }

    public function setDisclaimer(string $disclaimer): void
    {
        $this->disclaimer = $disclaimer;
    }

    /**
     * @throws AIChatException
     */
    public function getMaxMemoryMessages(bool $strict = false): int
    {
        if ($this->max_memory_messages != 0 || $strict) {
            return $this->max_memory_messages;
        }

        if (!empty(AIChatConfig::get("max_memory_messages"))) {
            return AIChatConfig::get("max_memory_messages");
        }

        return 100;
    }

    public function setMaxMemoryMessages(int $max_memory_messages): void
    {
        $this->max_memory_messages = $max_memory_messages;
    }

    /**
     * @throws AIChatException
     */
    public function getCharactersLimit(bool $strict = false): int
    {
        if ($this->characters_limit != 0 || $strict) {
            return $this->characters_limit;
        }

        if (!empty(AIChatConfig::get("characters_limit"))) {
            return AIChatConfig::get("characters_limit");
        }

        return 2000;
    }

    public function setCharactersLimit(int $characters_limit): void
    {
        $this->characters_limit = $characters_limit;
    }

    /**
     * @throws AIChatException
     */
    public function getOpenaiModel(bool $strict = false): string
    {
        GLOBAL $DIC;
        $nameColumn = "openai_model";

        $query = $DIC->database()->queryF('SELECT `value` FROM xaiuh_config WHERE name = %s',
            ['text'],
            [$nameColumn]
        );
        $currentModel = $DIC->database()->fetchAssoc($query) ?? false;

//        dump(OpenAI::MODEL_TYPES);exit();
//        dump(AIChatConfig::get("openai_model"));exit();
        if ($currentModel || $strict) {
            return $currentModel["value"] ?? "Please Select";
        }

        return AIChatConfig::get("openai_model");
    }

    public function setOpenaiModel(string $openai_model): void
    {
        $this->openai_model = $openai_model;

        $this->createOrCheckField("openai_model", $openai_model);
    }

    /**
     * @throws AIChatException
     */
    public function getOpenaiApiKey(bool $strict = false): string
    {
        GLOBAL $DIC;
        $nameColumn = "openai_api_key";

        $query = $DIC->database()->queryF('SELECT `value` FROM xaiuh_config WHERE name = %s',
            ['text'],
            [$nameColumn]
        );

        $existsApi = $DIC->database()->fetchAssoc($query) ?? "";

        if (!empty($existsApi) || $strict) {
            return $existsApi["value"] ?? "";
        }

        return AIChatConfig::get("openai_api_key");
    }

    public function setOpenaiApiKey(string $openai_api_key): void
    {
        $this->openai_api_key = $openai_api_key;

        $this->createOrCheckField("openai_api_key", $openai_api_key);
    }

    /**
     * @throws AIChatException
     */
    public function isOpenaiStreaming(bool $strict = false): bool
    {
        if ($this->getServiceToUse() != "openai") {
            return false;
        }

        GLOBAL $DIC;
        $nameColumn = "openai_streaming";

        $query = $DIC->database()->queryF('SELECT `value` FROM xaiuh_config WHERE name = %s',
            ['text'],
            [$nameColumn]
        );

        $isStreaming = $DIC->database()->fetchAssoc($query) ?? -1;

        if ($isStreaming > -1 || $strict) {
            return ($isStreaming["value"] ?? 0) == "1";
        }

        return AIChatConfig::get("openai_streaming") == "1";
    }

    public function setOpenaiStreaming(bool $openai_streaming): void
    {
        $this->openai_streaming = $openai_streaming;

        $this->createOrCheckField("openai_streaming", $openai_streaming);
    }

    public function getOpenAIModelsList()
    {
        if (!empty(AIChatConfig::get("openai_models"))) {
            return AIChatConfig::get("openai_models");
        }

        return [];
    }

    /**
     * @throws AIChatException
     */
    public function getOllamaModel(bool $strict = false): string
    {
        GLOBAL $DIC;
        $nameColumn = "ollama_model";

        $query = $DIC->database()->queryF('SELECT `value` FROM xaiuh_config WHERE name = %s',
            ['text'],
            [$nameColumn]
        );

        $currentModel = $DIC->database()->fetchAssoc($query) ?? false;

        if ($this->ollama_model != "" || $strict) {
            return $currentModel["value"] ?? "Please Select";
        }

        return AIChatConfig::get("ollama_model");
    }

    public function setOllamaModel(string $ollama_model): void
    {
        $this->ollama_model = $ollama_model;

        $this->createOrCheckField("ollama_model", $ollama_model);
    }

    /**
     * @throws AIChatException
     */
    public function getOllamaModelsList(): array
    {
        if (!empty(AIChatConfig::get("ollama_models"))) {
            return AIChatConfig::get("ollama_models");
        }

        return [];
    }

    public function getServiceToUse(bool $strict = false): string
    {
        $available_services = AIChatConfig::get("available_services");

        if (($this->service_to_use != "" && isset($available_services[$this->service_to_use]) && $available_services[$this->service_to_use]) || $strict) {
            return $this->service_to_use;
        }

        GLOBAL $DIC;
        $nameColumn = "service_to_use";

        $query = $DIC->database()->queryF('SELECT `value` FROM xaiuh_config WHERE name = %s',
            ['text'],
            [$nameColumn]
        );

        $currentModel = $DIC->database()->fetchAssoc($query);

        return $currentModel["value"] ?? "";
    }

    public function setServiceToUse(string $service_to_use): void
    {
        $this->service_to_use = $service_to_use;

        global $DIC;
        $nameColumn = "service_to_use";

        $this->createOrCheckField($nameColumn, $service_to_use);
    }

    public function getGwdgModel(bool $strict = false): string
    {
        GLOBAL $DIC;
        $nameColumn = "gwdg_model";

        $query = $DIC->database()->queryF('SELECT `value` FROM xaiuh_config WHERE name = %s',
            ['text'],
            [$nameColumn]
        );

        $currentModel = $DIC->database()->fetchAssoc($query) ?? false;

        if ($this->gwdg_model != "" || $strict) {
            return $currentModel["value"] ?? "Please Select";
        }

        return AIChatConfig::get("gwdg_model");
    }

    public function setGwdgModel(string $gwdg_model): void
    {
        $this->gwdg_model = $gwdg_model;

        $this->createOrCheckField("gwdg_model", $gwdg_model);
    }

    public function isGwdgStreaming(bool $strict = false): bool
    {
        if ($this->getServiceToUse() != "gwdg") {
            return false;
        }

        if ($this->gwdg_streaming || $strict) {
            return $this->gwdg_streaming;
        }

        return AIChatConfig::get("gwdg_streaming") == "1";
    }

    public function setGwdgStreaming(bool $gwdg_streaming): void
    {
        $this->gwdg_streaming = $gwdg_streaming;
    }

    public function getGwdgModelsList(): array
    {
        if (!empty(AIChatConfig::get("gwdg_models"))) {
            return AIChatConfig::get("gwdg_models");
        }

        return [];
    }

    public function getLlm()
    {
        return $this->llm;
        GLOBAL $DIC;
        $nameColumn = "openai_model";

        $query = $DIC->database()->queryF('SELECT `value` FROM xaiuh_config WHERE name = %s',
            ['text'],
            [$nameColumn]
        );

        return $DIC->database()->fetchAssoc($query);
    }

    public function setLlm(?LLM $llm = null): void
    {
        $this->llm = $llm;

    }

    /**
     * @throws AIChatException
     */
    public function loadFromDB(): void
    {
        global $DIC;
        $database = $DIC->database();

        $query = $DIC->database()->queryF('SELECT * FROM xaiuh_config', [], []);

        $rows = [];

        while ($row = $database->fetchAssoc($query)) {
            $rows[] = $row;
        }
        dump(AIChatConfig::getAll());exit();
        if (isset($result[0])) {
            $this->setPrompt((string) AIChatConfig::get("prompt"));
            $this->setDisclaimer((string) AIChatConfig::get("disclaimer"));
            $this->setMaxMemoryMessages((int) AIChatConfig::get("max_memory_messages"));
            $this->setCharactersLimit((int) AIChatConfig::get("characters_limit"));
            $this->setOpenaiModel((string) AIChatConfig::get("openai_model"));
            $this->setOpenaiApiKey((string) AIChatConfig::get("openai_api_key"));
            $this->setOpenaiStreaming((bool) AIChatConfig::get("openai_streaming"));
            $this->setOllamaModel((string) $result[0]["ollama_model"]);
            $this->setServiceToUse((string) $result[0]["service_to_use"]);
            $this->setGwdgModel((string) $result[0]["gwdg_model"] ?? "Please Select");
            $this->setGwdgStreaming((bool) AIChatConfig::get("gwdg_streaming"));
        }
    }

    /**
     * @throws AIChatException
     */
    public function save(): void
    {
        if (!isset($this->id) || $this->id == 0) {
            throw new AIChatException("AIChat::save() - AIChat ID is 0");
        }

        $database = new AIChatDatabase();

        $database->insertOnDuplicatedKey("xaic_objects", array(
            "id" => $this->id,
            "online" => (int) $this->online,
            "prompt" => $this->prompt,
            "disclaimer" => $this->disclaimer,
            "max_memory_messages" => $this->max_memory_messages,
            "characters_limit" => $this->characters_limit,
            "openai_model" => $this->openai_model,
            "openai_api_key" => $this->openai_api_key,
            "openai_streaming" => (int) $this->openai_streaming,
            "ollama_model" => $this->ollama_model,
            "service_to_use" => $this->service_to_use,
            "gwdg_model" => $this->gwdg_model,
            "gwdg_streaming" => (int) $this->gwdg_streaming,
        ));
    }

    /**
     * @throws AIChatException
     */
    public function delete(): void
    {
        $database = new AIChatDatabase();

        $database->delete("xaic_objects", ["id" => $this->id]);

        $chats = $database->select("xaic_chats", ["obj_id" => $this->id]);

        foreach ($chats as $chat) {
            $chat_obj = new Chat($chat["id"]);

            $chat_obj->delete();
        }
    }

    /**
     * @throws AIChatException
     */
    public function getChatsForApi(?int $user_id = null): array
    {
        $database = new AIChatDatabase();

        $where = [
            "obj_id" => $this->getId(),
        ];

        if (isset($user_id) && $user_id > 0) {
            $where["user_id"] = $user_id;
        }

        $chats = $database->select("xaic_chats", $where, null, "ORDER BY last_update DESC");

        if (empty($chats) && isset($user_id) && $user_id > 0) {
            $chat = new Chat();

            $chat->setMaxMessages($this->getMaxMemoryMessages());

            $chat->setObjId($this->getId());
            $chat->setUserId($user_id);

            $chat->save();

            return $this->getChatsForApi($user_id);
        }

        return $chats;
    }

    /**
     * @throws AIChatException
     */
    private function loadLLM()
    {
        $service_to_use = $this->getServiceToUse();

        if (!empty($service_to_use)) {
            switch ($service_to_use) {
                case "openai":
                    $this->llm = new OpenAI($this->getOpenaiModel());
                    $this->llm->setApiKey($this->getOpenaiApiKey());
                    $this->llm->setMaxMemoryMessages($this->getMaxMemoryMessages());
                    $this->llm->setPrompt($this->getPrompt());
                    $this->llm->setStreaming($this->isOpenaiStreaming());
                    break;
                case "ollama":
                    $models = $this->getOllamaModelsList();
                    $model = $this->getOllamaModel();

                    if (in_array($model, $models)) {
                        $this->llm = new Ollama($model);
                        $this->llm->setEndpoint(AIChatConfig::get("ollama_endpoint"));
                        $this->llm->setMaxMemoryMessages($this->getMaxMemoryMessages());
                        $this->llm->setPrompt($this->getPrompt());
                    }
                    break;
                case "gwdg":
                    $models = $this->getGwdgModelsList();
                    $model = $this->getGwdgModel();

                    if (in_array($model, $models) || array_key_exists($model, $models)) {
                        $this->llm = new GWDG($model);
                        $this->llm->setApiKey(AIChatConfig::get("gwdg_api_key"));
                        $this->llm->setMaxMemoryMessages($this->getMaxMemoryMessages());
                        $this->llm->setPrompt($this->getPrompt());
                        $this->llm->setStreaming($this->isGwdgStreaming());
                    }
                    break;
                default:
                    throw new AIChatException("AIChat::loadLLM() - LLM service to use not valid (Service: " . $service_to_use . ")");
            }
        }
    }

    /**
     * @throws AIChatException
     */
    public function getLLMResponse(\UIChat $chat): Message
    {
        $llm_response = $this->llm->sendChat($chat);

        $response = new Message();

        $response->setChatId($chat->getId());
        $response->setDate(new DateTime());
        $response->setRole("assistant");
        $response->setMessage($llm_response);

        $response->save();

        return $response;
    }

    public function createOrCheckField($nameColumn, $value) {
        GLOBAL $DIC;

        $query = $DIC->database()->queryF('SELECT name FROM xaiuh_config WHERE name = %s',
            ['text'],
            [$nameColumn]
        );

        if ($DIC->database()->fetchAssoc($query)) {
            $DIC->database()->manipulateF(
                'UPDATE xaiuh_config SET value = %s WHERE name = %s',
                ['text', 'text'],
                [$value, $nameColumn]
            );
        } else {
            $DIC->database()->manipulateF(
                'INSERT INTO xaiuh_config (name, value) VALUES (%s, %s)',
                ['text', 'text'],
                [$nameColumn, $value]
            );
        }
    }
}
</file>

<file path="classes/class.ilAIChatUIHookConfigGUI.php">
<?php
declare(strict_types=1);
/**
 *  This file is part of the AI Chat Repository Object plugin for ILIAS, which allows your platform's users
 *  To connect with an external LLM service
 *  This plugin is created and maintained by SURLABS.
 *
 *  The AI Chat Repository Object plugin for ILIAS is open-source and licensed under GPL-3.0.
 *  For license details, visit https://www.gnu.org/licenses/gpl-3.0.en.html.
 *
 *  To report bugs or participate in discussions, visit the Mantis system and filter by
 *  the category "AI Chat" at https://mantis.ilias.de.
 *
 *  More information and source code are available at:
 *  https://github.com/surlabs/AIChat
 *
 *  If you need support, please contact the maintainer of this software at:
 *  <EMAIL>
 *
 */

use Customizing\global\plugins\Services\Repository\RepositoryObject\AIChat\classes\components\Hint;
use ILIAS\UI\Component\Input\Container\Form\Standard;
use ILIAS\UI\Component\Input\Field\Section;
use ILIAS\UI\Factory;
use ILIAS\UI\Renderer;
use platform\AIChatConfig;
use platform\AIChatException;
use ai\OpenAI;
use objects\AIChat;
use objects\AIChatUIHookConfig;

/**
 * Class ilAIChatUIHookConfigGUI
 * @authors Jesús Copado, Daniel Cazalla, Saúl Díaz, Juan Aguilar <<EMAIL>>
 * @ilCtrl_IsCalledBy  ilAIChatUIHookConfigGUI: ilObjComponentSettingsGUI
 */
class ilAIChatUIHookConfigGUI extends ilPluginConfigGUI
{
    public function performCommand($cmd): void
    {
        global $DIC;
        $this->factory = $DIC->ui()->factory();
        $this->renderer = $DIC->ui()->renderer();
        $this->refinery = $DIC->refinery();
        $this->control = $DIC->ctrl();
        $this->tpl = $DIC->ui()->mainTemplate();
        $this->request = $DIC->http()->request();

        switch ($cmd) {
            case "configure":
                $rendered = $this->renderForm();
                break;
            default:
                throw new ilException("command not defined");
        }

        $this->tpl->setContent($rendered);
    }

    private function buildForm(): Standard
    {
        return $this->factory->input()->container()->form()->standard(
            "#",
            [
                "API Configuration" => $this->buildApiSection(),
                "general" => $this->buildGeneral(),
            ]
        );
    }

    private function renderForm(): string
    {
        $form = $this->buildForm();

        if ($this->request->getMethod() == "POST") {
            $form = $form->withRequest($this->request);
            $result = $form->getData();
            if ($result) {
                $this->save($result);
                $form = $this->buildForm();
            }
        }

        return $this->renderer->render($form);
    }

    private function buildGeneral(): Section
    {
        $general = [];

        $general["prompt"] = $this->factory->input()->field()->textarea(
            $this->plugin_object->txt("config_prompt_selection"),
            $this->plugin_object->txt("config_prompt_selection_info")
        )->withValue(AIChatConfig::get("prompt"))->withRequired(true)->withDisabled(true);

        $general["characters_limit"] = $this->factory->input()->field()->numeric(
            $this->plugin_object->txt("config_characters_limit_label"),
            $this->plugin_object->txt("config_characters_limit_info")
        )->withValue(AIChatConfig::get("characters_limit"))->withDisabled(true);

        $general["max_memory_messages"] = $this->factory->input()->field()->numeric(
            $this->plugin_object->txt("config_max_memory_messages_label"),
            $this->plugin_object->txt("config_max_memory_messages_info"),
        )->withValue(AIChatConfig::get("max_memory_messages"))->withDisabled(true);

        $general["disclaimer"] = $this->factory->input()->field()->textarea(
            $this->plugin_object->txt("config_disclaimer_text"),
            $this->plugin_object->txt("config_disclaimer_text_info"),
        )->withValue(AIChatConfig::get("disclaimer"))->withRequired(true)->withDisabled(true);

        return $this->factory->input()->field()->section(
            $general,
            $this->plugin_object->txt("config_general_section")
        );
    }

    private function buildApiSection(): Section
    {
        $apiControls = [];
        $aiChatUIHook = new AIChatUIHookConfig();
        $available_services = AIChatConfig::get("available_services");

        $service_to_use = $this->factory->input()->field()->radio(
            $this->plugin_object->txt("config_service_label"),
            $this->plugin_object->txt("config_service_info")
        );

        if (isset($available_services["openai"]) && $available_services["openai"]) {
            $service_to_use = $service_to_use->withOption("openai", "OpenAI");
        }

        if (isset($available_services["ollama"]) && $available_services["ollama"]) {
            $service_to_use = $service_to_use->withOption("ollama", "Ollama");
        }

        if (isset($available_services["gwdg"]) && $available_services["gwdg"]) {
            $service_to_use = $service_to_use->withOption("gwdg", "GWDG");
        }

        $current_service = $aiChatUIHook->getServiceToUse(true);


        if (isset($available_services[$current_service]) && $available_services[$current_service]) {
            $service_to_use = $service_to_use->withValue($current_service);
        }

        $apiControls[] = $service_to_use->withAdditionalTransformation($this->refinery->custom()->transformation(
            function ($v) use ($aiChatUIHook) {
                $aiChatUIHook->setServiceToUse($v);
            }
        ));
//        var_dump($aiChatUIHook->getServiceToUse());exit();

        switch ($aiChatUIHook->getServiceToUse()) {
            case "openai":
                $models = OpenAI::MODEL_TYPES;
                $apiControls[] = $this->factory->input()->field()->select(
                    $this->plugin_object->txt('config_openai_models_label'),
                    $models
                )->withValue($aiChatUIHook->getOpenaiModel())->withAdditionalTransformation($this->refinery->custom()->transformation(
                    function ($v) use ($aiChatUIHook) {
                        $aiChatUIHook->setOpenaiModel($v);
                    }
                ));

                $apiControls[] = $this->factory->input()->field()->text(
                    $this->plugin_object->txt('config_openai_key_label'),
                    $this->plugin_object->txt('config_openai_key_info')
                )->withValue($aiChatUIHook->getOpenaiApiKey(true))->withAdditionalTransformation($this->refinery->custom()->transformation(
                    function ($v) use ($aiChatUIHook) {
                        $aiChatUIHook->setOpenaiApiKey($v);
                    }
                ));

                $apiControls[] = $this->factory->input()->field()->checkbox(
                    $this->plugin_object->txt('config_openai_stream_label'),
                    $this->plugin_object->txt('config_openai_stream_info')
                )->withValue($aiChatUIHook->isOpenaiStreaming(true))->withAdditionalTransformation($this->refinery->custom()->transformation(
                    function ($v) use ($aiChatUIHook) {
                        $aiChatUIHook->setOpenaiStreaming($v);
                    }
                ));

                break;
            case "ollama":
                $ollamaModels = $aiChatUIHook->getOllamaModelsList();

                $model = $this->factory->input()->field()->select(
                    $this->plugin_object->txt('config_ollama_models_label'),
                    $ollamaModels,
                )->withAdditionalTransformation($this->refinery->custom()->transformation(
                    function ($v) use ($aiChatUIHook) {
                        $current_model = $v ?? "Please Select";
                        $aiChatUIHook->setOllamaModel($current_model);
                    }
                ))->withRequired(true);

                if (in_array($aiChatUIHook->getOllamaModel(true), $ollamaModels)) {
                    $model = $model->withValue($aiChatUIHook->getOllamaModel(true));
                }

                $apiControls[] = $model;

                break;
            case "gwdg":
                $gwdgModels = $aiChatUIHook->getGWDGModelsList();

                $model = $this->factory->input()->field()->select(
                    $this->plugin_object->txt('config_gwdg_models_label'),
                    $gwdgModels,
                )->withAdditionalTransformation($this->refinery->custom()->transformation(
                    function ($v) use ($aiChatUIHook) {
                        $current_model = $v ?? "Please Select";
                        $aiChatUIHook->setGWDGModel($current_model);
                    }
                ))->withRequired(true);

                if (in_array($aiChatUIHook->getGWDGModel(true), $gwdgModels) || array_key_exists($aiChatUIHook->getGWDGModel(true), $gwdgModels)) {
                    $model = $model->withValue($aiChatUIHook->getGWDGModel(true));
                }

                $apiControls[] = $model;

                $apiControls[] = $this->factory->input()->field()->checkbox(
                    $this->plugin_object->txt('config_gwdg_stream_label'),
                    $this->plugin_object->txt('config_gwdg_stream_info')
                )->withValue($aiChatUIHook->isGWDGStreaming(true))->withAdditionalTransformation($this->refinery->custom()->transformation(
                    function ($v) use ($aiChatUIHook) {
                        $aiChatUIHook->setGWDGStreaming($v);
                    }
                ));

                break;
        }

        $api_section = $this->factory->input()->field()->section(
            $apiControls,
            $this->plugin_object->txt('config_api_section')
        );

        return $api_section;
    }

    public function save(array $data): void
    {
        if (!empty($data["services"])) {
            $available_services = [];

            foreach ($data["services"] as $service => $values) {
                if ($service == "no-save") {
                    continue;
                }

                if ($values) {
                    $this->saveService($service, $values);

                    $available_services[$service] = true;
                } else {
                    $available_services[$service] = false;
                }
            }

            AIChatConfig::set("available_services", $available_services);
        }

        if (!empty($data["general"])) {
            foreach ($data["general"] as $key => $value) {
                if ($key == "no-save") {
                    continue;
                }

                AIChatConfig::set($key, $value);
            }
        }

        AIChatConfig::save();

        $this->tpl->setOnScreenMessage("success", $this->plugin_object->txt('config_msg_success'), true);
        $this->control->redirect($this, "configure");
    }

    private function getOpenAIModels(): array
    {
        return OpenAI::MODEL_TYPES;
    }

    private function saveService(string $service, array $values): void
    {
        foreach ($values as $key => $value) {
            if ($key == "no-save") {
                continue;
            }

            if ($key == "models") {
                $models_tags = [];

                switch ($service) {
                    case "openai":
                        $models_tags = $this->getOpenAIModels();
                        break;
                    case "ollama":
                        $models_tags = $this->getOLlamaModels($values["endpoint"]);
                        break;
                    case "gwdg":
                        $models_tags = $this->getGWDGModels($values["api_key"]);
                        break;
                }

                $models = [];

                foreach ($value as $model => $selected) {
                    if ($selected) {
                        $models[$model] = $models_tags[$model];
                    }
                }

                $value = $models;
            }

            AIChatConfig::set($service . "_" . $key, $value);
        }
    }

    private function getOLlamaModels(string $llama_endpoint): array
    {
        $llama_endpoint = rtrim($llama_endpoint, '/') . '/api/tags';

        $curlSession = curl_init();
        curl_setopt($curlSession, CURLOPT_URL, $llama_endpoint);
        curl_setopt($curlSession, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curlSession, CURLOPT_TIMEOUT, 10);

        if (ilProxySettings::_getInstance()->isActive()) {
            $proxyHost = ilProxySettings::_getInstance()->getHost();
            $proxyPort = ilProxySettings::_getInstance()->getPort();
            $proxyURL = $proxyHost . ":" . $proxyPort;
            curl_setopt($curlSession, CURLOPT_PROXY, $proxyURL);
        }

        $response = curl_exec($curlSession);

        $models = [];

        if (!curl_errno($curlSession)) {
            $response = json_decode($response, true);


            if (isset($response["models"])) {
                foreach ($response["models"] as $model) {
                    $models[$model['model']] = $model['name'];
                }
            }
        }

        curl_close($curlSession);

        return $models;
    }

    private function getGWDGModels(string $api_key): array
    {
        $curlSession = curl_init();
        curl_setopt($curlSession, CURLOPT_URL, "https://chat-ai.academiccloud.de/v1/models");
        curl_setopt($curlSession, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curlSession, CURLOPT_TIMEOUT, 10);
        curl_setopt($curlSession, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $api_key
        ]);

        if (ilProxySettings::_getInstance()->isActive()) {
            $proxyHost = ilProxySettings::_getInstance()->getHost();
            $proxyPort = ilProxySettings::_getInstance()->getPort();
            $proxyURL = $proxyHost . ":" . $proxyPort;
            curl_setopt($curlSession, CURLOPT_PROXY, $proxyURL);
        }

        $response = curl_exec($curlSession);

        $models = [];

        if (!curl_errno($curlSession)) {
            $response = json_decode($response, true);

            if (isset($response["data"])) {
                foreach ($response["data"] as $model) {
                    $models[$model['id']] = $model['name'];
                }
            }
        }

        curl_close($curlSession);

        return $models;
    }
}
</file>

<file path="classes/class.ilAIChatUIHookPlugin.php">
<?php
declare(strict_types=1);

class ilAIChatUIHookPlugin extends ilUserInterfaceHookPlugin
{
    const PLUGIN_ID = 'xaiuh';

    const PLUGIN_NAME = 'AIChatUIHook';
    protected function uninstallCustom(): void
    {
    }

    public function getPluginName(): string
    {
        return self::PLUGIN_NAME;
    }
    public function allowCopy(): bool
    {
        return true;
    }

    public function uninstall(): bool
    {
        GLOBAL $DIC;

        $DIC->database()->dropSequence('xaiuh_messages');
        $DIC->database()->dropTable('xaiuh_messages');
        $DIC->database()->dropTable('xaiuh_config');

        return parent::uninstall(); // TODO: Change the autogenerated stub
    }
}
</file>

<file path="classes/class.ilAIChatUIHookUIHookGUI.php">
<?php
declare(strict_types=1);

class ilAIChatUIHookUIHookGUI extends ilUIHookPluginGUI
{
    /**
     * @throws ilTemplateException
     * @throws ilCtrlException
     */
    public function getHTML($a_comp, $a_part, $a_par = array()): array
    {
        global $DIC;
        $ctrl = $DIC->ctrl();

        if (isset($DIC["tpl"])) {
            $tpl = $DIC["tpl"];

            // Verifica si el componente es el que quieres modificar (en este caso, el repositorio)
            if ($a_comp == 'Services/Container' && $a_part == 'right_column') {
                $js_path = "Customizing/global/plugins/Services/UIComponent/UserInterfaceHook/AIChatUIHook/templates/js/script.js";
                $js_aiChat_path = "Customizing/global/plugins/Services/UIComponent/UserInterfaceHook/AIChatUIHook/templates/js/index.js";

                $tpl->addCss($this->plugin_object->getDirectory() . "/templates/css/styles.css");
                $tpl->addJavaScript($js_path);

                // CSS y JS de la carpeta del plugin AIChat.
                $tpl->addCss($this->plugin_object->getDirectory() . "/templates/css/index.css");
                $tpl->addJavaScript($js_aiChat_path);

                $apiUrl = $ctrl->getLinkTargetByClass("ilObjAIChatGUI", "apiCall");
                $apiUrl .= "&src=UIHook";

                return [
                    "mode" => ilUIHookPluginGUI::APPEND,
                    "html" => "<div id='root' class='floatWindow' apiurl='$apiUrl'></div>"
                ];
            }
        }

        return ["mode" => ilUIHookPluginGUI::KEEP, "html" => ""];
    }
}
</file>

<file path="classes/objects/class.UIChat.php">
<?php

use objects\Chat;
use objects\Message;
use platform\AIChatDatabase;
use platform\AIChatException;

class UIChat extends Chat
{
    private array $messages = array();
    private ?int $max_messages = null;


    public function __construct(?int $id = null, bool $anon = false)
    {
        parent::__construct($id, $anon);
    }

    public function loadFromDB(): void
    {
        $database = new AIChatDatabase();

        $result = $database->select("xaiuh_chats", ["id" => $this->getId()]);

        if (isset($result[0])) {
            $this->setObjId((int)$result[0]["obj_id"]);
            $this->setTitle($result[0]["title"]);
            $this->setCreatedAt(new DateTime($result[0]["created_at"]));
            $this->setUserId((int)$result[0]["user_id"]);
            $this->setLastUpdate(new DateTime($result[0]["last_update"]));
        }

        $messages = $database->select("xaiuh_messages", ["chat_id" => $this->getId()], ["id"], "ORDER BY date ASC");

        foreach ($messages as $message) {
            $this->addMessage(new Message((int)$message["id"]));
        }
    }

    /**
     * @throws AIChatException
     */
    public function save(): void
    {
        $database = new AIChatDatabase();

        $data = [
            "obj_id" => $this->getObjId(),
            "title" => $this->getTitle(),
            "created_at" => $this->getCreatedAt()->format("Y-m-d H:i:s"),
            "user_id" => $this->getUserId(),
            "last_update" => $this->getLastUpdate()->format("Y-m-d H:i:s")
        ];

        if ($this->getId() > 0) {
            $database->update("xaiuh_chats", $data, ["id" => $this->getId()]);
        } else {
            $id = $database->nextId("xaiuh_chats");

            $this->setId($id);

            $data["id"] = $id;

            $database->insert("xaiuh_chats", $data);
        }
    }

    /**
     * @throws AIChatException
     */
    public function toArray(): array
    {
        $messages = array();

        foreach ($this->messages as $message) {
            $messages[] = $message->toArray();
        }

        $max_memory_messages = $this->getMaxMessages();

        if (isset($max_memory_messages)) {
            $max_memory_messages = intval($max_memory_messages);
        } else {
            $max_memory_messages = 0;
        }

        if ($max_memory_messages > 0) {
            $messages = array_slice($messages, -$max_memory_messages);
        }

        return [
            "id" => $this->getId(),
            "obj_id" => $this->getObjId(),
            "title" => $this->getTitle(),
            "created_at" => $this->getCreatedAt()->format("Y-m-d H:i:s"),
            "user_id" => $this->getUserId(),
            "messages" => $messages,
            "last_update" => $this->getLastUpdate()->format("Y-m-d H:i:s")
        ];
    }
}

//    private string $text = "";
//    private int $id = 0;
//    private int $user_id = 0;
//    private string $role = "user";
//    private ?DateTime $date = null;
//    private array $messages = [];
//
//    public function __construct(?int $id = null)
//    {
//        if ($id !== null && $id > 0) {
//            $this->id = $id;
//            $this->loadFromDB(); // Cargar si se proporciona un ID
//        }
//    }
//
//    /**
//     * @throws AIChatException
//     * @throws Exception
//     */
//    public function loadFromDB(): void
//    {
//        GLOBAL $DIC;
//        $database = $DIC->database();
//
//        if ($this->getId() <= 0) {
//            return; // No se puede cargar sin un ID válido
//        }
//
//        $query = $database->queryF("SELECT * FROM xaiuh_messages WHERE id = %s ORDER BY date ASC",
//            ["integer"],
//            [$this->getId()]
//        );
//
//        while($row = $database->fetchAssoc($query)) {
//            $this->setId((int)$row["id"]);
//            $this->setUserId((int)$row["user_id"]);
//            $this->setDate(new DateTime($row["date"]));
//            $this->setRole($row["role"]);
//            $this->setText($row["message"]);
//        }
//    }
//
//    /**
//     * @throws AIChatException
//     */
//    public function save(): void
//    {
//        GLOBAL $DIC;
//        $database = $DIC->database();
//
//        if ($this->getDate() === null) {
//            $this->setDate(new DateTime());
//        }
//
//        $data = [
//            "user_id" => $this->getUserId(),
//            "date" => $this->getDate()->format("Y-m-d H:i:s"),
//            "role" => $this->getRole(),
//            "message" => $this->getText(), // Guardar el contenido de 'text' en la columna 'message'
//        ];
//
//        if ($this->getId() > 0) {
//            $database->update("xaiuh_messages", $data, ["id" => $this->getId()]);
//        } else {
//
//            $id = $database->nextId("xaiuh_messages");
//            $this->setId($id);
//            $data["id"] = $id;
//            //self::sendApiResponse(["error" => json_encode($data)], 500);
//
//            try {
////                $database->insert("xaiuh_messages", $data);
//                $database->manipulateF(
//                    "INSERT INTO xaiuh_messages (id, user_id, date, role, message) VALUES (%s, %s, %s, %s, %s)",
//                    ['integer', 'integer', 'timestamp', 'text', 'text'],
//                    [
//                        $data['id'], $data['user_id'], $data['date'], $data['role'], $data['message']
//                    ]
//                );
//            } catch (Exception $e) {
//                self::sendApiResponse(["error" => $e->getMessage()], 500);
//                throw new AIChatException("Error saving message: " . $e->getMessage());
//            }
//        }
//
//    }
//
//    public static function sendApiResponse($data, int $httpCode = 200): void
//    {
//        http_response_code($httpCode);
//        header('Content-type: application/json');
//        echo json_encode($data);
//        exit();
//    }
//
//    /**
//     * @throws AIChatException
//     */
//    public function toArray(): array
//    {
//        return [
//            "id" => $this->getId(),
//            "user_id" => $this->getUserId(),
//            "date" => $this->date ? $this->date->format("Y-m-d H:i:s") : null,
//            "role" => $this->getRole(),
//            "message" => $this->getText() ?? [],
//        ];
//    }
//
//    public function loadMessages(int $user_id)
//    {
//        global $DIC;
//        $db = $DIC->database();
//
//        $this->setMaxMessages(AIChatConfig::get("max_memory_messages"));
//
//        $query = $db->queryF("SELECT * FROM xaiuh_messages WHERE user_id = %s ORDER BY date DESC",
//            ["integer"],
//            [$user_id]
//        );
//
//        $messages = [];
//        while ($row = $db->fetchAssoc($query)) {
//            $messages[] = [
//                'id' => (int)$row['id'],
//                'user_id' => $user_id,
//                'role' => $row['role'],
//                'date' => $row['date'],
//                'content' => $row['message'],
//            ];
//        }
//
//        $messages = array_reverse($messages);
//
//        $max_memory_messages = $this->getMaxMessages();
//
//        if (isset($max_memory_messages)) {
//            $max_memory_messages = intval($max_memory_messages);
//        } else {
//            $max_memory_messages = 0;
//        }
//
//        if ($max_memory_messages > 0) {
//            $messages_array_for_frontend = array_slice($messages, -$max_memory_messages);
//        }
//
//        return $messages_array_for_frontend;
//    }
//
//    public function saveResponse(array $llm_response): void
//    {
//        global $DIC;
//        $database = $DIC->database();
//        $llm_response["id"] = $database->nextId("xaiuh_messages");
//        $llm_response["user_id"] = $this->getUserId();
//        $llm_response["date"] = date("Y-m-d H:i:s");
//
//        try {
//            $database->manipulateF(
//                "INSERT INTO xaiuh_messages (id, user_id, date, role, message) VALUES (%s, %s, %s, %s, %s)",
//                ['integer', 'integer', 'timestamp', 'text', 'text'],
//                [
//                    $llm_response['id'], $llm_response['user_id'], $llm_response['date'], $llm_response['role'], $llm_response['content']
//                ]
//            );
//        } catch (Exception $e) {
//            self::sendApiResponse(["error" => $e->getMessage()], 500);
//            throw new AIChatException("Error saving message: " . $e->getMessage());
//        }
//    }
//
//    /**
//     * @throws AIChatException
//     */
//    public function delete(): void
//    {
//        global $DIC;
//        $database = $DIC->database();
//        $user_id = $DIC->user()->getId();
//
//        $database->manipulateF(
//            "DELETE FROM xaiuh_messages WHERE user_id = %s",
//            ["integer"],
//            [$user_id]
//        );
//    }
//
//    public function getId(): int
//    {
//        return $this->id;
//    }
//
//    public function setId(int $id): void
//    {
//        $this->id = $id;
//    }
//
//    public function getUserId(): int
//    {
//        return $this->user_id;
//    }
//
//    public function setUserId(int $user_id): void
//    {
//        $this->user_id = $user_id;
//    }
//
//    public function getDate(): ?DateTime
//    {
//        return $this->date;
//    }
//
//    public function setDate(DateTime $date): void
//    {
//        $this->date = $date;
//    }
//
//    public function getMessages(): array
//    {
//        return $this->messages;
//    }
//
//    public function setMessages(Message $messages): void
//    {
//        $this->messages[] = $messages;
//    }
//
//    public function getMaxMessages(): ?int
//    {
//        return $this->max_messages;
//    }
//
//    public function setMaxMessages(?int $max_messages): void
//    {
//        $this->max_messages = $max_messages;
//    }
//
//    public function getRole(): string
//    {
//        return $this->role;
//    }
//
//    public function setRole(string $role): void
//    {
//        $this->role = $role;
//    }
//
//    public function getText(): string
//    {
//        return $this->text;
//    }
//
//    public function setText(string $text): void
//    {
//        $this->text = $text;
//    }
//
//    public function jsonSerialize(): mixed // <--- Implementación del método de la interfaz
//    {
//        // Devuelve un array asociativo con los datos que quieres en el JSON.
//        // Esencialmente, es lo mismo que tu método toArray().
//        return [
//            "id" => $this->getId(),
//            "user_id" => $this->getUserId(),
//            "date" => $this->date ? $this->date->format("Y-m-d H:i:s") : null,
//            "role" => $this->getRole(),
//            "message" => $this->getText(), // Si getText() devuelve un array de objetos Message, y Message es JsonSerializable, funcionará bien.
//            // Si getText() devuelve un string simple, también está bien.
//            // Si quisieras incluir el array $this->messages y sus elementos son objetos Message:
//            // "messages_list" => array_map(function($msg) { return $msg->jsonSerialize(); }, $this->messages), // Ejemplo si Message implementa JsonSerializable
//            // o simplemente:
//            // "messages_list" => $this->messages, // Si los objetos Message ya son JsonSerializable
//        ];
//    }
</file>

<file path="classes/objects/class.UImessage.php">
<?php

use platform\AIChatDatabase;
use platform\AIChatException;

class UImessage extends \objects\Message
{
    public function __construct(?int $id = null, bool $anon = false)
    {
        parent::__construct($id, $anon);
    }

    /**
     * @throws AIChatException
     * @throws Exception
     */
    public function loadFromDB(): void
    {
        $database = new AIChatDatabase();

        $result = $database->select("xaiuh_messages", ["id" => $this->getId()]);

        if (isset($result[0])) {
            $this->setChatId((int)$result[0]["chat_id"]);
            $this->setDate(new DateTime($result[0]["date"]));
            $this->setRole($result[0]["role"]);
            $this->setMessage($result[0]["message"]);
        }
    }
}
</file>

<file path="lang/ilias_en.lang">
obj_xaic#:#AI Chat
objs_xaic#:#AI Chat Objects
xaic_new#:#New AI Chat
objs_xaic_duplicate#:#Duplicate AI Chat Object
xaic_add#:#Add AI Chat
object_content#:#Content
object_settings#:#Settings
object_settings_basic#:#Basic Settings
object_settings_advanced#:#Advanced Settings
object_settings_title#:#Title
object_settings_description#:#Description
object_settings_online#:#Online
object_settings_status#:#Status
object_settings_offline#:#Offline
object_settings_online_info#:#AI Chat will be available to users when this option is enabled.
object_settings_msg_success#:#Settings saved successfully.
chat_default_title#:#New Chat
front_new_chat_button#:#New Chat
front_input_placeholder#:#Type a message...
error_http#:#Error connecting to the API.
error_apikey#:#The API key is invalid.
config_general#:#General
config_openai#:#OpenAI
config_ollama#:#Ollama
config_service_label#:#Service to Use
config_service_info#:#The service that will be used for the chat, remember to save the configuration so you can configure the service below.
config_available_services#:#Available Services
config_prompt_label#:#System Prompt
config_prompt_info#:#This prompt will be used as the initial message in a conversation, it will not be visible to the user, but the model will take it into account.
config_characters_limit_label#:#Character Limit
config_characters_limit_info#:#The maximum number of characters you can send in a single message.
config_max_memory_messages_label#:#Maximum Number of Memory Messages
config_max_memory_messages_info#:#The maximum number of messages that will be stored in the conversation.
config_disclaimer_label#:#Legal Disclaimer
config_disclaimer_info#:#Text that will be displayed in the chat to inform the user that they are talking to an artificial intelligence.
config_openai_models_label#:#Models
config_openai_key_label#:#API Key
config_openai_key_info#:#Your OpenAI API key
config_openai_stream_label#:#Enable Streaming
config_openai_stream_info#:#Enable streaming responses from OpenAI
config_ollama_endpoint_label#:#Server Endpoint
config_ollama_endpoint_info#:#The URL of the Ollama server endpoint
config_ollama_models_label#:#Available Models
config_msg_success#:#Configuration saved successfully.
config_ollama_models_error#:#Error getting available models, please check the server endpoint.
object_no_llm#:#There is no language model selected.
object_offline_info#:#AI Chat is offline.
config_api_section#:#Api configuration
config_general_section#:#General configuration
config_prompt_selection#:#Select a prompt
config_disclaimer_text#:#Disclaimer Text
config_n_memory_messages#:#Number of memory messages
config_characters_limit#:#Character limit
config_prompt_selection_info#:#Prompt information
config_disclaimer_text_info#:#Disclaimer text
config_n_memory_messages_info#:#Number of memory messages
config_gwdg_key_label#:#API Key
config_gwdg_key_info#:#Your GWDG API key
config_gwdg_stream_label#:#Enable Streaming
config_gwdg_stream_info#:#Enable streaming responses from GWDG
config_gwdg_models_label#:#Models
config_gwdg_models_error#:#Error getting available models, please check the api key.
</file>

<file path="plugin.php">
<?php
$id = 'xaiuh';

$version = '1.0.0';

$ilias_min_version = '9.0';
$ilias_max_version = '9.999';

$responsible = 'Jesus Copado';
$responsible_mail = '<EMAIL>';

$supports_export = false;
$supports_cli_setup = true;
</file>

<file path="sql/dbupdate.php">
<#1>
<?php
global $DIC;
$ilDB = $DIC->database();

if(!$ilDB->tableExists('xaiuh_chats'))
{
    $fields = array(
        'id' => array(
            'type' => 'integer',
            'notnull' => true,
            'default' => 0
        ),
        'obj_id' => array(
            'type' => 'integer',
            'notnull' => true,
            'default' => 0
        ),
        'title' => array(
            'type' => 'text',
            'notnull' => true,
            'length' => 255
        ),
        'created_at' => array(
            'type' => 'timestamp',
            'notnull' => true,
        ),
        'user_id' => array(
            'type' => 'integer',
            'notnull' => true,
            'default' => 0
        ),
        'last_update' => array(
            'type' => 'timestamp',
            'notnull' => true,
        ),
    );
    $ilDB->createTable('xaiuh_chats', $fields);
    $ilDB->addPrimaryKey('xaiuh_chats', array('id'));
    $ilDB->createSequence('xaiuh_chats');
}

if(!$ilDB->tableExists('xaiuh_messages'))
{
    $fields = array(
        'id' => array(
            'type' => 'integer',
            'notnull' => true,
            'default' => 0
        ),
        'user_id' => array(
            'type' => 'integer',
            'notnull' => true,
            'default' => 0
        ),
        'date' => array(
            'type' => 'timestamp',
            'notnull' => true,
        ),
        'role' => array(
            'type' => 'text',
            'notnull' => true,
            'length' => 20
        ),
        'message' => array(
            'type' => 'blob',
            'notnull' => true,
        ),
    );
    $ilDB->createTable('xaiuh_messages', $fields);
    $ilDB->addPrimaryKey('xaiuh_messages', array('id'));
    $ilDB->createSequence('xaiuh_messages');
}

if(!$ilDB->tableExists('xaiuh_config'))
{
    $fields = array(
        'name' => array(
            'type' => 'text',
            'notnull' => true,
            'length' => 100
        ),
        'value' => array(
            'type' => 'text',
            'notnull' => true,
        ),
    );
    $ilDB->createTable('xaiuh_config', $fields);
    $ilDB->addPrimaryKey('xaiuh_config', array('name'));
}
?>
</file>

<file path="templates/css/index.css">
*,:before,:after{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}:before,:after{--tw-content: ""}html,:host{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;-o-tab-size:4;tab-size:4;font-family:ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji",Segoe UI Symbol,"Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}body{margin:0;line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,samp,pre{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;font-feature-settings:normal;font-variation-settings:normal;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;letter-spacing:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,input:where([type=button]),input:where([type=reset]),input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dl,dd,h1,h2,h3,h4,h5,h6,hr,figure,p,pre{margin:0}fieldset{margin:0;padding:0}legend{padding:0}ol,ul,menu{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::-moz-placeholder,textarea::-moz-placeholder{opacity:1;color:#9ca3af}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}button,[role=button]{cursor:pointer}:disabled{cursor:default}img,svg,video,canvas,audio,iframe,embed,object{display:block;vertical-align:middle}img,video{max-width:100%;height:auto}[hidden]{display:none}*,:before,:after{--tw-border-spacing-x: 0;--tw-border-spacing-y: 0;--tw-translate-x: 0;--tw-translate-y: 0;--tw-rotate: 0;--tw-skew-x: 0;--tw-skew-y: 0;--tw-scale-x: 1;--tw-scale-y: 1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness: proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width: 0px;--tw-ring-offset-color: #fff;--tw-ring-color: rgb(59 130 246 / .5);--tw-ring-offset-shadow: 0 0 #0000;--tw-ring-shadow: 0 0 #0000;--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::backdrop{--tw-border-spacing-x: 0;--tw-border-spacing-y: 0;--tw-translate-x: 0;--tw-translate-y: 0;--tw-rotate: 0;--tw-skew-x: 0;--tw-skew-y: 0;--tw-scale-x: 1;--tw-scale-y: 1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness: proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width: 0px;--tw-ring-offset-color: #fff;--tw-ring-color: rgb(59 130 246 / .5);--tw-ring-offset-shadow: 0 0 #0000;--tw-ring-shadow: 0 0 #0000;--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }.absolute{position:absolute}.relative{position:relative}.bottom-4{bottom:1rem}.right-3{right:.75rem}.flex{display:flex}.inline-flex{display:inline-flex}.h-5{height:1.25rem}.h-full{height:100%}.w-5{width:1.25rem}.w-full{width:100%}.shrink-0{flex-shrink:0}@keyframes ping{75%,to{transform:scale(2);opacity:0}}.animate-ping{animation:ping 1s cubic-bezier(0,0,.2,1) infinite}@keyframes spin{to{transform:rotate(360deg)}}.animate-spin{animation:spin 1s linear infinite}.flex-col{flex-direction:column}.items-center{align-items:center}.justify-center{justify-content:center}.rounded-full{border-radius:9999px}.bg-\[\#4c6586\]{--tw-bg-opacity: 1;background-color:rgb(76 101 134 / var(--tw-bg-opacity))}.p-1{padding:.25rem}.p-2{padding:.5rem}.text-xs{font-size:.75rem;line-height:1rem}.text-gray-400{--tw-text-opacity: 1;color:rgb(156 163 175 / var(--tw-text-opacity))}.text-neutral-400{--tw-text-opacity: 1;color:rgb(163 163 163 / var(--tw-text-opacity))}.text-neutral-700{--tw-text-opacity: 1;color:rgb(64 64 64 / var(--tw-text-opacity))}.text-white{--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity))}.text-white\/50{color:#ffffff80}.opacity-75{opacity:.75}.filter{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.transition{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,-webkit-backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter,-webkit-backdrop-filter;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.transition-all{transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}code{display:block;border-radius:.5rem;--tw-bg-opacity: 1;background-color:rgb(24 24 27 / var(--tw-bg-opacity));padding:.5rem;--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity))}.aichat-container h1,.aichat-container h2,.aichat-container h3,.aichat-container h4,.aichat-container h5,.aichat-container h6,.aichat-container p{margin:0}.message h1{font-size:1.5rem;line-height:2rem;font-weight:700}.message h2{font-size:1.25rem;line-height:1.75rem;font-weight:700}.message h3{font-size:1.125rem;line-height:1.75rem;font-weight:700}.message h4{font-size:1rem;line-height:1.5rem;font-weight:700}.message h5{font-size:.875rem;line-height:1.25rem;font-weight:700}.message h6{font-size:.75rem;line-height:1rem;font-weight:700}.ilias-button-wrapper{display:flex!important;margin-bottom:10px!important;justify-content:center;align-items:center;font-weight:700!important;gap:5px!important}.aichat-container *::-webkit-scrollbar{width:5px}.aichat-container *::-webkit-scrollbar-track{background:transparent}.aichat-container *::-webkit-scrollbar-thumb{-webkit-transition:.25s ease-in-out all;transition:.25s ease-in-out all;background-color:#c1c1c1;border-radius:20px}.aichat-container *::-webkit-scrollbar-thumb:hover{background-color:#b3b3b3}.hover\:text-\[\#4c6586\]:hover{--tw-text-opacity: 1;color:rgb(76 101 134 / var(--tw-text-opacity))}.hover\:text-red-700:hover{--tw-text-opacity: 1;color:rgb(185 28 28 / var(--tw-text-opacity))}.hover\:underline:hover{text-decoration-line:underline}.disabled\:opacity-50:disabled{opacity:.5}@media (min-width: 768px){.md\:relative{position:relative}}.aichat-container{overflow:hidden;display:flex;height:100%;width:100%;flex-direction:column;gap:.75rem}@media (min-width: 768px){.aichat-container{flex-direction:row;gap:1.5rem}}.chat-container{display:flex;flex-direction:column;height:100%;width:100%;position:relative;overflow:hidden;border-radius:.75rem;--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity));border:1px solid #80808052}.chat-container .loading{display:flex;justify-content:center;align-items:center;height:100%;width:100%}.aichat-container .sidebar{width:260px;height:100%;display:flex;height:-moz-max-content;height:max-content;width:100%;flex-shrink:0;flex-direction:row;justify-content:space-between}@media (min-width: 768px){.aichat-container .sidebar{height:100%;width:260px;flex-direction:column;justify-content:normal}}.aichat-container .chat-list{height:100%;width:100%;flex-shrink:0;gap:.25rem;overflow-y:auto;overflow-x:hidden;display:flex;flex-direction:column}.aichat-container .chats-container{right:0;bottom:-12px;z-index:50;margin:10px;height:94%;width:calc(80% - 20px);--tw-bg-opacity: 1;background-color:rgb(240 240 240 / var(--tw-bg-opacity));padding:10px}@media (min-width: 768px){.aichat-container .chats-container{margin:0;height:calc(100% - 50px);width:100%;background-color:transparent;padding:0}}.aichat-container .toggle-chat-button{display:none!important}@media (max-width: 768px){.aichat-container .chats-container{display:none}.aichat-container .chats-container.active{display:block}.aichat-container .toggle-chat-button{display:flex!important}}.aichat-container .chat-list .chat-item{display:flex;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;user-select:none;border-radius:.5rem;--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity));padding:.25rem .5rem;font-size:.875rem;line-height:1.25rem;transition-property:all;transition-duration:.15s;transition-timing-function:cubic-bezier(.4,0,.2,1)}.aichat-container .chat-list .chat-item:hover{--tw-bg-opacity: 1;background-color:rgb(244 244 245 / var(--tw-bg-opacity))}.aichat-container .chat-list .chat-item.active{--tw-bg-opacity: 1;background-color:rgb(228 228 231 / var(--tw-bg-opacity))}.aichat-container .chat-list .chat-item .chat-message{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;font-size:1.25rem;line-height:1.75rem;font-weight:700}.aichat-container .chat-list .chat-item .chat-timestamp{font-size:1.125rem;line-height:1.75rem;--tw-text-opacity: 1;color:rgb(115 115 115 / var(--tw-text-opacity))}.aichat-container .chat-content{display:flex;width:100%;align-items:center;justify-content:space-between}.aichat-container .chat-list .chat-buttons{display:none}.aichat-container .chat-list .chat-header{width:100%;font-size:1rem;line-height:1.5rem}.aichat-container .chat-list .chat-item:hover .chat-buttons{display:flex;justify-content:center;align-items:center}.aichat-container .chat-list .chat-item:hover .chat-header{max-width:90%}.aichat-container .top-container{flex:1;overflow-y:auto;height:80%;flex-shrink:0}.aichat-container .top-container .messages{display:flex;flex-direction:column;gap:1.5rem;overflow-y:auto;height:100%;overflow-x:hidden;width:100%}@media (min-width: 640px){.aichat-container .top-container .messages{max-width:640px}}@media (min-width: 768px){.aichat-container .top-container .messages{max-width:768px}}@media (min-width: 1024px){.aichat-container .top-container .messages{max-width:1024px}}@media (min-width: 1280px){.aichat-container .top-container .messages{max-width:1280px}}@media (min-width: 1536px){.aichat-container .top-container .messages{max-width:1536px}}.aichat-container .top-container .messages{margin-left:auto;margin-right:auto;max-width:85%;flex-shrink:0;padding-top:2.5rem}@media (min-width: 1024px){.aichat-container .top-container .messages{max-width:90%}}.aichat-container .top-container .messages .message{display:flex;gap:1rem}.aichat-container .top-container .messages .message .avatar{width:32px;height:32px;border-radius:50%}.aichat-container .top-container .messages .message .avatar img{width:100%;height:100%;-o-object-fit:cover;object-fit:cover;border-radius:50%}.aichat-container .top-container .messages .message.user{display:flex;justify-content:flex-end;gap:.5rem}.aichat-container .top-container .messages .message .message-content{display:flex;max-width:85%;flex-direction:column;justify-content:center;gap:.75rem}.aichat-container .top-container .messages .message.user .message-content{width:-moz-max-content;width:max-content;border-radius:1.5rem;--tw-bg-opacity: 1;background-color:rgb(245 245 245 / var(--tw-bg-opacity));padding:.75rem 1.25rem;max-width:70%;flex-shrink:0;height:-moz-max-content;height:max-content}.aichat-container .top-container .messages .message.assistant .message-content{--tw-text-opacity: 1;color:rgb(39 39 42 / var(--tw-text-opacity))}.aichat-container .bottom-container{display:flex;justify-content:space-between;align-items:center;padding-bottom:.7rem;gap:.7rem;flex-direction:column;width:100%}@media (min-width: 640px){.aichat-container .bottom-container{max-width:640px}}@media (min-width: 768px){.aichat-container .bottom-container{max-width:768px}}@media (min-width: 1024px){.aichat-container .bottom-container{max-width:1024px}}@media (min-width: 1280px){.aichat-container .bottom-container{max-width:1280px}}@media (min-width: 1536px){.aichat-container .bottom-container{max-width:1536px}}.aichat-container .bottom-container{margin-left:auto;margin-right:auto;flex-shrink:0;padding-top:0}.aichat-container .bottom-container .input-container{display:flex;width:100%;position:relative;max-width:85%;align-items:center;gap:.5rem}@media (min-width: 1024px){.aichat-container .bottom-container .input-container{max-width:90%}}.aichat-container .bottom-container .input-container .input{width:100%;resize:none;border-radius:25px;--tw-bg-opacity: 1;background-color:rgb(244 244 245 / var(--tw-bg-opacity));padding:1.5rem;padding-inline-end:3.5rem;--tw-text-opacity: 1;color:rgb(38 38 38 / var(--tw-text-opacity));outline:2px solid transparent;outline-offset:2px;transition-property:all;transition-duration:.15s;transition-timing-function:cubic-bezier(.4,0,.2,1)}.aichat-container .bottom-container .input-container .input:hover{background-color:#e4e4e7b3}.aichat-container .bottom-container .input-container .input::-webkit-scrollbar{display:none}.aichat-container .bottom-container .input-container .input:focus{--tw-bg-opacity: 1;background-color:rgb(228 228 231 / var(--tw-bg-opacity))}.error-screen{display:flex;justify-content:center;align-items:center;height:100%;width:100%;position:absolute;top:0;left:0;background-color:#000000c2;z-index:99;padding:2.5rem;--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity));--tw-backdrop-blur: blur(4px);-webkit-backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia)}.error-screen .error-content{display:flex;align-items:center;gap:1rem}.error-screen .error-title{font-size:1.5rem;line-height:2rem;font-weight:700}.aichat-container .disclaimer{display:flex;justify-content:center;position:absolute;bottom:20%;left:50%;transform:translate(-50%,-50%);width:100%;padding-left:8rem;padding-right:8rem;text-align:center;font-size:1rem;line-height:1.5rem;color:#00000080}@media (min-width: 768px){.aichat-container .disclaimer{padding-left:1rem;padding-right:1rem}}pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}/*!
  Theme: GitHub Dark
  Description: Dark theme as seen on github.com
  Author: github.com
  Maintainer: @Hirse
  Updated: 2021-05-15

  Outdated base version: https://github.com/primer/github-syntax-dark
  Current colors taken from GitHub's CSS
*/.hljs{color:#c9d1d9;background:#0d1117}.hljs-doctag,.hljs-keyword,.hljs-meta .hljs-keyword,.hljs-template-tag,.hljs-template-variable,.hljs-type,.hljs-variable.language_{color:#ff7b72}.hljs-title,.hljs-title.class_,.hljs-title.class_.inherited__,.hljs-title.function_{color:#d2a8ff}.hljs-attr,.hljs-attribute,.hljs-literal,.hljs-meta,.hljs-number,.hljs-operator,.hljs-variable,.hljs-selector-attr,.hljs-selector-class,.hljs-selector-id{color:#79c0ff}.hljs-regexp,.hljs-string,.hljs-meta .hljs-string{color:#a5d6ff}.hljs-built_in,.hljs-symbol{color:#ffa657}.hljs-comment,.hljs-code,.hljs-formula{color:#8b949e}.hljs-name,.hljs-quote,.hljs-selector-tag,.hljs-selector-pseudo{color:#7ee787}.hljs-subst{color:#c9d1d9}.hljs-section{color:#1f6feb;font-weight:700}.hljs-bullet{color:#f2cc60}.hljs-emphasis{color:#c9d1d9;font-style:italic}.hljs-strong{color:#c9d1d9;font-weight:700}.hljs-addition{color:#aff5b4;background-color:#033a16}.hljs-deletion{color:#ffdcd7;background-color:#67060c}
</file>

<file path="templates/css/styles.css">
.principalButton {
    position: fixed;
    bottom: 30px;
    right: 40px;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: #fdfdfd;
    color: white;
    border: none;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    font-size: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.principalButton:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 15px rgba(0,0,0,0.3);
}

.principalButton:active {
    transform: scale(0.95);
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

.principalButton img {
    width: 75%;
    height: 75%;
    object-fit: contain;
}

.floatWindow {
    position: fixed;
    bottom: 100px;
    right: 40px;
    width: 30dvw;
    height: 55dvh;
    background-color: #fff;
    border-radius: 12px;
    margin-bottom: 7px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    z-index: 10000;
    /* display: none; */
    visibility: hidden;
    transform: scale(0.9) translateY(73px);
    opacity: 0;
    transition: 0.15s ease-in-out all;
    transform-origin: right;
}

.active {
    visibility: visible;
    opacity: 1;
    transform: scale(1) translateY(0);
    }

@media (width <= 990px) {
    .principalButton {
        bottom: 95px;
    }
}

@media (width <= 800px) {
    .floatWindow {
        width: 50dvw;
    }
}

@media (width <= 400px) {
    .floatWindow {
        width: 80dvw;
    }
}
</file>

<file path="templates/js/index.js">
setTimeout(function () {
    (function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const s of o.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(i){if(i.ep)return;i.ep=!0;const o=n(i);fetch(i.href,o)}})();var If=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function cm(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var fm={exports:{}},Ns={},hm={exports:{}},G={};/**
     * @license React
     * react.production.min.js
     *
     * Copyright (c) Facebook, Inc. and its affiliates.
     *
     * This source code is licensed under the MIT license found in the
     * LICENSE file in the root directory of this source tree.
     */var Zi=Symbol.for("react.element"),g1=Symbol.for("react.portal"),y1=Symbol.for("react.fragment"),v1=Symbol.for("react.strict_mode"),x1=Symbol.for("react.profiler"),w1=Symbol.for("react.provider"),k1=Symbol.for("react.context"),S1=Symbol.for("react.forward_ref"),C1=Symbol.for("react.suspense"),E1=Symbol.for("react.memo"),T1=Symbol.for("react.lazy"),_f=Symbol.iterator;function P1(e){return e===null||typeof e!="object"?null:(e=_f&&e[_f]||e["@@iterator"],typeof e=="function"?e:null)}var dm={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},pm=Object.assign,mm={};function Br(e,t,n){this.props=e,this.context=t,this.refs=mm,this.updater=n||dm}Br.prototype.isReactComponent={};Br.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Br.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function gm(){}gm.prototype=Br.prototype;function _u(e,t,n){this.props=e,this.context=t,this.refs=mm,this.updater=n||dm}var Ou=_u.prototype=new gm;Ou.constructor=_u;pm(Ou,Br.prototype);Ou.isPureReactComponent=!0;var Of=Array.isArray,ym=Object.prototype.hasOwnProperty,zu={current:null},vm={key:!0,ref:!0,__self:!0,__source:!0};function xm(e,t,n){var r,i={},o=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(o=""+t.key),t)ym.call(t,r)&&!vm.hasOwnProperty(r)&&(i[r]=t[r]);var l=arguments.length-2;if(l===1)i.children=n;else if(1<l){for(var a=Array(l),u=0;u<l;u++)a[u]=arguments[u+2];i.children=a}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)i[r]===void 0&&(i[r]=l[r]);return{$$typeof:Zi,type:e,key:o,ref:s,props:i,_owner:zu.current}}function A1(e,t){return{$$typeof:Zi,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Fu(e){return typeof e=="object"&&e!==null&&e.$$typeof===Zi}function D1(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var zf=/\/+/g;function fl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?D1(""+e.key):t.toString(36)}function Fo(e,t,n,r,i){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(o){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case Zi:case g1:s=!0}}if(s)return s=e,i=i(s),e=r===""?"."+fl(s,0):r,Of(i)?(n="",e!=null&&(n=e.replace(zf,"$&/")+"/"),Fo(i,t,n,"",function(u){return u})):i!=null&&(Fu(i)&&(i=A1(i,n+(!i.key||s&&s.key===i.key?"":(""+i.key).replace(zf,"$&/")+"/")+e)),t.push(i)),1;if(s=0,r=r===""?".":r+":",Of(e))for(var l=0;l<e.length;l++){o=e[l];var a=r+fl(o,l);s+=Fo(o,t,n,a,i)}else if(a=P1(e),typeof a=="function")for(e=a.call(e),l=0;!(o=e.next()).done;)o=o.value,a=r+fl(o,l++),s+=Fo(o,t,n,a,i);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function fo(e,t,n){if(e==null)return e;var r=[],i=0;return Fo(e,r,"","",function(o){return t.call(n,o,i++)}),r}function L1(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var $e={current:null},No={transition:null},b1={ReactCurrentDispatcher:$e,ReactCurrentBatchConfig:No,ReactCurrentOwner:zu};function wm(){throw Error("act(...) is not supported in production builds of React.")}G.Children={map:fo,forEach:function(e,t,n){fo(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return fo(e,function(){t++}),t},toArray:function(e){return fo(e,function(t){return t})||[]},only:function(e){if(!Fu(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};G.Component=Br;G.Fragment=y1;G.Profiler=x1;G.PureComponent=_u;G.StrictMode=v1;G.Suspense=C1;G.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=b1;G.act=wm;G.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=pm({},e.props),i=e.key,o=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,s=zu.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(a in t)ym.call(t,a)&&!vm.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&l!==void 0?l[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){l=Array(a);for(var u=0;u<a;u++)l[u]=arguments[u+2];r.children=l}return{$$typeof:Zi,type:e.type,key:i,ref:o,props:r,_owner:s}};G.createContext=function(e){return e={$$typeof:k1,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:w1,_context:e},e.Consumer=e};G.createElement=xm;G.createFactory=function(e){var t=xm.bind(null,e);return t.type=e,t};G.createRef=function(){return{current:null}};G.forwardRef=function(e){return{$$typeof:S1,render:e}};G.isValidElement=Fu;G.lazy=function(e){return{$$typeof:T1,_payload:{_status:-1,_result:e},_init:L1}};G.memo=function(e,t){return{$$typeof:E1,type:e,compare:t===void 0?null:t}};G.startTransition=function(e){var t=No.transition;No.transition={};try{e()}finally{No.transition=t}};G.unstable_act=wm;G.useCallback=function(e,t){return $e.current.useCallback(e,t)};G.useContext=function(e){return $e.current.useContext(e)};G.useDebugValue=function(){};G.useDeferredValue=function(e){return $e.current.useDeferredValue(e)};G.useEffect=function(e,t){return $e.current.useEffect(e,t)};G.useId=function(){return $e.current.useId()};G.useImperativeHandle=function(e,t,n){return $e.current.useImperativeHandle(e,t,n)};G.useInsertionEffect=function(e,t){return $e.current.useInsertionEffect(e,t)};G.useLayoutEffect=function(e,t){return $e.current.useLayoutEffect(e,t)};G.useMemo=function(e,t){return $e.current.useMemo(e,t)};G.useReducer=function(e,t,n){return $e.current.useReducer(e,t,n)};G.useRef=function(e){return $e.current.useRef(e)};G.useState=function(e){return $e.current.useState(e)};G.useSyncExternalStore=function(e,t,n){return $e.current.useSyncExternalStore(e,t,n)};G.useTransition=function(){return $e.current.useTransition()};G.version="18.3.1";hm.exports=G;var b=hm.exports;const hn=cm(b);/**
     * @license React
     * react-jsx-runtime.production.min.js
     *
     * Copyright (c) Facebook, Inc. and its affiliates.
     *
     * This source code is licensed under the MIT license found in the
     * LICENSE file in the root directory of this source tree.
     */var R1=b,M1=Symbol.for("react.element"),I1=Symbol.for("react.fragment"),_1=Object.prototype.hasOwnProperty,O1=R1.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,z1={key:!0,ref:!0,__self:!0,__source:!0};function km(e,t,n){var r,i={},o=null,s=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)_1.call(t,r)&&!z1.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:M1,type:e,key:o,ref:s,props:i,_owner:O1.current}}Ns.Fragment=I1;Ns.jsx=km;Ns.jsxs=km;fm.exports=Ns;var V=fm.exports,da={},Sm={exports:{}},ot={},Cm={exports:{}},Em={};/**
     * @license React
     * scheduler.production.min.js
     *
     * Copyright (c) Facebook, Inc. and its affiliates.
     *
     * This source code is licensed under the MIT license found in the
     * LICENSE file in the root directory of this source tree.
     */(function(e){function t(L,z){var x=L.length;L.push(z);e:for(;0<x;){var W=x-1>>>1,ee=L[W];if(0<i(ee,z))L[W]=z,L[x]=ee,x=W;else break e}}function n(L){return L.length===0?null:L[0]}function r(L){if(L.length===0)return null;var z=L[0],x=L.pop();if(x!==z){L[0]=x;e:for(var W=0,ee=L.length,S=ee>>>1;W<S;){var fe=2*(W+1)-1,vt=L[fe],se=fe+1,Lt=L[se];if(0>i(vt,x))se<ee&&0>i(Lt,vt)?(L[W]=Lt,L[se]=x,W=se):(L[W]=vt,L[fe]=x,W=fe);else if(se<ee&&0>i(Lt,x))L[W]=Lt,L[se]=x,W=se;else break e}}return z}function i(L,z){var x=L.sortIndex-z.sortIndex;return x!==0?x:L.id-z.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var s=Date,l=s.now();e.unstable_now=function(){return s.now()-l}}var a=[],u=[],c=1,f=null,h=3,d=!1,g=!1,v=!1,w=typeof setTimeout=="function"?setTimeout:null,p=typeof clearTimeout=="function"?clearTimeout:null,m=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function y(L){for(var z=n(u);z!==null;){if(z.callback===null)r(u);else if(z.startTime<=L)r(u),z.sortIndex=z.expirationTime,t(a,z);else break;z=n(u)}}function C(L){if(v=!1,y(L),!g)if(n(a)!==null)g=!0,ge(T);else{var z=n(u);z!==null&&F(C,z.startTime-L)}}function T(L,z){g=!1,v&&(v=!1,p(A),A=-1),d=!0;var x=h;try{for(y(z),f=n(a);f!==null&&(!(f.expirationTime>z)||L&&!_());){var W=f.callback;if(typeof W=="function"){f.callback=null,h=f.priorityLevel;var ee=W(f.expirationTime<=z);z=e.unstable_now(),typeof ee=="function"?f.callback=ee:f===n(a)&&r(a),y(z)}else r(a);f=n(a)}if(f!==null)var S=!0;else{var fe=n(u);fe!==null&&F(C,fe.startTime-z),S=!1}return S}finally{f=null,h=x,d=!1}}var k=!1,P=null,A=-1,O=5,E=-1;function _(){return!(e.unstable_now()-E<O)}function N(){if(P!==null){var L=e.unstable_now();E=L;var z=!0;try{z=P(!0,L)}finally{z?Y():(k=!1,P=null)}}else k=!1}var Y;if(typeof m=="function")Y=function(){m(N)};else if(typeof MessageChannel<"u"){var J=new MessageChannel,K=J.port2;J.port1.onmessage=N,Y=function(){K.postMessage(null)}}else Y=function(){w(N,0)};function ge(L){P=L,k||(k=!0,Y())}function F(L,z){A=w(function(){L(e.unstable_now())},z)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(L){L.callback=null},e.unstable_continueExecution=function(){g||d||(g=!0,ge(T))},e.unstable_forceFrameRate=function(L){0>L||125<L?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):O=0<L?Math.floor(1e3/L):5},e.unstable_getCurrentPriorityLevel=function(){return h},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(L){switch(h){case 1:case 2:case 3:var z=3;break;default:z=h}var x=h;h=z;try{return L()}finally{h=x}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(L,z){switch(L){case 1:case 2:case 3:case 4:case 5:break;default:L=3}var x=h;h=L;try{return z()}finally{h=x}},e.unstable_scheduleCallback=function(L,z,x){var W=e.unstable_now();switch(typeof x=="object"&&x!==null?(x=x.delay,x=typeof x=="number"&&0<x?W+x:W):x=W,L){case 1:var ee=-1;break;case 2:ee=250;break;case 5:ee=**********;break;case 4:ee=1e4;break;default:ee=5e3}return ee=x+ee,L={id:c++,callback:z,priorityLevel:L,startTime:x,expirationTime:ee,sortIndex:-1},x>W?(L.sortIndex=x,t(u,L),n(a)===null&&L===n(u)&&(v?(p(A),A=-1):v=!0,F(C,x-W))):(L.sortIndex=ee,t(a,L),g||d||(g=!0,ge(T))),L},e.unstable_shouldYield=_,e.unstable_wrapCallback=function(L){var z=h;return function(){var x=h;h=z;try{return L.apply(this,arguments)}finally{h=x}}}})(Em);Cm.exports=Em;var F1=Cm.exports;/**
     * @license React
     * react-dom.production.min.js
     *
     * Copyright (c) Facebook, Inc. and its affiliates.
     *
     * This source code is licensed under the MIT license found in the
     * LICENSE file in the root directory of this source tree.
     */var N1=b,rt=F1;function R(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Tm=new Set,Li={};function Xn(e,t){Rr(e,t),Rr(e+"Capture",t)}function Rr(e,t){for(Li[e]=t,e=0;e<t.length;e++)Tm.add(t[e])}var Gt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),pa=Object.prototype.hasOwnProperty,j1=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Ff={},Nf={};function V1(e){return pa.call(Nf,e)?!0:pa.call(Ff,e)?!1:j1.test(e)?Nf[e]=!0:(Ff[e]=!0,!1)}function B1(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function U1(e,t,n,r){if(t===null||typeof t>"u"||B1(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function He(e,t,n,r,i,o,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=s}var be={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){be[e]=new He(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];be[t]=new He(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){be[e]=new He(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){be[e]=new He(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){be[e]=new He(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){be[e]=new He(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){be[e]=new He(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){be[e]=new He(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){be[e]=new He(e,5,!1,e.toLowerCase(),null,!1,!1)});var Nu=/[\-:]([a-z])/g;function ju(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Nu,ju);be[t]=new He(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Nu,ju);be[t]=new He(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Nu,ju);be[t]=new He(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){be[e]=new He(e,1,!1,e.toLowerCase(),null,!1,!1)});be.xlinkHref=new He("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){be[e]=new He(e,1,!1,e.toLowerCase(),null,!0,!0)});function Vu(e,t,n,r){var i=be.hasOwnProperty(t)?be[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(U1(t,n,i,r)&&(n=null),r||i===null?V1(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Jt=N1.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ho=Symbol.for("react.element"),lr=Symbol.for("react.portal"),ar=Symbol.for("react.fragment"),Bu=Symbol.for("react.strict_mode"),ma=Symbol.for("react.profiler"),Pm=Symbol.for("react.provider"),Am=Symbol.for("react.context"),Uu=Symbol.for("react.forward_ref"),ga=Symbol.for("react.suspense"),ya=Symbol.for("react.suspense_list"),$u=Symbol.for("react.memo"),sn=Symbol.for("react.lazy"),Dm=Symbol.for("react.offscreen"),jf=Symbol.iterator;function qr(e){return e===null||typeof e!="object"?null:(e=jf&&e[jf]||e["@@iterator"],typeof e=="function"?e:null)}var me=Object.assign,hl;function li(e){if(hl===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);hl=t&&t[1]||""}return`
`+hl+e}var dl=!1;function pl(e,t){if(!e||dl)return"";dl=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),o=r.stack.split(`
`),s=i.length-1,l=o.length-1;1<=s&&0<=l&&i[s]!==o[l];)l--;for(;1<=s&&0<=l;s--,l--)if(i[s]!==o[l]){if(s!==1||l!==1)do if(s--,l--,0>l||i[s]!==o[l]){var a=`
`+i[s].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=s&&0<=l);break}}}finally{dl=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?li(e):""}function $1(e){switch(e.tag){case 5:return li(e.type);case 16:return li("Lazy");case 13:return li("Suspense");case 19:return li("SuspenseList");case 0:case 2:case 15:return e=pl(e.type,!1),e;case 11:return e=pl(e.type.render,!1),e;case 1:return e=pl(e.type,!0),e;default:return""}}function va(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case ar:return"Fragment";case lr:return"Portal";case ma:return"Profiler";case Bu:return"StrictMode";case ga:return"Suspense";case ya:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Am:return(e.displayName||"Context")+".Consumer";case Pm:return(e._context.displayName||"Context")+".Provider";case Uu:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case $u:return t=e.displayName||null,t!==null?t:va(e.type)||"Memo";case sn:t=e._payload,e=e._init;try{return va(e(t))}catch{}}return null}function H1(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return va(t);case 8:return t===Bu?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function kn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Lm(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function W1(e){var t=Lm(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(s){r=""+s,o.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function po(e){e._valueTracker||(e._valueTracker=W1(e))}function bm(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Lm(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function es(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function xa(e,t){var n=t.checked;return me({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Vf(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=kn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Rm(e,t){t=t.checked,t!=null&&Vu(e,"checked",t,!1)}function wa(e,t){Rm(e,t);var n=kn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?ka(e,t.type,n):t.hasOwnProperty("defaultValue")&&ka(e,t.type,kn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Bf(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function ka(e,t,n){(t!=="number"||es(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var ai=Array.isArray;function Er(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+kn(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function Sa(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(R(91));return me({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Uf(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(R(92));if(ai(n)){if(1<n.length)throw Error(R(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:kn(n)}}function Mm(e,t){var n=kn(t.value),r=kn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function $f(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Im(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Ca(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Im(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var mo,_m=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(mo=mo||document.createElement("div"),mo.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=mo.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function bi(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var pi={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},K1=["Webkit","ms","Moz","O"];Object.keys(pi).forEach(function(e){K1.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),pi[t]=pi[e]})});function Om(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||pi.hasOwnProperty(e)&&pi[e]?(""+t).trim():t+"px"}function zm(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=Om(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var Q1=me({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Ea(e,t){if(t){if(Q1[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(R(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(R(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(R(61))}if(t.style!=null&&typeof t.style!="object")throw Error(R(62))}}function Ta(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Pa=null;function Hu(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Aa=null,Tr=null,Pr=null;function Hf(e){if(e=to(e)){if(typeof Aa!="function")throw Error(R(280));var t=e.stateNode;t&&(t=$s(t),Aa(e.stateNode,e.type,t))}}function Fm(e){Tr?Pr?Pr.push(e):Pr=[e]:Tr=e}function Nm(){if(Tr){var e=Tr,t=Pr;if(Pr=Tr=null,Hf(e),t)for(e=0;e<t.length;e++)Hf(t[e])}}function jm(e,t){return e(t)}function Vm(){}var ml=!1;function Bm(e,t,n){if(ml)return e(t,n);ml=!0;try{return jm(e,t,n)}finally{ml=!1,(Tr!==null||Pr!==null)&&(Vm(),Nm())}}function Ri(e,t){var n=e.stateNode;if(n===null)return null;var r=$s(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(R(231,t,typeof n));return n}var Da=!1;if(Gt)try{var Xr={};Object.defineProperty(Xr,"passive",{get:function(){Da=!0}}),window.addEventListener("test",Xr,Xr),window.removeEventListener("test",Xr,Xr)}catch{Da=!1}function G1(e,t,n,r,i,o,s,l,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var mi=!1,ts=null,ns=!1,La=null,Y1={onError:function(e){mi=!0,ts=e}};function q1(e,t,n,r,i,o,s,l,a){mi=!1,ts=null,G1.apply(Y1,arguments)}function X1(e,t,n,r,i,o,s,l,a){if(q1.apply(this,arguments),mi){if(mi){var u=ts;mi=!1,ts=null}else throw Error(R(198));ns||(ns=!0,La=u)}}function Zn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Um(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Wf(e){if(Zn(e)!==e)throw Error(R(188))}function Z1(e){var t=e.alternate;if(!t){if(t=Zn(e),t===null)throw Error(R(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var o=i.alternate;if(o===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===o.child){for(o=i.child;o;){if(o===n)return Wf(i),e;if(o===r)return Wf(i),t;o=o.sibling}throw Error(R(188))}if(n.return!==r.return)n=i,r=o;else{for(var s=!1,l=i.child;l;){if(l===n){s=!0,n=i,r=o;break}if(l===r){s=!0,r=i,n=o;break}l=l.sibling}if(!s){for(l=o.child;l;){if(l===n){s=!0,n=o,r=i;break}if(l===r){s=!0,r=o,n=i;break}l=l.sibling}if(!s)throw Error(R(189))}}if(n.alternate!==r)throw Error(R(190))}if(n.tag!==3)throw Error(R(188));return n.stateNode.current===n?e:t}function $m(e){return e=Z1(e),e!==null?Hm(e):null}function Hm(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Hm(e);if(t!==null)return t;e=e.sibling}return null}var Wm=rt.unstable_scheduleCallback,Kf=rt.unstable_cancelCallback,J1=rt.unstable_shouldYield,ex=rt.unstable_requestPaint,we=rt.unstable_now,tx=rt.unstable_getCurrentPriorityLevel,Wu=rt.unstable_ImmediatePriority,Km=rt.unstable_UserBlockingPriority,rs=rt.unstable_NormalPriority,nx=rt.unstable_LowPriority,Qm=rt.unstable_IdlePriority,js=null,_t=null;function rx(e){if(_t&&typeof _t.onCommitFiberRoot=="function")try{_t.onCommitFiberRoot(js,e,void 0,(e.current.flags&128)===128)}catch{}}var Tt=Math.clz32?Math.clz32:sx,ix=Math.log,ox=Math.LN2;function sx(e){return e>>>=0,e===0?32:31-(ix(e)/ox|0)|0}var go=64,yo=4194304;function ui(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function is(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,o=e.pingedLanes,s=n&268435455;if(s!==0){var l=s&~i;l!==0?r=ui(l):(o&=s,o!==0&&(r=ui(o)))}else s=n&~i,s!==0?r=ui(s):o!==0&&(r=ui(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,o=t&-t,i>=o||i===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Tt(t),i=1<<n,r|=e[n],t&=~i;return r}function lx(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function ax(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes;0<o;){var s=31-Tt(o),l=1<<s,a=i[s];a===-1?(!(l&n)||l&r)&&(i[s]=lx(l,t)):a<=t&&(e.expiredLanes|=l),o&=~l}}function ba(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Gm(){var e=go;return go<<=1,!(go&4194240)&&(go=64),e}function gl(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ji(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Tt(t),e[t]=n}function ux(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-Tt(n),o=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~o}}function Ku(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Tt(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var te=0;function Ym(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var qm,Qu,Xm,Zm,Jm,Ra=!1,vo=[],dn=null,pn=null,mn=null,Mi=new Map,Ii=new Map,an=[],cx="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Qf(e,t){switch(e){case"focusin":case"focusout":dn=null;break;case"dragenter":case"dragleave":pn=null;break;case"mouseover":case"mouseout":mn=null;break;case"pointerover":case"pointerout":Mi.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Ii.delete(t.pointerId)}}function Zr(e,t,n,r,i,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[i]},t!==null&&(t=to(t),t!==null&&Qu(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function fx(e,t,n,r,i){switch(t){case"focusin":return dn=Zr(dn,e,t,n,r,i),!0;case"dragenter":return pn=Zr(pn,e,t,n,r,i),!0;case"mouseover":return mn=Zr(mn,e,t,n,r,i),!0;case"pointerover":var o=i.pointerId;return Mi.set(o,Zr(Mi.get(o)||null,e,t,n,r,i)),!0;case"gotpointercapture":return o=i.pointerId,Ii.set(o,Zr(Ii.get(o)||null,e,t,n,r,i)),!0}return!1}function eg(e){var t=Nn(e.target);if(t!==null){var n=Zn(t);if(n!==null){if(t=n.tag,t===13){if(t=Um(n),t!==null){e.blockedOn=t,Jm(e.priority,function(){Xm(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function jo(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Ma(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Pa=r,n.target.dispatchEvent(r),Pa=null}else return t=to(n),t!==null&&Qu(t),e.blockedOn=n,!1;t.shift()}return!0}function Gf(e,t,n){jo(e)&&n.delete(t)}function hx(){Ra=!1,dn!==null&&jo(dn)&&(dn=null),pn!==null&&jo(pn)&&(pn=null),mn!==null&&jo(mn)&&(mn=null),Mi.forEach(Gf),Ii.forEach(Gf)}function Jr(e,t){e.blockedOn===t&&(e.blockedOn=null,Ra||(Ra=!0,rt.unstable_scheduleCallback(rt.unstable_NormalPriority,hx)))}function _i(e){function t(i){return Jr(i,e)}if(0<vo.length){Jr(vo[0],e);for(var n=1;n<vo.length;n++){var r=vo[n];r.blockedOn===e&&(r.blockedOn=null)}}for(dn!==null&&Jr(dn,e),pn!==null&&Jr(pn,e),mn!==null&&Jr(mn,e),Mi.forEach(t),Ii.forEach(t),n=0;n<an.length;n++)r=an[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<an.length&&(n=an[0],n.blockedOn===null);)eg(n),n.blockedOn===null&&an.shift()}var Ar=Jt.ReactCurrentBatchConfig,os=!0;function dx(e,t,n,r){var i=te,o=Ar.transition;Ar.transition=null;try{te=1,Gu(e,t,n,r)}finally{te=i,Ar.transition=o}}function px(e,t,n,r){var i=te,o=Ar.transition;Ar.transition=null;try{te=4,Gu(e,t,n,r)}finally{te=i,Ar.transition=o}}function Gu(e,t,n,r){if(os){var i=Ma(e,t,n,r);if(i===null)Pl(e,t,r,ss,n),Qf(e,r);else if(fx(i,e,t,n,r))r.stopPropagation();else if(Qf(e,r),t&4&&-1<cx.indexOf(e)){for(;i!==null;){var o=to(i);if(o!==null&&qm(o),o=Ma(e,t,n,r),o===null&&Pl(e,t,r,ss,n),o===i)break;i=o}i!==null&&r.stopPropagation()}else Pl(e,t,r,null,n)}}var ss=null;function Ma(e,t,n,r){if(ss=null,e=Hu(r),e=Nn(e),e!==null)if(t=Zn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Um(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ss=e,null}function tg(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(tx()){case Wu:return 1;case Km:return 4;case rs:case nx:return 16;case Qm:return 536870912;default:return 16}default:return 16}}var cn=null,Yu=null,Vo=null;function ng(){if(Vo)return Vo;var e,t=Yu,n=t.length,r,i="value"in cn?cn.value:cn.textContent,o=i.length;for(e=0;e<n&&t[e]===i[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===i[o-r];r++);return Vo=i.slice(e,1<r?1-r:void 0)}function Bo(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function xo(){return!0}function Yf(){return!1}function st(e){function t(n,r,i,o,s){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=o,this.target=s,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(o):o[l]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?xo:Yf,this.isPropagationStopped=Yf,this}return me(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=xo)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=xo)},persist:function(){},isPersistent:xo}),t}var Ur={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},qu=st(Ur),eo=me({},Ur,{view:0,detail:0}),mx=st(eo),yl,vl,ei,Vs=me({},eo,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Xu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ei&&(ei&&e.type==="mousemove"?(yl=e.screenX-ei.screenX,vl=e.screenY-ei.screenY):vl=yl=0,ei=e),yl)},movementY:function(e){return"movementY"in e?e.movementY:vl}}),qf=st(Vs),gx=me({},Vs,{dataTransfer:0}),yx=st(gx),vx=me({},eo,{relatedTarget:0}),xl=st(vx),xx=me({},Ur,{animationName:0,elapsedTime:0,pseudoElement:0}),wx=st(xx),kx=me({},Ur,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Sx=st(kx),Cx=me({},Ur,{data:0}),Xf=st(Cx),Ex={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Tx={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Px={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Ax(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Px[e])?!!t[e]:!1}function Xu(){return Ax}var Dx=me({},eo,{key:function(e){if(e.key){var t=Ex[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Bo(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Tx[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Xu,charCode:function(e){return e.type==="keypress"?Bo(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Bo(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Lx=st(Dx),bx=me({},Vs,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Zf=st(bx),Rx=me({},eo,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Xu}),Mx=st(Rx),Ix=me({},Ur,{propertyName:0,elapsedTime:0,pseudoElement:0}),_x=st(Ix),Ox=me({},Vs,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),zx=st(Ox),Fx=[9,13,27,32],Zu=Gt&&"CompositionEvent"in window,gi=null;Gt&&"documentMode"in document&&(gi=document.documentMode);var Nx=Gt&&"TextEvent"in window&&!gi,rg=Gt&&(!Zu||gi&&8<gi&&11>=gi),Jf=" ",eh=!1;function ig(e,t){switch(e){case"keyup":return Fx.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function og(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var ur=!1;function jx(e,t){switch(e){case"compositionend":return og(t);case"keypress":return t.which!==32?null:(eh=!0,Jf);case"textInput":return e=t.data,e===Jf&&eh?null:e;default:return null}}function Vx(e,t){if(ur)return e==="compositionend"||!Zu&&ig(e,t)?(e=ng(),Vo=Yu=cn=null,ur=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return rg&&t.locale!=="ko"?null:t.data;default:return null}}var Bx={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function th(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Bx[e.type]:t==="textarea"}function sg(e,t,n,r){Fm(r),t=ls(t,"onChange"),0<t.length&&(n=new qu("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var yi=null,Oi=null;function Ux(e){yg(e,0)}function Bs(e){var t=hr(e);if(bm(t))return e}function $x(e,t){if(e==="change")return t}var lg=!1;if(Gt){var wl;if(Gt){var kl="oninput"in document;if(!kl){var nh=document.createElement("div");nh.setAttribute("oninput","return;"),kl=typeof nh.oninput=="function"}wl=kl}else wl=!1;lg=wl&&(!document.documentMode||9<document.documentMode)}function rh(){yi&&(yi.detachEvent("onpropertychange",ag),Oi=yi=null)}function ag(e){if(e.propertyName==="value"&&Bs(Oi)){var t=[];sg(t,Oi,e,Hu(e)),Bm(Ux,t)}}function Hx(e,t,n){e==="focusin"?(rh(),yi=t,Oi=n,yi.attachEvent("onpropertychange",ag)):e==="focusout"&&rh()}function Wx(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Bs(Oi)}function Kx(e,t){if(e==="click")return Bs(t)}function Qx(e,t){if(e==="input"||e==="change")return Bs(t)}function Gx(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Dt=typeof Object.is=="function"?Object.is:Gx;function zi(e,t){if(Dt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!pa.call(t,i)||!Dt(e[i],t[i]))return!1}return!0}function ih(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function oh(e,t){var n=ih(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=ih(n)}}function ug(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?ug(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function cg(){for(var e=window,t=es();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=es(e.document)}return t}function Ju(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Yx(e){var t=cg(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&ug(n.ownerDocument.documentElement,n)){if(r!==null&&Ju(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,o=Math.min(r.start,i);r=r.end===void 0?o:Math.min(r.end,i),!e.extend&&o>r&&(i=r,r=o,o=i),i=oh(n,o);var s=oh(n,r);i&&s&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var qx=Gt&&"documentMode"in document&&11>=document.documentMode,cr=null,Ia=null,vi=null,_a=!1;function sh(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;_a||cr==null||cr!==es(r)||(r=cr,"selectionStart"in r&&Ju(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),vi&&zi(vi,r)||(vi=r,r=ls(Ia,"onSelect"),0<r.length&&(t=new qu("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=cr)))}function wo(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var fr={animationend:wo("Animation","AnimationEnd"),animationiteration:wo("Animation","AnimationIteration"),animationstart:wo("Animation","AnimationStart"),transitionend:wo("Transition","TransitionEnd")},Sl={},fg={};Gt&&(fg=document.createElement("div").style,"AnimationEvent"in window||(delete fr.animationend.animation,delete fr.animationiteration.animation,delete fr.animationstart.animation),"TransitionEvent"in window||delete fr.transitionend.transition);function Us(e){if(Sl[e])return Sl[e];if(!fr[e])return e;var t=fr[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in fg)return Sl[e]=t[n];return e}var hg=Us("animationend"),dg=Us("animationiteration"),pg=Us("animationstart"),mg=Us("transitionend"),gg=new Map,lh="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Tn(e,t){gg.set(e,t),Xn(t,[e])}for(var Cl=0;Cl<lh.length;Cl++){var El=lh[Cl],Xx=El.toLowerCase(),Zx=El[0].toUpperCase()+El.slice(1);Tn(Xx,"on"+Zx)}Tn(hg,"onAnimationEnd");Tn(dg,"onAnimationIteration");Tn(pg,"onAnimationStart");Tn("dblclick","onDoubleClick");Tn("focusin","onFocus");Tn("focusout","onBlur");Tn(mg,"onTransitionEnd");Rr("onMouseEnter",["mouseout","mouseover"]);Rr("onMouseLeave",["mouseout","mouseover"]);Rr("onPointerEnter",["pointerout","pointerover"]);Rr("onPointerLeave",["pointerout","pointerover"]);Xn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Xn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Xn("onBeforeInput",["compositionend","keypress","textInput","paste"]);Xn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Xn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Xn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var ci="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Jx=new Set("cancel close invalid load scroll toggle".split(" ").concat(ci));function ah(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,X1(r,t,void 0,e),e.currentTarget=null}function yg(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var s=r.length-1;0<=s;s--){var l=r[s],a=l.instance,u=l.currentTarget;if(l=l.listener,a!==o&&i.isPropagationStopped())break e;ah(i,l,u),o=a}else for(s=0;s<r.length;s++){if(l=r[s],a=l.instance,u=l.currentTarget,l=l.listener,a!==o&&i.isPropagationStopped())break e;ah(i,l,u),o=a}}}if(ns)throw e=La,ns=!1,La=null,e}function le(e,t){var n=t[ja];n===void 0&&(n=t[ja]=new Set);var r=e+"__bubble";n.has(r)||(vg(t,e,2,!1),n.add(r))}function Tl(e,t,n){var r=0;t&&(r|=4),vg(n,e,r,t)}var ko="_reactListening"+Math.random().toString(36).slice(2);function Fi(e){if(!e[ko]){e[ko]=!0,Tm.forEach(function(n){n!=="selectionchange"&&(Jx.has(n)||Tl(n,!1,e),Tl(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ko]||(t[ko]=!0,Tl("selectionchange",!1,t))}}function vg(e,t,n,r){switch(tg(t)){case 1:var i=dx;break;case 4:i=px;break;default:i=Gu}n=i.bind(null,t,n,e),i=void 0,!Da||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function Pl(e,t,n,r,i){var o=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var l=r.stateNode.containerInfo;if(l===i||l.nodeType===8&&l.parentNode===i)break;if(s===4)for(s=r.return;s!==null;){var a=s.tag;if((a===3||a===4)&&(a=s.stateNode.containerInfo,a===i||a.nodeType===8&&a.parentNode===i))return;s=s.return}for(;l!==null;){if(s=Nn(l),s===null)return;if(a=s.tag,a===5||a===6){r=o=s;continue e}l=l.parentNode}}r=r.return}Bm(function(){var u=o,c=Hu(n),f=[];e:{var h=gg.get(e);if(h!==void 0){var d=qu,g=e;switch(e){case"keypress":if(Bo(n)===0)break e;case"keydown":case"keyup":d=Lx;break;case"focusin":g="focus",d=xl;break;case"focusout":g="blur",d=xl;break;case"beforeblur":case"afterblur":d=xl;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":d=qf;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":d=yx;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":d=Mx;break;case hg:case dg:case pg:d=wx;break;case mg:d=_x;break;case"scroll":d=mx;break;case"wheel":d=zx;break;case"copy":case"cut":case"paste":d=Sx;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":d=Zf}var v=(t&4)!==0,w=!v&&e==="scroll",p=v?h!==null?h+"Capture":null:h;v=[];for(var m=u,y;m!==null;){y=m;var C=y.stateNode;if(y.tag===5&&C!==null&&(y=C,p!==null&&(C=Ri(m,p),C!=null&&v.push(Ni(m,C,y)))),w)break;m=m.return}0<v.length&&(h=new d(h,g,null,n,c),f.push({event:h,listeners:v}))}}if(!(t&7)){e:{if(h=e==="mouseover"||e==="pointerover",d=e==="mouseout"||e==="pointerout",h&&n!==Pa&&(g=n.relatedTarget||n.fromElement)&&(Nn(g)||g[Yt]))break e;if((d||h)&&(h=c.window===c?c:(h=c.ownerDocument)?h.defaultView||h.parentWindow:window,d?(g=n.relatedTarget||n.toElement,d=u,g=g?Nn(g):null,g!==null&&(w=Zn(g),g!==w||g.tag!==5&&g.tag!==6)&&(g=null)):(d=null,g=u),d!==g)){if(v=qf,C="onMouseLeave",p="onMouseEnter",m="mouse",(e==="pointerout"||e==="pointerover")&&(v=Zf,C="onPointerLeave",p="onPointerEnter",m="pointer"),w=d==null?h:hr(d),y=g==null?h:hr(g),h=new v(C,m+"leave",d,n,c),h.target=w,h.relatedTarget=y,C=null,Nn(c)===u&&(v=new v(p,m+"enter",g,n,c),v.target=y,v.relatedTarget=w,C=v),w=C,d&&g)t:{for(v=d,p=g,m=0,y=v;y;y=ir(y))m++;for(y=0,C=p;C;C=ir(C))y++;for(;0<m-y;)v=ir(v),m--;for(;0<y-m;)p=ir(p),y--;for(;m--;){if(v===p||p!==null&&v===p.alternate)break t;v=ir(v),p=ir(p)}v=null}else v=null;d!==null&&uh(f,h,d,v,!1),g!==null&&w!==null&&uh(f,w,g,v,!0)}}e:{if(h=u?hr(u):window,d=h.nodeName&&h.nodeName.toLowerCase(),d==="select"||d==="input"&&h.type==="file")var T=$x;else if(th(h))if(lg)T=Qx;else{T=Wx;var k=Hx}else(d=h.nodeName)&&d.toLowerCase()==="input"&&(h.type==="checkbox"||h.type==="radio")&&(T=Kx);if(T&&(T=T(e,u))){sg(f,T,n,c);break e}k&&k(e,h,u),e==="focusout"&&(k=h._wrapperState)&&k.controlled&&h.type==="number"&&ka(h,"number",h.value)}switch(k=u?hr(u):window,e){case"focusin":(th(k)||k.contentEditable==="true")&&(cr=k,Ia=u,vi=null);break;case"focusout":vi=Ia=cr=null;break;case"mousedown":_a=!0;break;case"contextmenu":case"mouseup":case"dragend":_a=!1,sh(f,n,c);break;case"selectionchange":if(qx)break;case"keydown":case"keyup":sh(f,n,c)}var P;if(Zu)e:{switch(e){case"compositionstart":var A="onCompositionStart";break e;case"compositionend":A="onCompositionEnd";break e;case"compositionupdate":A="onCompositionUpdate";break e}A=void 0}else ur?ig(e,n)&&(A="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(A="onCompositionStart");A&&(rg&&n.locale!=="ko"&&(ur||A!=="onCompositionStart"?A==="onCompositionEnd"&&ur&&(P=ng()):(cn=c,Yu="value"in cn?cn.value:cn.textContent,ur=!0)),k=ls(u,A),0<k.length&&(A=new Xf(A,e,null,n,c),f.push({event:A,listeners:k}),P?A.data=P:(P=og(n),P!==null&&(A.data=P)))),(P=Nx?jx(e,n):Vx(e,n))&&(u=ls(u,"onBeforeInput"),0<u.length&&(c=new Xf("onBeforeInput","beforeinput",null,n,c),f.push({event:c,listeners:u}),c.data=P))}yg(f,t)})}function Ni(e,t,n){return{instance:e,listener:t,currentTarget:n}}function ls(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,o=i.stateNode;i.tag===5&&o!==null&&(i=o,o=Ri(e,n),o!=null&&r.unshift(Ni(e,o,i)),o=Ri(e,t),o!=null&&r.push(Ni(e,o,i))),e=e.return}return r}function ir(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function uh(e,t,n,r,i){for(var o=t._reactName,s=[];n!==null&&n!==r;){var l=n,a=l.alternate,u=l.stateNode;if(a!==null&&a===r)break;l.tag===5&&u!==null&&(l=u,i?(a=Ri(n,o),a!=null&&s.unshift(Ni(n,a,l))):i||(a=Ri(n,o),a!=null&&s.push(Ni(n,a,l)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var ew=/\r\n?/g,tw=/\u0000|\uFFFD/g;function ch(e){return(typeof e=="string"?e:""+e).replace(ew,`
`).replace(tw,"")}function So(e,t,n){if(t=ch(t),ch(e)!==t&&n)throw Error(R(425))}function as(){}var Oa=null,za=null;function Fa(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Na=typeof setTimeout=="function"?setTimeout:void 0,nw=typeof clearTimeout=="function"?clearTimeout:void 0,fh=typeof Promise=="function"?Promise:void 0,rw=typeof queueMicrotask=="function"?queueMicrotask:typeof fh<"u"?function(e){return fh.resolve(null).then(e).catch(iw)}:Na;function iw(e){setTimeout(function(){throw e})}function Al(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),_i(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);_i(t)}function gn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function hh(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var $r=Math.random().toString(36).slice(2),It="__reactFiber$"+$r,ji="__reactProps$"+$r,Yt="__reactContainer$"+$r,ja="__reactEvents$"+$r,ow="__reactListeners$"+$r,sw="__reactHandles$"+$r;function Nn(e){var t=e[It];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Yt]||n[It]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=hh(e);e!==null;){if(n=e[It])return n;e=hh(e)}return t}e=n,n=e.parentNode}return null}function to(e){return e=e[It]||e[Yt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function hr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(R(33))}function $s(e){return e[ji]||null}var Va=[],dr=-1;function Pn(e){return{current:e}}function ae(e){0>dr||(e.current=Va[dr],Va[dr]=null,dr--)}function oe(e,t){dr++,Va[dr]=e.current,e.current=t}var Sn={},Fe=Pn(Sn),Ge=Pn(!1),Wn=Sn;function Mr(e,t){var n=e.type.contextTypes;if(!n)return Sn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},o;for(o in n)i[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Ye(e){return e=e.childContextTypes,e!=null}function us(){ae(Ge),ae(Fe)}function dh(e,t,n){if(Fe.current!==Sn)throw Error(R(168));oe(Fe,t),oe(Ge,n)}function xg(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(R(108,H1(e)||"Unknown",i));return me({},n,r)}function cs(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Sn,Wn=Fe.current,oe(Fe,e),oe(Ge,Ge.current),!0}function ph(e,t,n){var r=e.stateNode;if(!r)throw Error(R(169));n?(e=xg(e,t,Wn),r.__reactInternalMemoizedMergedChildContext=e,ae(Ge),ae(Fe),oe(Fe,e)):ae(Ge),oe(Ge,n)}var Ut=null,Hs=!1,Dl=!1;function wg(e){Ut===null?Ut=[e]:Ut.push(e)}function lw(e){Hs=!0,wg(e)}function An(){if(!Dl&&Ut!==null){Dl=!0;var e=0,t=te;try{var n=Ut;for(te=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Ut=null,Hs=!1}catch(i){throw Ut!==null&&(Ut=Ut.slice(e+1)),Wm(Wu,An),i}finally{te=t,Dl=!1}}return null}var pr=[],mr=0,fs=null,hs=0,ut=[],ct=0,Kn=null,$t=1,Ht="";function In(e,t){pr[mr++]=hs,pr[mr++]=fs,fs=e,hs=t}function kg(e,t,n){ut[ct++]=$t,ut[ct++]=Ht,ut[ct++]=Kn,Kn=e;var r=$t;e=Ht;var i=32-Tt(r)-1;r&=~(1<<i),n+=1;var o=32-Tt(t)+i;if(30<o){var s=i-i%5;o=(r&(1<<s)-1).toString(32),r>>=s,i-=s,$t=1<<32-Tt(t)+i|n<<i|r,Ht=o+e}else $t=1<<o|n<<i|r,Ht=e}function ec(e){e.return!==null&&(In(e,1),kg(e,1,0))}function tc(e){for(;e===fs;)fs=pr[--mr],pr[mr]=null,hs=pr[--mr],pr[mr]=null;for(;e===Kn;)Kn=ut[--ct],ut[ct]=null,Ht=ut[--ct],ut[ct]=null,$t=ut[--ct],ut[ct]=null}var tt=null,et=null,ce=!1,Et=null;function Sg(e,t){var n=ht(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function mh(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,tt=e,et=gn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,tt=e,et=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Kn!==null?{id:$t,overflow:Ht}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=ht(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,tt=e,et=null,!0):!1;default:return!1}}function Ba(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Ua(e){if(ce){var t=et;if(t){var n=t;if(!mh(e,t)){if(Ba(e))throw Error(R(418));t=gn(n.nextSibling);var r=tt;t&&mh(e,t)?Sg(r,n):(e.flags=e.flags&-4097|2,ce=!1,tt=e)}}else{if(Ba(e))throw Error(R(418));e.flags=e.flags&-4097|2,ce=!1,tt=e}}}function gh(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;tt=e}function Co(e){if(e!==tt)return!1;if(!ce)return gh(e),ce=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Fa(e.type,e.memoizedProps)),t&&(t=et)){if(Ba(e))throw Cg(),Error(R(418));for(;t;)Sg(e,t),t=gn(t.nextSibling)}if(gh(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(R(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){et=gn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}et=null}}else et=tt?gn(e.stateNode.nextSibling):null;return!0}function Cg(){for(var e=et;e;)e=gn(e.nextSibling)}function Ir(){et=tt=null,ce=!1}function nc(e){Et===null?Et=[e]:Et.push(e)}var aw=Jt.ReactCurrentBatchConfig;function ti(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(R(309));var r=n.stateNode}if(!r)throw Error(R(147,e));var i=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(s){var l=i.refs;s===null?delete l[o]:l[o]=s},t._stringRef=o,t)}if(typeof e!="string")throw Error(R(284));if(!n._owner)throw Error(R(290,e))}return e}function Eo(e,t){throw e=Object.prototype.toString.call(t),Error(R(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function yh(e){var t=e._init;return t(e._payload)}function Eg(e){function t(p,m){if(e){var y=p.deletions;y===null?(p.deletions=[m],p.flags|=16):y.push(m)}}function n(p,m){if(!e)return null;for(;m!==null;)t(p,m),m=m.sibling;return null}function r(p,m){for(p=new Map;m!==null;)m.key!==null?p.set(m.key,m):p.set(m.index,m),m=m.sibling;return p}function i(p,m){return p=wn(p,m),p.index=0,p.sibling=null,p}function o(p,m,y){return p.index=y,e?(y=p.alternate,y!==null?(y=y.index,y<m?(p.flags|=2,m):y):(p.flags|=2,m)):(p.flags|=1048576,m)}function s(p){return e&&p.alternate===null&&(p.flags|=2),p}function l(p,m,y,C){return m===null||m.tag!==6?(m=Ol(y,p.mode,C),m.return=p,m):(m=i(m,y),m.return=p,m)}function a(p,m,y,C){var T=y.type;return T===ar?c(p,m,y.props.children,C,y.key):m!==null&&(m.elementType===T||typeof T=="object"&&T!==null&&T.$$typeof===sn&&yh(T)===m.type)?(C=i(m,y.props),C.ref=ti(p,m,y),C.return=p,C):(C=Go(y.type,y.key,y.props,null,p.mode,C),C.ref=ti(p,m,y),C.return=p,C)}function u(p,m,y,C){return m===null||m.tag!==4||m.stateNode.containerInfo!==y.containerInfo||m.stateNode.implementation!==y.implementation?(m=zl(y,p.mode,C),m.return=p,m):(m=i(m,y.children||[]),m.return=p,m)}function c(p,m,y,C,T){return m===null||m.tag!==7?(m=$n(y,p.mode,C,T),m.return=p,m):(m=i(m,y),m.return=p,m)}function f(p,m,y){if(typeof m=="string"&&m!==""||typeof m=="number")return m=Ol(""+m,p.mode,y),m.return=p,m;if(typeof m=="object"&&m!==null){switch(m.$$typeof){case ho:return y=Go(m.type,m.key,m.props,null,p.mode,y),y.ref=ti(p,null,m),y.return=p,y;case lr:return m=zl(m,p.mode,y),m.return=p,m;case sn:var C=m._init;return f(p,C(m._payload),y)}if(ai(m)||qr(m))return m=$n(m,p.mode,y,null),m.return=p,m;Eo(p,m)}return null}function h(p,m,y,C){var T=m!==null?m.key:null;if(typeof y=="string"&&y!==""||typeof y=="number")return T!==null?null:l(p,m,""+y,C);if(typeof y=="object"&&y!==null){switch(y.$$typeof){case ho:return y.key===T?a(p,m,y,C):null;case lr:return y.key===T?u(p,m,y,C):null;case sn:return T=y._init,h(p,m,T(y._payload),C)}if(ai(y)||qr(y))return T!==null?null:c(p,m,y,C,null);Eo(p,y)}return null}function d(p,m,y,C,T){if(typeof C=="string"&&C!==""||typeof C=="number")return p=p.get(y)||null,l(m,p,""+C,T);if(typeof C=="object"&&C!==null){switch(C.$$typeof){case ho:return p=p.get(C.key===null?y:C.key)||null,a(m,p,C,T);case lr:return p=p.get(C.key===null?y:C.key)||null,u(m,p,C,T);case sn:var k=C._init;return d(p,m,y,k(C._payload),T)}if(ai(C)||qr(C))return p=p.get(y)||null,c(m,p,C,T,null);Eo(m,C)}return null}function g(p,m,y,C){for(var T=null,k=null,P=m,A=m=0,O=null;P!==null&&A<y.length;A++){P.index>A?(O=P,P=null):O=P.sibling;var E=h(p,P,y[A],C);if(E===null){P===null&&(P=O);break}e&&P&&E.alternate===null&&t(p,P),m=o(E,m,A),k===null?T=E:k.sibling=E,k=E,P=O}if(A===y.length)return n(p,P),ce&&In(p,A),T;if(P===null){for(;A<y.length;A++)P=f(p,y[A],C),P!==null&&(m=o(P,m,A),k===null?T=P:k.sibling=P,k=P);return ce&&In(p,A),T}for(P=r(p,P);A<y.length;A++)O=d(P,p,A,y[A],C),O!==null&&(e&&O.alternate!==null&&P.delete(O.key===null?A:O.key),m=o(O,m,A),k===null?T=O:k.sibling=O,k=O);return e&&P.forEach(function(_){return t(p,_)}),ce&&In(p,A),T}function v(p,m,y,C){var T=qr(y);if(typeof T!="function")throw Error(R(150));if(y=T.call(y),y==null)throw Error(R(151));for(var k=T=null,P=m,A=m=0,O=null,E=y.next();P!==null&&!E.done;A++,E=y.next()){P.index>A?(O=P,P=null):O=P.sibling;var _=h(p,P,E.value,C);if(_===null){P===null&&(P=O);break}e&&P&&_.alternate===null&&t(p,P),m=o(_,m,A),k===null?T=_:k.sibling=_,k=_,P=O}if(E.done)return n(p,P),ce&&In(p,A),T;if(P===null){for(;!E.done;A++,E=y.next())E=f(p,E.value,C),E!==null&&(m=o(E,m,A),k===null?T=E:k.sibling=E,k=E);return ce&&In(p,A),T}for(P=r(p,P);!E.done;A++,E=y.next())E=d(P,p,A,E.value,C),E!==null&&(e&&E.alternate!==null&&P.delete(E.key===null?A:E.key),m=o(E,m,A),k===null?T=E:k.sibling=E,k=E);return e&&P.forEach(function(N){return t(p,N)}),ce&&In(p,A),T}function w(p,m,y,C){if(typeof y=="object"&&y!==null&&y.type===ar&&y.key===null&&(y=y.props.children),typeof y=="object"&&y!==null){switch(y.$$typeof){case ho:e:{for(var T=y.key,k=m;k!==null;){if(k.key===T){if(T=y.type,T===ar){if(k.tag===7){n(p,k.sibling),m=i(k,y.props.children),m.return=p,p=m;break e}}else if(k.elementType===T||typeof T=="object"&&T!==null&&T.$$typeof===sn&&yh(T)===k.type){n(p,k.sibling),m=i(k,y.props),m.ref=ti(p,k,y),m.return=p,p=m;break e}n(p,k);break}else t(p,k);k=k.sibling}y.type===ar?(m=$n(y.props.children,p.mode,C,y.key),m.return=p,p=m):(C=Go(y.type,y.key,y.props,null,p.mode,C),C.ref=ti(p,m,y),C.return=p,p=C)}return s(p);case lr:e:{for(k=y.key;m!==null;){if(m.key===k)if(m.tag===4&&m.stateNode.containerInfo===y.containerInfo&&m.stateNode.implementation===y.implementation){n(p,m.sibling),m=i(m,y.children||[]),m.return=p,p=m;break e}else{n(p,m);break}else t(p,m);m=m.sibling}m=zl(y,p.mode,C),m.return=p,p=m}return s(p);case sn:return k=y._init,w(p,m,k(y._payload),C)}if(ai(y))return g(p,m,y,C);if(qr(y))return v(p,m,y,C);Eo(p,y)}return typeof y=="string"&&y!==""||typeof y=="number"?(y=""+y,m!==null&&m.tag===6?(n(p,m.sibling),m=i(m,y),m.return=p,p=m):(n(p,m),m=Ol(y,p.mode,C),m.return=p,p=m),s(p)):n(p,m)}return w}var _r=Eg(!0),Tg=Eg(!1),ds=Pn(null),ps=null,gr=null,rc=null;function ic(){rc=gr=ps=null}function oc(e){var t=ds.current;ae(ds),e._currentValue=t}function $a(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Dr(e,t){ps=e,rc=gr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Qe=!0),e.firstContext=null)}function mt(e){var t=e._currentValue;if(rc!==e)if(e={context:e,memoizedValue:t,next:null},gr===null){if(ps===null)throw Error(R(308));gr=e,ps.dependencies={lanes:0,firstContext:e}}else gr=gr.next=e;return t}var jn=null;function sc(e){jn===null?jn=[e]:jn.push(e)}function Pg(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,sc(t)):(n.next=i.next,i.next=n),t.interleaved=n,qt(e,r)}function qt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var ln=!1;function lc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Ag(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Wt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function yn(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,Z&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,qt(e,n)}return i=r.interleaved,i===null?(t.next=t,sc(r)):(t.next=i.next,i.next=t),r.interleaved=t,qt(e,n)}function Uo(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Ku(e,n)}}function vh(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?i=o=s:o=o.next=s,n=n.next}while(n!==null);o===null?i=o=t:o=o.next=t}else i=o=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function ms(e,t,n,r){var i=e.updateQueue;ln=!1;var o=i.firstBaseUpdate,s=i.lastBaseUpdate,l=i.shared.pending;if(l!==null){i.shared.pending=null;var a=l,u=a.next;a.next=null,s===null?o=u:s.next=u,s=a;var c=e.alternate;c!==null&&(c=c.updateQueue,l=c.lastBaseUpdate,l!==s&&(l===null?c.firstBaseUpdate=u:l.next=u,c.lastBaseUpdate=a))}if(o!==null){var f=i.baseState;s=0,c=u=a=null,l=o;do{var h=l.lane,d=l.eventTime;if((r&h)===h){c!==null&&(c=c.next={eventTime:d,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var g=e,v=l;switch(h=t,d=n,v.tag){case 1:if(g=v.payload,typeof g=="function"){f=g.call(d,f,h);break e}f=g;break e;case 3:g.flags=g.flags&-65537|128;case 0:if(g=v.payload,h=typeof g=="function"?g.call(d,f,h):g,h==null)break e;f=me({},f,h);break e;case 2:ln=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,h=i.effects,h===null?i.effects=[l]:h.push(l))}else d={eventTime:d,lane:h,tag:l.tag,payload:l.payload,callback:l.callback,next:null},c===null?(u=c=d,a=f):c=c.next=d,s|=h;if(l=l.next,l===null){if(l=i.shared.pending,l===null)break;h=l,l=h.next,h.next=null,i.lastBaseUpdate=h,i.shared.pending=null}}while(!0);if(c===null&&(a=f),i.baseState=a,i.firstBaseUpdate=u,i.lastBaseUpdate=c,t=i.shared.interleaved,t!==null){i=t;do s|=i.lane,i=i.next;while(i!==t)}else o===null&&(i.shared.lanes=0);Gn|=s,e.lanes=s,e.memoizedState=f}}function xh(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(R(191,i));i.call(r)}}}var no={},Ot=Pn(no),Vi=Pn(no),Bi=Pn(no);function Vn(e){if(e===no)throw Error(R(174));return e}function ac(e,t){switch(oe(Bi,t),oe(Vi,e),oe(Ot,no),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Ca(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Ca(t,e)}ae(Ot),oe(Ot,t)}function Or(){ae(Ot),ae(Vi),ae(Bi)}function Dg(e){Vn(Bi.current);var t=Vn(Ot.current),n=Ca(t,e.type);t!==n&&(oe(Vi,e),oe(Ot,n))}function uc(e){Vi.current===e&&(ae(Ot),ae(Vi))}var he=Pn(0);function gs(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Ll=[];function cc(){for(var e=0;e<Ll.length;e++)Ll[e]._workInProgressVersionPrimary=null;Ll.length=0}var $o=Jt.ReactCurrentDispatcher,bl=Jt.ReactCurrentBatchConfig,Qn=0,pe=null,Ce=null,Te=null,ys=!1,xi=!1,Ui=0,uw=0;function Re(){throw Error(R(321))}function fc(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Dt(e[n],t[n]))return!1;return!0}function hc(e,t,n,r,i,o){if(Qn=o,pe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,$o.current=e===null||e.memoizedState===null?dw:pw,e=n(r,i),xi){o=0;do{if(xi=!1,Ui=0,25<=o)throw Error(R(301));o+=1,Te=Ce=null,t.updateQueue=null,$o.current=mw,e=n(r,i)}while(xi)}if($o.current=vs,t=Ce!==null&&Ce.next!==null,Qn=0,Te=Ce=pe=null,ys=!1,t)throw Error(R(300));return e}function dc(){var e=Ui!==0;return Ui=0,e}function Rt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Te===null?pe.memoizedState=Te=e:Te=Te.next=e,Te}function gt(){if(Ce===null){var e=pe.alternate;e=e!==null?e.memoizedState:null}else e=Ce.next;var t=Te===null?pe.memoizedState:Te.next;if(t!==null)Te=t,Ce=e;else{if(e===null)throw Error(R(310));Ce=e,e={memoizedState:Ce.memoizedState,baseState:Ce.baseState,baseQueue:Ce.baseQueue,queue:Ce.queue,next:null},Te===null?pe.memoizedState=Te=e:Te=Te.next=e}return Te}function $i(e,t){return typeof t=="function"?t(e):t}function Rl(e){var t=gt(),n=t.queue;if(n===null)throw Error(R(311));n.lastRenderedReducer=e;var r=Ce,i=r.baseQueue,o=n.pending;if(o!==null){if(i!==null){var s=i.next;i.next=o.next,o.next=s}r.baseQueue=i=o,n.pending=null}if(i!==null){o=i.next,r=r.baseState;var l=s=null,a=null,u=o;do{var c=u.lane;if((Qn&c)===c)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(l=a=f,s=r):a=a.next=f,pe.lanes|=c,Gn|=c}u=u.next}while(u!==null&&u!==o);a===null?s=r:a.next=l,Dt(r,t.memoizedState)||(Qe=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do o=i.lane,pe.lanes|=o,Gn|=o,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Ml(e){var t=gt(),n=t.queue;if(n===null)throw Error(R(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,o=t.memoizedState;if(i!==null){n.pending=null;var s=i=i.next;do o=e(o,s.action),s=s.next;while(s!==i);Dt(o,t.memoizedState)||(Qe=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function Lg(){}function bg(e,t){var n=pe,r=gt(),i=t(),o=!Dt(r.memoizedState,i);if(o&&(r.memoizedState=i,Qe=!0),r=r.queue,pc(Ig.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||Te!==null&&Te.memoizedState.tag&1){if(n.flags|=2048,Hi(9,Mg.bind(null,n,r,i,t),void 0,null),Pe===null)throw Error(R(349));Qn&30||Rg(n,t,i)}return i}function Rg(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=pe.updateQueue,t===null?(t={lastEffect:null,stores:null},pe.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Mg(e,t,n,r){t.value=n,t.getSnapshot=r,_g(t)&&Og(e)}function Ig(e,t,n){return n(function(){_g(t)&&Og(e)})}function _g(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Dt(e,n)}catch{return!0}}function Og(e){var t=qt(e,1);t!==null&&Pt(t,e,1,-1)}function wh(e){var t=Rt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:$i,lastRenderedState:e},t.queue=e,e=e.dispatch=hw.bind(null,pe,e),[t.memoizedState,e]}function Hi(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=pe.updateQueue,t===null?(t={lastEffect:null,stores:null},pe.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function zg(){return gt().memoizedState}function Ho(e,t,n,r){var i=Rt();pe.flags|=e,i.memoizedState=Hi(1|t,n,void 0,r===void 0?null:r)}function Ws(e,t,n,r){var i=gt();r=r===void 0?null:r;var o=void 0;if(Ce!==null){var s=Ce.memoizedState;if(o=s.destroy,r!==null&&fc(r,s.deps)){i.memoizedState=Hi(t,n,o,r);return}}pe.flags|=e,i.memoizedState=Hi(1|t,n,o,r)}function kh(e,t){return Ho(8390656,8,e,t)}function pc(e,t){return Ws(2048,8,e,t)}function Fg(e,t){return Ws(4,2,e,t)}function Ng(e,t){return Ws(4,4,e,t)}function jg(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Vg(e,t,n){return n=n!=null?n.concat([e]):null,Ws(4,4,jg.bind(null,t,e),n)}function mc(){}function Bg(e,t){var n=gt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&fc(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Ug(e,t){var n=gt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&fc(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function $g(e,t,n){return Qn&21?(Dt(n,t)||(n=Gm(),pe.lanes|=n,Gn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Qe=!0),e.memoizedState=n)}function cw(e,t){var n=te;te=n!==0&&4>n?n:4,e(!0);var r=bl.transition;bl.transition={};try{e(!1),t()}finally{te=n,bl.transition=r}}function Hg(){return gt().memoizedState}function fw(e,t,n){var r=xn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Wg(e))Kg(t,n);else if(n=Pg(e,t,n,r),n!==null){var i=Ue();Pt(n,e,r,i),Qg(n,t,r)}}function hw(e,t,n){var r=xn(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Wg(e))Kg(t,i);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var s=t.lastRenderedState,l=o(s,n);if(i.hasEagerState=!0,i.eagerState=l,Dt(l,s)){var a=t.interleaved;a===null?(i.next=i,sc(t)):(i.next=a.next,a.next=i),t.interleaved=i;return}}catch{}finally{}n=Pg(e,t,i,r),n!==null&&(i=Ue(),Pt(n,e,r,i),Qg(n,t,r))}}function Wg(e){var t=e.alternate;return e===pe||t!==null&&t===pe}function Kg(e,t){xi=ys=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Qg(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Ku(e,n)}}var vs={readContext:mt,useCallback:Re,useContext:Re,useEffect:Re,useImperativeHandle:Re,useInsertionEffect:Re,useLayoutEffect:Re,useMemo:Re,useReducer:Re,useRef:Re,useState:Re,useDebugValue:Re,useDeferredValue:Re,useTransition:Re,useMutableSource:Re,useSyncExternalStore:Re,useId:Re,unstable_isNewReconciler:!1},dw={readContext:mt,useCallback:function(e,t){return Rt().memoizedState=[e,t===void 0?null:t],e},useContext:mt,useEffect:kh,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Ho(4194308,4,jg.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Ho(4194308,4,e,t)},useInsertionEffect:function(e,t){return Ho(4,2,e,t)},useMemo:function(e,t){var n=Rt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Rt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=fw.bind(null,pe,e),[r.memoizedState,e]},useRef:function(e){var t=Rt();return e={current:e},t.memoizedState=e},useState:wh,useDebugValue:mc,useDeferredValue:function(e){return Rt().memoizedState=e},useTransition:function(){var e=wh(!1),t=e[0];return e=cw.bind(null,e[1]),Rt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=pe,i=Rt();if(ce){if(n===void 0)throw Error(R(407));n=n()}else{if(n=t(),Pe===null)throw Error(R(349));Qn&30||Rg(r,t,n)}i.memoizedState=n;var o={value:n,getSnapshot:t};return i.queue=o,kh(Ig.bind(null,r,o,e),[e]),r.flags|=2048,Hi(9,Mg.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=Rt(),t=Pe.identifierPrefix;if(ce){var n=Ht,r=$t;n=(r&~(1<<32-Tt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Ui++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=uw++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},pw={readContext:mt,useCallback:Bg,useContext:mt,useEffect:pc,useImperativeHandle:Vg,useInsertionEffect:Fg,useLayoutEffect:Ng,useMemo:Ug,useReducer:Rl,useRef:zg,useState:function(){return Rl($i)},useDebugValue:mc,useDeferredValue:function(e){var t=gt();return $g(t,Ce.memoizedState,e)},useTransition:function(){var e=Rl($i)[0],t=gt().memoizedState;return[e,t]},useMutableSource:Lg,useSyncExternalStore:bg,useId:Hg,unstable_isNewReconciler:!1},mw={readContext:mt,useCallback:Bg,useContext:mt,useEffect:pc,useImperativeHandle:Vg,useInsertionEffect:Fg,useLayoutEffect:Ng,useMemo:Ug,useReducer:Ml,useRef:zg,useState:function(){return Ml($i)},useDebugValue:mc,useDeferredValue:function(e){var t=gt();return Ce===null?t.memoizedState=e:$g(t,Ce.memoizedState,e)},useTransition:function(){var e=Ml($i)[0],t=gt().memoizedState;return[e,t]},useMutableSource:Lg,useSyncExternalStore:bg,useId:Hg,unstable_isNewReconciler:!1};function St(e,t){if(e&&e.defaultProps){t=me({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Ha(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:me({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Ks={isMounted:function(e){return(e=e._reactInternals)?Zn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Ue(),i=xn(e),o=Wt(r,i);o.payload=t,n!=null&&(o.callback=n),t=yn(e,o,i),t!==null&&(Pt(t,e,i,r),Uo(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Ue(),i=xn(e),o=Wt(r,i);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=yn(e,o,i),t!==null&&(Pt(t,e,i,r),Uo(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ue(),r=xn(e),i=Wt(n,r);i.tag=2,t!=null&&(i.callback=t),t=yn(e,i,r),t!==null&&(Pt(t,e,r,n),Uo(t,e,r))}};function Sh(e,t,n,r,i,o,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,s):t.prototype&&t.prototype.isPureReactComponent?!zi(n,r)||!zi(i,o):!0}function Gg(e,t,n){var r=!1,i=Sn,o=t.contextType;return typeof o=="object"&&o!==null?o=mt(o):(i=Ye(t)?Wn:Fe.current,r=t.contextTypes,o=(r=r!=null)?Mr(e,i):Sn),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Ks,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=o),t}function Ch(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Ks.enqueueReplaceState(t,t.state,null)}function Wa(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},lc(e);var o=t.contextType;typeof o=="object"&&o!==null?i.context=mt(o):(o=Ye(t)?Wn:Fe.current,i.context=Mr(e,o)),i.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(Ha(e,t,o,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&Ks.enqueueReplaceState(i,i.state,null),ms(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function zr(e,t){try{var n="",r=t;do n+=$1(r),r=r.return;while(r);var i=n}catch(o){i=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:i,digest:null}}function Il(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Ka(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var gw=typeof WeakMap=="function"?WeakMap:Map;function Yg(e,t,n){n=Wt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){ws||(ws=!0,nu=r),Ka(e,t)},n}function qg(e,t,n){n=Wt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){Ka(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){Ka(e,t),typeof r!="function"&&(vn===null?vn=new Set([this]):vn.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function Eh(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new gw;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=bw.bind(null,e,t,n),t.then(e,e))}function Th(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Ph(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Wt(-1,1),t.tag=2,yn(n,t,1))),n.lanes|=1),e)}var yw=Jt.ReactCurrentOwner,Qe=!1;function Ve(e,t,n,r){t.child=e===null?Tg(t,null,n,r):_r(t,e.child,n,r)}function Ah(e,t,n,r,i){n=n.render;var o=t.ref;return Dr(t,i),r=hc(e,t,n,r,o,i),n=dc(),e!==null&&!Qe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,Xt(e,t,i)):(ce&&n&&ec(t),t.flags|=1,Ve(e,t,r,i),t.child)}function Dh(e,t,n,r,i){if(e===null){var o=n.type;return typeof o=="function"&&!Cc(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,Xg(e,t,o,r,i)):(e=Go(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&i)){var s=o.memoizedProps;if(n=n.compare,n=n!==null?n:zi,n(s,r)&&e.ref===t.ref)return Xt(e,t,i)}return t.flags|=1,e=wn(o,r),e.ref=t.ref,e.return=t,t.child=e}function Xg(e,t,n,r,i){if(e!==null){var o=e.memoizedProps;if(zi(o,r)&&e.ref===t.ref)if(Qe=!1,t.pendingProps=r=o,(e.lanes&i)!==0)e.flags&131072&&(Qe=!0);else return t.lanes=e.lanes,Xt(e,t,i)}return Qa(e,t,n,r,i)}function Zg(e,t,n){var r=t.pendingProps,i=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},oe(vr,Je),Je|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,oe(vr,Je),Je|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,oe(vr,Je),Je|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,oe(vr,Je),Je|=r;return Ve(e,t,i,n),t.child}function Jg(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Qa(e,t,n,r,i){var o=Ye(n)?Wn:Fe.current;return o=Mr(t,o),Dr(t,i),n=hc(e,t,n,r,o,i),r=dc(),e!==null&&!Qe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,Xt(e,t,i)):(ce&&r&&ec(t),t.flags|=1,Ve(e,t,n,i),t.child)}function Lh(e,t,n,r,i){if(Ye(n)){var o=!0;cs(t)}else o=!1;if(Dr(t,i),t.stateNode===null)Wo(e,t),Gg(t,n,r),Wa(t,n,r,i),r=!0;else if(e===null){var s=t.stateNode,l=t.memoizedProps;s.props=l;var a=s.context,u=n.contextType;typeof u=="object"&&u!==null?u=mt(u):(u=Ye(n)?Wn:Fe.current,u=Mr(t,u));var c=n.getDerivedStateFromProps,f=typeof c=="function"||typeof s.getSnapshotBeforeUpdate=="function";f||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==r||a!==u)&&Ch(t,s,r,u),ln=!1;var h=t.memoizedState;s.state=h,ms(t,r,s,i),a=t.memoizedState,l!==r||h!==a||Ge.current||ln?(typeof c=="function"&&(Ha(t,n,c,r),a=t.memoizedState),(l=ln||Sh(t,n,l,r,h,a,u))?(f||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),s.props=r,s.state=a,s.context=u,r=l):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,Ag(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:St(t.type,l),s.props=u,f=t.pendingProps,h=s.context,a=n.contextType,typeof a=="object"&&a!==null?a=mt(a):(a=Ye(n)?Wn:Fe.current,a=Mr(t,a));var d=n.getDerivedStateFromProps;(c=typeof d=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==f||h!==a)&&Ch(t,s,r,a),ln=!1,h=t.memoizedState,s.state=h,ms(t,r,s,i);var g=t.memoizedState;l!==f||h!==g||Ge.current||ln?(typeof d=="function"&&(Ha(t,n,d,r),g=t.memoizedState),(u=ln||Sh(t,n,u,r,h,g,a)||!1)?(c||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,g,a),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,g,a)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=g),s.props=r,s.state=g,s.context=a,r=u):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),r=!1)}return Ga(e,t,n,r,o,i)}function Ga(e,t,n,r,i,o){Jg(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return i&&ph(t,n,!1),Xt(e,t,o);r=t.stateNode,yw.current=t;var l=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=_r(t,e.child,null,o),t.child=_r(t,null,l,o)):Ve(e,t,l,o),t.memoizedState=r.state,i&&ph(t,n,!0),t.child}function ey(e){var t=e.stateNode;t.pendingContext?dh(e,t.pendingContext,t.pendingContext!==t.context):t.context&&dh(e,t.context,!1),ac(e,t.containerInfo)}function bh(e,t,n,r,i){return Ir(),nc(i),t.flags|=256,Ve(e,t,n,r),t.child}var Ya={dehydrated:null,treeContext:null,retryLane:0};function qa(e){return{baseLanes:e,cachePool:null,transitions:null}}function ty(e,t,n){var r=t.pendingProps,i=he.current,o=!1,s=(t.flags&128)!==0,l;if((l=s)||(l=e!==null&&e.memoizedState===null?!1:(i&2)!==0),l?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),oe(he,i&1),e===null)return Ua(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,o?(r=t.mode,o=t.child,s={mode:"hidden",children:s},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=s):o=Ys(s,r,0,null),e=$n(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=qa(n),t.memoizedState=Ya,e):gc(t,s));if(i=e.memoizedState,i!==null&&(l=i.dehydrated,l!==null))return vw(e,t,s,r,l,i,n);if(o){o=r.fallback,s=t.mode,i=e.child,l=i.sibling;var a={mode:"hidden",children:r.children};return!(s&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=wn(i,a),r.subtreeFlags=i.subtreeFlags&14680064),l!==null?o=wn(l,o):(o=$n(o,s,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,s=e.child.memoizedState,s=s===null?qa(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},o.memoizedState=s,o.childLanes=e.childLanes&~n,t.memoizedState=Ya,r}return o=e.child,e=o.sibling,r=wn(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function gc(e,t){return t=Ys({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function To(e,t,n,r){return r!==null&&nc(r),_r(t,e.child,null,n),e=gc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function vw(e,t,n,r,i,o,s){if(n)return t.flags&256?(t.flags&=-257,r=Il(Error(R(422))),To(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,i=t.mode,r=Ys({mode:"visible",children:r.children},i,0,null),o=$n(o,i,s,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&_r(t,e.child,null,s),t.child.memoizedState=qa(s),t.memoizedState=Ya,o);if(!(t.mode&1))return To(e,t,s,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var l=r.dgst;return r=l,o=Error(R(419)),r=Il(o,r,void 0),To(e,t,s,r)}if(l=(s&e.childLanes)!==0,Qe||l){if(r=Pe,r!==null){switch(s&-s){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|s)?0:i,i!==0&&i!==o.retryLane&&(o.retryLane=i,qt(e,i),Pt(r,e,i,-1))}return Sc(),r=Il(Error(R(421))),To(e,t,s,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=Rw.bind(null,e),i._reactRetry=t,null):(e=o.treeContext,et=gn(i.nextSibling),tt=t,ce=!0,Et=null,e!==null&&(ut[ct++]=$t,ut[ct++]=Ht,ut[ct++]=Kn,$t=e.id,Ht=e.overflow,Kn=t),t=gc(t,r.children),t.flags|=4096,t)}function Rh(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),$a(e.return,t,n)}function _l(e,t,n,r,i){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=i)}function ny(e,t,n){var r=t.pendingProps,i=r.revealOrder,o=r.tail;if(Ve(e,t,r.children,n),r=he.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Rh(e,n,t);else if(e.tag===19)Rh(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(oe(he,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&gs(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),_l(t,!1,i,n,o);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&gs(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}_l(t,!0,n,null,o);break;case"together":_l(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Wo(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Xt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Gn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(R(153));if(t.child!==null){for(e=t.child,n=wn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=wn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function xw(e,t,n){switch(t.tag){case 3:ey(t),Ir();break;case 5:Dg(t);break;case 1:Ye(t.type)&&cs(t);break;case 4:ac(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;oe(ds,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(oe(he,he.current&1),t.flags|=128,null):n&t.child.childLanes?ty(e,t,n):(oe(he,he.current&1),e=Xt(e,t,n),e!==null?e.sibling:null);oe(he,he.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return ny(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),oe(he,he.current),r)break;return null;case 22:case 23:return t.lanes=0,Zg(e,t,n)}return Xt(e,t,n)}var ry,Xa,iy,oy;ry=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Xa=function(){};iy=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,Vn(Ot.current);var o=null;switch(n){case"input":i=xa(e,i),r=xa(e,r),o=[];break;case"select":i=me({},i,{value:void 0}),r=me({},r,{value:void 0}),o=[];break;case"textarea":i=Sa(e,i),r=Sa(e,r),o=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=as)}Ea(n,r);var s;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var l=i[u];for(s in l)l.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Li.hasOwnProperty(u)?o||(o=[]):(o=o||[]).push(u,null));for(u in r){var a=r[u];if(l=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&a!==l&&(a!=null||l!=null))if(u==="style")if(l){for(s in l)!l.hasOwnProperty(s)||a&&a.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in a)a.hasOwnProperty(s)&&l[s]!==a[s]&&(n||(n={}),n[s]=a[s])}else n||(o||(o=[]),o.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,l=l?l.__html:void 0,a!=null&&l!==a&&(o=o||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(o=o||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Li.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&le("scroll",e),o||l===a||(o=[])):(o=o||[]).push(u,a))}n&&(o=o||[]).push("style",n);var u=o;(t.updateQueue=u)&&(t.flags|=4)}};oy=function(e,t,n,r){n!==r&&(t.flags|=4)};function ni(e,t){if(!ce)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Me(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function ww(e,t,n){var r=t.pendingProps;switch(tc(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Me(t),null;case 1:return Ye(t.type)&&us(),Me(t),null;case 3:return r=t.stateNode,Or(),ae(Ge),ae(Fe),cc(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Co(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Et!==null&&(ou(Et),Et=null))),Xa(e,t),Me(t),null;case 5:uc(t);var i=Vn(Bi.current);if(n=t.type,e!==null&&t.stateNode!=null)iy(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(R(166));return Me(t),null}if(e=Vn(Ot.current),Co(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[It]=t,r[ji]=o,e=(t.mode&1)!==0,n){case"dialog":le("cancel",r),le("close",r);break;case"iframe":case"object":case"embed":le("load",r);break;case"video":case"audio":for(i=0;i<ci.length;i++)le(ci[i],r);break;case"source":le("error",r);break;case"img":case"image":case"link":le("error",r),le("load",r);break;case"details":le("toggle",r);break;case"input":Vf(r,o),le("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},le("invalid",r);break;case"textarea":Uf(r,o),le("invalid",r)}Ea(n,o),i=null;for(var s in o)if(o.hasOwnProperty(s)){var l=o[s];s==="children"?typeof l=="string"?r.textContent!==l&&(o.suppressHydrationWarning!==!0&&So(r.textContent,l,e),i=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(o.suppressHydrationWarning!==!0&&So(r.textContent,l,e),i=["children",""+l]):Li.hasOwnProperty(s)&&l!=null&&s==="onScroll"&&le("scroll",r)}switch(n){case"input":po(r),Bf(r,o,!0);break;case"textarea":po(r),$f(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=as)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Im(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[It]=t,e[ji]=r,ry(e,t,!1,!1),t.stateNode=e;e:{switch(s=Ta(n,r),n){case"dialog":le("cancel",e),le("close",e),i=r;break;case"iframe":case"object":case"embed":le("load",e),i=r;break;case"video":case"audio":for(i=0;i<ci.length;i++)le(ci[i],e);i=r;break;case"source":le("error",e),i=r;break;case"img":case"image":case"link":le("error",e),le("load",e),i=r;break;case"details":le("toggle",e),i=r;break;case"input":Vf(e,r),i=xa(e,r),le("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=me({},r,{value:void 0}),le("invalid",e);break;case"textarea":Uf(e,r),i=Sa(e,r),le("invalid",e);break;default:i=r}Ea(n,i),l=i;for(o in l)if(l.hasOwnProperty(o)){var a=l[o];o==="style"?zm(e,a):o==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&_m(e,a)):o==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&bi(e,a):typeof a=="number"&&bi(e,""+a):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(Li.hasOwnProperty(o)?a!=null&&o==="onScroll"&&le("scroll",e):a!=null&&Vu(e,o,a,s))}switch(n){case"input":po(e),Bf(e,r,!1);break;case"textarea":po(e),$f(e);break;case"option":r.value!=null&&e.setAttribute("value",""+kn(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?Er(e,!!r.multiple,o,!1):r.defaultValue!=null&&Er(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=as)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Me(t),null;case 6:if(e&&t.stateNode!=null)oy(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(R(166));if(n=Vn(Bi.current),Vn(Ot.current),Co(t)){if(r=t.stateNode,n=t.memoizedProps,r[It]=t,(o=r.nodeValue!==n)&&(e=tt,e!==null))switch(e.tag){case 3:So(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&So(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[It]=t,t.stateNode=r}return Me(t),null;case 13:if(ae(he),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(ce&&et!==null&&t.mode&1&&!(t.flags&128))Cg(),Ir(),t.flags|=98560,o=!1;else if(o=Co(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(R(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(R(317));o[It]=t}else Ir(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Me(t),o=!1}else Et!==null&&(ou(Et),Et=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||he.current&1?Ee===0&&(Ee=3):Sc())),t.updateQueue!==null&&(t.flags|=4),Me(t),null);case 4:return Or(),Xa(e,t),e===null&&Fi(t.stateNode.containerInfo),Me(t),null;case 10:return oc(t.type._context),Me(t),null;case 17:return Ye(t.type)&&us(),Me(t),null;case 19:if(ae(he),o=t.memoizedState,o===null)return Me(t),null;if(r=(t.flags&128)!==0,s=o.rendering,s===null)if(r)ni(o,!1);else{if(Ee!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=gs(e),s!==null){for(t.flags|=128,ni(o,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,s=o.alternate,s===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=s.childLanes,o.lanes=s.lanes,o.child=s.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=s.memoizedProps,o.memoizedState=s.memoizedState,o.updateQueue=s.updateQueue,o.type=s.type,e=s.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return oe(he,he.current&1|2),t.child}e=e.sibling}o.tail!==null&&we()>Fr&&(t.flags|=128,r=!0,ni(o,!1),t.lanes=4194304)}else{if(!r)if(e=gs(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),ni(o,!0),o.tail===null&&o.tailMode==="hidden"&&!s.alternate&&!ce)return Me(t),null}else 2*we()-o.renderingStartTime>Fr&&n!==1073741824&&(t.flags|=128,r=!0,ni(o,!1),t.lanes=4194304);o.isBackwards?(s.sibling=t.child,t.child=s):(n=o.last,n!==null?n.sibling=s:t.child=s,o.last=s)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=we(),t.sibling=null,n=he.current,oe(he,r?n&1|2:n&1),t):(Me(t),null);case 22:case 23:return kc(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Je&1073741824&&(Me(t),t.subtreeFlags&6&&(t.flags|=8192)):Me(t),null;case 24:return null;case 25:return null}throw Error(R(156,t.tag))}function kw(e,t){switch(tc(t),t.tag){case 1:return Ye(t.type)&&us(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Or(),ae(Ge),ae(Fe),cc(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return uc(t),null;case 13:if(ae(he),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(R(340));Ir()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return ae(he),null;case 4:return Or(),null;case 10:return oc(t.type._context),null;case 22:case 23:return kc(),null;case 24:return null;default:return null}}var Po=!1,_e=!1,Sw=typeof WeakSet=="function"?WeakSet:Set,j=null;function yr(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ve(e,t,r)}else n.current=null}function Za(e,t,n){try{n()}catch(r){ve(e,t,r)}}var Mh=!1;function Cw(e,t){if(Oa=os,e=cg(),Ju(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var s=0,l=-1,a=-1,u=0,c=0,f=e,h=null;t:for(;;){for(var d;f!==n||i!==0&&f.nodeType!==3||(l=s+i),f!==o||r!==0&&f.nodeType!==3||(a=s+r),f.nodeType===3&&(s+=f.nodeValue.length),(d=f.firstChild)!==null;)h=f,f=d;for(;;){if(f===e)break t;if(h===n&&++u===i&&(l=s),h===o&&++c===r&&(a=s),(d=f.nextSibling)!==null)break;f=h,h=f.parentNode}f=d}n=l===-1||a===-1?null:{start:l,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(za={focusedElem:e,selectionRange:n},os=!1,j=t;j!==null;)if(t=j,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,j=e;else for(;j!==null;){t=j;try{var g=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(g!==null){var v=g.memoizedProps,w=g.memoizedState,p=t.stateNode,m=p.getSnapshotBeforeUpdate(t.elementType===t.type?v:St(t.type,v),w);p.__reactInternalSnapshotBeforeUpdate=m}break;case 3:var y=t.stateNode.containerInfo;y.nodeType===1?y.textContent="":y.nodeType===9&&y.documentElement&&y.removeChild(y.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(R(163))}}catch(C){ve(t,t.return,C)}if(e=t.sibling,e!==null){e.return=t.return,j=e;break}j=t.return}return g=Mh,Mh=!1,g}function wi(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var o=i.destroy;i.destroy=void 0,o!==void 0&&Za(t,n,o)}i=i.next}while(i!==r)}}function Qs(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Ja(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function sy(e){var t=e.alternate;t!==null&&(e.alternate=null,sy(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[It],delete t[ji],delete t[ja],delete t[ow],delete t[sw])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function ly(e){return e.tag===5||e.tag===3||e.tag===4}function Ih(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||ly(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function eu(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=as));else if(r!==4&&(e=e.child,e!==null))for(eu(e,t,n),e=e.sibling;e!==null;)eu(e,t,n),e=e.sibling}function tu(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(tu(e,t,n),e=e.sibling;e!==null;)tu(e,t,n),e=e.sibling}var Ae=null,Ct=!1;function nn(e,t,n){for(n=n.child;n!==null;)ay(e,t,n),n=n.sibling}function ay(e,t,n){if(_t&&typeof _t.onCommitFiberUnmount=="function")try{_t.onCommitFiberUnmount(js,n)}catch{}switch(n.tag){case 5:_e||yr(n,t);case 6:var r=Ae,i=Ct;Ae=null,nn(e,t,n),Ae=r,Ct=i,Ae!==null&&(Ct?(e=Ae,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Ae.removeChild(n.stateNode));break;case 18:Ae!==null&&(Ct?(e=Ae,n=n.stateNode,e.nodeType===8?Al(e.parentNode,n):e.nodeType===1&&Al(e,n),_i(e)):Al(Ae,n.stateNode));break;case 4:r=Ae,i=Ct,Ae=n.stateNode.containerInfo,Ct=!0,nn(e,t,n),Ae=r,Ct=i;break;case 0:case 11:case 14:case 15:if(!_e&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var o=i,s=o.destroy;o=o.tag,s!==void 0&&(o&2||o&4)&&Za(n,t,s),i=i.next}while(i!==r)}nn(e,t,n);break;case 1:if(!_e&&(yr(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){ve(n,t,l)}nn(e,t,n);break;case 21:nn(e,t,n);break;case 22:n.mode&1?(_e=(r=_e)||n.memoizedState!==null,nn(e,t,n),_e=r):nn(e,t,n);break;default:nn(e,t,n)}}function _h(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Sw),t.forEach(function(r){var i=Mw.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function wt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var o=e,s=t,l=s;e:for(;l!==null;){switch(l.tag){case 5:Ae=l.stateNode,Ct=!1;break e;case 3:Ae=l.stateNode.containerInfo,Ct=!0;break e;case 4:Ae=l.stateNode.containerInfo,Ct=!0;break e}l=l.return}if(Ae===null)throw Error(R(160));ay(o,s,i),Ae=null,Ct=!1;var a=i.alternate;a!==null&&(a.return=null),i.return=null}catch(u){ve(i,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)uy(t,e),t=t.sibling}function uy(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(wt(t,e),bt(e),r&4){try{wi(3,e,e.return),Qs(3,e)}catch(v){ve(e,e.return,v)}try{wi(5,e,e.return)}catch(v){ve(e,e.return,v)}}break;case 1:wt(t,e),bt(e),r&512&&n!==null&&yr(n,n.return);break;case 5:if(wt(t,e),bt(e),r&512&&n!==null&&yr(n,n.return),e.flags&32){var i=e.stateNode;try{bi(i,"")}catch(v){ve(e,e.return,v)}}if(r&4&&(i=e.stateNode,i!=null)){var o=e.memoizedProps,s=n!==null?n.memoizedProps:o,l=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{l==="input"&&o.type==="radio"&&o.name!=null&&Rm(i,o),Ta(l,s);var u=Ta(l,o);for(s=0;s<a.length;s+=2){var c=a[s],f=a[s+1];c==="style"?zm(i,f):c==="dangerouslySetInnerHTML"?_m(i,f):c==="children"?bi(i,f):Vu(i,c,f,u)}switch(l){case"input":wa(i,o);break;case"textarea":Mm(i,o);break;case"select":var h=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!o.multiple;var d=o.value;d!=null?Er(i,!!o.multiple,d,!1):h!==!!o.multiple&&(o.defaultValue!=null?Er(i,!!o.multiple,o.defaultValue,!0):Er(i,!!o.multiple,o.multiple?[]:"",!1))}i[ji]=o}catch(v){ve(e,e.return,v)}}break;case 6:if(wt(t,e),bt(e),r&4){if(e.stateNode===null)throw Error(R(162));i=e.stateNode,o=e.memoizedProps;try{i.nodeValue=o}catch(v){ve(e,e.return,v)}}break;case 3:if(wt(t,e),bt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{_i(t.containerInfo)}catch(v){ve(e,e.return,v)}break;case 4:wt(t,e),bt(e);break;case 13:wt(t,e),bt(e),i=e.child,i.flags&8192&&(o=i.memoizedState!==null,i.stateNode.isHidden=o,!o||i.alternate!==null&&i.alternate.memoizedState!==null||(xc=we())),r&4&&_h(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(_e=(u=_e)||c,wt(t,e),_e=u):wt(t,e),bt(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(j=e,c=e.child;c!==null;){for(f=j=c;j!==null;){switch(h=j,d=h.child,h.tag){case 0:case 11:case 14:case 15:wi(4,h,h.return);break;case 1:yr(h,h.return);var g=h.stateNode;if(typeof g.componentWillUnmount=="function"){r=h,n=h.return;try{t=r,g.props=t.memoizedProps,g.state=t.memoizedState,g.componentWillUnmount()}catch(v){ve(r,n,v)}}break;case 5:yr(h,h.return);break;case 22:if(h.memoizedState!==null){zh(f);continue}}d!==null?(d.return=h,j=d):zh(f)}c=c.sibling}e:for(c=null,f=e;;){if(f.tag===5){if(c===null){c=f;try{i=f.stateNode,u?(o=i.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(l=f.stateNode,a=f.memoizedProps.style,s=a!=null&&a.hasOwnProperty("display")?a.display:null,l.style.display=Om("display",s))}catch(v){ve(e,e.return,v)}}}else if(f.tag===6){if(c===null)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(v){ve(e,e.return,v)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;c===f&&(c=null),f=f.return}c===f&&(c=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:wt(t,e),bt(e),r&4&&_h(e);break;case 21:break;default:wt(t,e),bt(e)}}function bt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(ly(n)){var r=n;break e}n=n.return}throw Error(R(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(bi(i,""),r.flags&=-33);var o=Ih(e);tu(e,o,i);break;case 3:case 4:var s=r.stateNode.containerInfo,l=Ih(e);eu(e,l,s);break;default:throw Error(R(161))}}catch(a){ve(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Ew(e,t,n){j=e,cy(e)}function cy(e,t,n){for(var r=(e.mode&1)!==0;j!==null;){var i=j,o=i.child;if(i.tag===22&&r){var s=i.memoizedState!==null||Po;if(!s){var l=i.alternate,a=l!==null&&l.memoizedState!==null||_e;l=Po;var u=_e;if(Po=s,(_e=a)&&!u)for(j=i;j!==null;)s=j,a=s.child,s.tag===22&&s.memoizedState!==null?Fh(i):a!==null?(a.return=s,j=a):Fh(i);for(;o!==null;)j=o,cy(o),o=o.sibling;j=i,Po=l,_e=u}Oh(e)}else i.subtreeFlags&8772&&o!==null?(o.return=i,j=o):Oh(e)}}function Oh(e){for(;j!==null;){var t=j;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:_e||Qs(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!_e)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:St(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&xh(t,o,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}xh(t,s,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var f=c.dehydrated;f!==null&&_i(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(R(163))}_e||t.flags&512&&Ja(t)}catch(h){ve(t,t.return,h)}}if(t===e){j=null;break}if(n=t.sibling,n!==null){n.return=t.return,j=n;break}j=t.return}}function zh(e){for(;j!==null;){var t=j;if(t===e){j=null;break}var n=t.sibling;if(n!==null){n.return=t.return,j=n;break}j=t.return}}function Fh(e){for(;j!==null;){var t=j;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Qs(4,t)}catch(a){ve(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(a){ve(t,i,a)}}var o=t.return;try{Ja(t)}catch(a){ve(t,o,a)}break;case 5:var s=t.return;try{Ja(t)}catch(a){ve(t,s,a)}}}catch(a){ve(t,t.return,a)}if(t===e){j=null;break}var l=t.sibling;if(l!==null){l.return=t.return,j=l;break}j=t.return}}var Tw=Math.ceil,xs=Jt.ReactCurrentDispatcher,yc=Jt.ReactCurrentOwner,dt=Jt.ReactCurrentBatchConfig,Z=0,Pe=null,Se=null,Le=0,Je=0,vr=Pn(0),Ee=0,Wi=null,Gn=0,Gs=0,vc=0,ki=null,Ke=null,xc=0,Fr=1/0,Bt=null,ws=!1,nu=null,vn=null,Ao=!1,fn=null,ks=0,Si=0,ru=null,Ko=-1,Qo=0;function Ue(){return Z&6?we():Ko!==-1?Ko:Ko=we()}function xn(e){return e.mode&1?Z&2&&Le!==0?Le&-Le:aw.transition!==null?(Qo===0&&(Qo=Gm()),Qo):(e=te,e!==0||(e=window.event,e=e===void 0?16:tg(e.type)),e):1}function Pt(e,t,n,r){if(50<Si)throw Si=0,ru=null,Error(R(185));Ji(e,n,r),(!(Z&2)||e!==Pe)&&(e===Pe&&(!(Z&2)&&(Gs|=n),Ee===4&&un(e,Le)),qe(e,r),n===1&&Z===0&&!(t.mode&1)&&(Fr=we()+500,Hs&&An()))}function qe(e,t){var n=e.callbackNode;ax(e,t);var r=is(e,e===Pe?Le:0);if(r===0)n!==null&&Kf(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Kf(n),t===1)e.tag===0?lw(Nh.bind(null,e)):wg(Nh.bind(null,e)),rw(function(){!(Z&6)&&An()}),n=null;else{switch(Ym(r)){case 1:n=Wu;break;case 4:n=Km;break;case 16:n=rs;break;case 536870912:n=Qm;break;default:n=rs}n=vy(n,fy.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function fy(e,t){if(Ko=-1,Qo=0,Z&6)throw Error(R(327));var n=e.callbackNode;if(Lr()&&e.callbackNode!==n)return null;var r=is(e,e===Pe?Le:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Ss(e,r);else{t=r;var i=Z;Z|=2;var o=dy();(Pe!==e||Le!==t)&&(Bt=null,Fr=we()+500,Un(e,t));do try{Dw();break}catch(l){hy(e,l)}while(!0);ic(),xs.current=o,Z=i,Se!==null?t=0:(Pe=null,Le=0,t=Ee)}if(t!==0){if(t===2&&(i=ba(e),i!==0&&(r=i,t=iu(e,i))),t===1)throw n=Wi,Un(e,0),un(e,r),qe(e,we()),n;if(t===6)un(e,r);else{if(i=e.current.alternate,!(r&30)&&!Pw(i)&&(t=Ss(e,r),t===2&&(o=ba(e),o!==0&&(r=o,t=iu(e,o))),t===1))throw n=Wi,Un(e,0),un(e,r),qe(e,we()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(R(345));case 2:_n(e,Ke,Bt);break;case 3:if(un(e,r),(r&130023424)===r&&(t=xc+500-we(),10<t)){if(is(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){Ue(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=Na(_n.bind(null,e,Ke,Bt),t);break}_n(e,Ke,Bt);break;case 4:if(un(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var s=31-Tt(r);o=1<<s,s=t[s],s>i&&(i=s),r&=~o}if(r=i,r=we()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Tw(r/1960))-r,10<r){e.timeoutHandle=Na(_n.bind(null,e,Ke,Bt),r);break}_n(e,Ke,Bt);break;case 5:_n(e,Ke,Bt);break;default:throw Error(R(329))}}}return qe(e,we()),e.callbackNode===n?fy.bind(null,e):null}function iu(e,t){var n=ki;return e.current.memoizedState.isDehydrated&&(Un(e,t).flags|=256),e=Ss(e,t),e!==2&&(t=Ke,Ke=n,t!==null&&ou(t)),e}function ou(e){Ke===null?Ke=e:Ke.push.apply(Ke,e)}function Pw(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],o=i.getSnapshot;i=i.value;try{if(!Dt(o(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function un(e,t){for(t&=~vc,t&=~Gs,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Tt(t),r=1<<n;e[n]=-1,t&=~r}}function Nh(e){if(Z&6)throw Error(R(327));Lr();var t=is(e,0);if(!(t&1))return qe(e,we()),null;var n=Ss(e,t);if(e.tag!==0&&n===2){var r=ba(e);r!==0&&(t=r,n=iu(e,r))}if(n===1)throw n=Wi,Un(e,0),un(e,t),qe(e,we()),n;if(n===6)throw Error(R(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,_n(e,Ke,Bt),qe(e,we()),null}function wc(e,t){var n=Z;Z|=1;try{return e(t)}finally{Z=n,Z===0&&(Fr=we()+500,Hs&&An())}}function Yn(e){fn!==null&&fn.tag===0&&!(Z&6)&&Lr();var t=Z;Z|=1;var n=dt.transition,r=te;try{if(dt.transition=null,te=1,e)return e()}finally{te=r,dt.transition=n,Z=t,!(Z&6)&&An()}}function kc(){Je=vr.current,ae(vr)}function Un(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,nw(n)),Se!==null)for(n=Se.return;n!==null;){var r=n;switch(tc(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&us();break;case 3:Or(),ae(Ge),ae(Fe),cc();break;case 5:uc(r);break;case 4:Or();break;case 13:ae(he);break;case 19:ae(he);break;case 10:oc(r.type._context);break;case 22:case 23:kc()}n=n.return}if(Pe=e,Se=e=wn(e.current,null),Le=Je=t,Ee=0,Wi=null,vc=Gs=Gn=0,Ke=ki=null,jn!==null){for(t=0;t<jn.length;t++)if(n=jn[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,o=n.pending;if(o!==null){var s=o.next;o.next=i,r.next=s}n.pending=r}jn=null}return e}function hy(e,t){do{var n=Se;try{if(ic(),$o.current=vs,ys){for(var r=pe.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}ys=!1}if(Qn=0,Te=Ce=pe=null,xi=!1,Ui=0,yc.current=null,n===null||n.return===null){Ee=1,Wi=t,Se=null;break}e:{var o=e,s=n.return,l=n,a=t;if(t=Le,l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,c=l,f=c.tag;if(!(c.mode&1)&&(f===0||f===11||f===15)){var h=c.alternate;h?(c.updateQueue=h.updateQueue,c.memoizedState=h.memoizedState,c.lanes=h.lanes):(c.updateQueue=null,c.memoizedState=null)}var d=Th(s);if(d!==null){d.flags&=-257,Ph(d,s,l,o,t),d.mode&1&&Eh(o,u,t),t=d,a=u;var g=t.updateQueue;if(g===null){var v=new Set;v.add(a),t.updateQueue=v}else g.add(a);break e}else{if(!(t&1)){Eh(o,u,t),Sc();break e}a=Error(R(426))}}else if(ce&&l.mode&1){var w=Th(s);if(w!==null){!(w.flags&65536)&&(w.flags|=256),Ph(w,s,l,o,t),nc(zr(a,l));break e}}o=a=zr(a,l),Ee!==4&&(Ee=2),ki===null?ki=[o]:ki.push(o),o=s;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var p=Yg(o,a,t);vh(o,p);break e;case 1:l=a;var m=o.type,y=o.stateNode;if(!(o.flags&128)&&(typeof m.getDerivedStateFromError=="function"||y!==null&&typeof y.componentDidCatch=="function"&&(vn===null||!vn.has(y)))){o.flags|=65536,t&=-t,o.lanes|=t;var C=qg(o,l,t);vh(o,C);break e}}o=o.return}while(o!==null)}my(n)}catch(T){t=T,Se===n&&n!==null&&(Se=n=n.return);continue}break}while(!0)}function dy(){var e=xs.current;return xs.current=vs,e===null?vs:e}function Sc(){(Ee===0||Ee===3||Ee===2)&&(Ee=4),Pe===null||!(Gn&268435455)&&!(Gs&268435455)||un(Pe,Le)}function Ss(e,t){var n=Z;Z|=2;var r=dy();(Pe!==e||Le!==t)&&(Bt=null,Un(e,t));do try{Aw();break}catch(i){hy(e,i)}while(!0);if(ic(),Z=n,xs.current=r,Se!==null)throw Error(R(261));return Pe=null,Le=0,Ee}function Aw(){for(;Se!==null;)py(Se)}function Dw(){for(;Se!==null&&!J1();)py(Se)}function py(e){var t=yy(e.alternate,e,Je);e.memoizedProps=e.pendingProps,t===null?my(e):Se=t,yc.current=null}function my(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=kw(n,t),n!==null){n.flags&=32767,Se=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Ee=6,Se=null;return}}else if(n=ww(n,t,Je),n!==null){Se=n;return}if(t=t.sibling,t!==null){Se=t;return}Se=t=e}while(t!==null);Ee===0&&(Ee=5)}function _n(e,t,n){var r=te,i=dt.transition;try{dt.transition=null,te=1,Lw(e,t,n,r)}finally{dt.transition=i,te=r}return null}function Lw(e,t,n,r){do Lr();while(fn!==null);if(Z&6)throw Error(R(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(R(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(ux(e,o),e===Pe&&(Se=Pe=null,Le=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Ao||(Ao=!0,vy(rs,function(){return Lr(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=dt.transition,dt.transition=null;var s=te;te=1;var l=Z;Z|=4,yc.current=null,Cw(e,n),uy(n,e),Yx(za),os=!!Oa,za=Oa=null,e.current=n,Ew(n),ex(),Z=l,te=s,dt.transition=o}else e.current=n;if(Ao&&(Ao=!1,fn=e,ks=i),o=e.pendingLanes,o===0&&(vn=null),rx(n.stateNode),qe(e,we()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(ws)throw ws=!1,e=nu,nu=null,e;return ks&1&&e.tag!==0&&Lr(),o=e.pendingLanes,o&1?e===ru?Si++:(Si=0,ru=e):Si=0,An(),null}function Lr(){if(fn!==null){var e=Ym(ks),t=dt.transition,n=te;try{if(dt.transition=null,te=16>e?16:e,fn===null)var r=!1;else{if(e=fn,fn=null,ks=0,Z&6)throw Error(R(331));var i=Z;for(Z|=4,j=e.current;j!==null;){var o=j,s=o.child;if(j.flags&16){var l=o.deletions;if(l!==null){for(var a=0;a<l.length;a++){var u=l[a];for(j=u;j!==null;){var c=j;switch(c.tag){case 0:case 11:case 15:wi(8,c,o)}var f=c.child;if(f!==null)f.return=c,j=f;else for(;j!==null;){c=j;var h=c.sibling,d=c.return;if(sy(c),c===u){j=null;break}if(h!==null){h.return=d,j=h;break}j=d}}}var g=o.alternate;if(g!==null){var v=g.child;if(v!==null){g.child=null;do{var w=v.sibling;v.sibling=null,v=w}while(v!==null)}}j=o}}if(o.subtreeFlags&2064&&s!==null)s.return=o,j=s;else e:for(;j!==null;){if(o=j,o.flags&2048)switch(o.tag){case 0:case 11:case 15:wi(9,o,o.return)}var p=o.sibling;if(p!==null){p.return=o.return,j=p;break e}j=o.return}}var m=e.current;for(j=m;j!==null;){s=j;var y=s.child;if(s.subtreeFlags&2064&&y!==null)y.return=s,j=y;else e:for(s=m;j!==null;){if(l=j,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:Qs(9,l)}}catch(T){ve(l,l.return,T)}if(l===s){j=null;break e}var C=l.sibling;if(C!==null){C.return=l.return,j=C;break e}j=l.return}}if(Z=i,An(),_t&&typeof _t.onPostCommitFiberRoot=="function")try{_t.onPostCommitFiberRoot(js,e)}catch{}r=!0}return r}finally{te=n,dt.transition=t}}return!1}function jh(e,t,n){t=zr(n,t),t=Yg(e,t,1),e=yn(e,t,1),t=Ue(),e!==null&&(Ji(e,1,t),qe(e,t))}function ve(e,t,n){if(e.tag===3)jh(e,e,n);else for(;t!==null;){if(t.tag===3){jh(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(vn===null||!vn.has(r))){e=zr(n,e),e=qg(t,e,1),t=yn(t,e,1),e=Ue(),t!==null&&(Ji(t,1,e),qe(t,e));break}}t=t.return}}function bw(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Ue(),e.pingedLanes|=e.suspendedLanes&n,Pe===e&&(Le&n)===n&&(Ee===4||Ee===3&&(Le&130023424)===Le&&500>we()-xc?Un(e,0):vc|=n),qe(e,t)}function gy(e,t){t===0&&(e.mode&1?(t=yo,yo<<=1,!(yo&130023424)&&(yo=4194304)):t=1);var n=Ue();e=qt(e,t),e!==null&&(Ji(e,t,n),qe(e,n))}function Rw(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),gy(e,n)}function Mw(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(R(314))}r!==null&&r.delete(t),gy(e,n)}var yy;yy=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ge.current)Qe=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Qe=!1,xw(e,t,n);Qe=!!(e.flags&131072)}else Qe=!1,ce&&t.flags&1048576&&kg(t,hs,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Wo(e,t),e=t.pendingProps;var i=Mr(t,Fe.current);Dr(t,n),i=hc(null,t,r,e,i,n);var o=dc();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ye(r)?(o=!0,cs(t)):o=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,lc(t),i.updater=Ks,t.stateNode=i,i._reactInternals=t,Wa(t,r,e,n),t=Ga(null,t,r,!0,o,n)):(t.tag=0,ce&&o&&ec(t),Ve(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Wo(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=_w(r),e=St(r,e),i){case 0:t=Qa(null,t,r,e,n);break e;case 1:t=Lh(null,t,r,e,n);break e;case 11:t=Ah(null,t,r,e,n);break e;case 14:t=Dh(null,t,r,St(r.type,e),n);break e}throw Error(R(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:St(r,i),Qa(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:St(r,i),Lh(e,t,r,i,n);case 3:e:{if(ey(t),e===null)throw Error(R(387));r=t.pendingProps,o=t.memoizedState,i=o.element,Ag(e,t),ms(t,r,null,n);var s=t.memoizedState;if(r=s.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){i=zr(Error(R(423)),t),t=bh(e,t,r,n,i);break e}else if(r!==i){i=zr(Error(R(424)),t),t=bh(e,t,r,n,i);break e}else for(et=gn(t.stateNode.containerInfo.firstChild),tt=t,ce=!0,Et=null,n=Tg(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Ir(),r===i){t=Xt(e,t,n);break e}Ve(e,t,r,n)}t=t.child}return t;case 5:return Dg(t),e===null&&Ua(t),r=t.type,i=t.pendingProps,o=e!==null?e.memoizedProps:null,s=i.children,Fa(r,i)?s=null:o!==null&&Fa(r,o)&&(t.flags|=32),Jg(e,t),Ve(e,t,s,n),t.child;case 6:return e===null&&Ua(t),null;case 13:return ty(e,t,n);case 4:return ac(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=_r(t,null,r,n):Ve(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:St(r,i),Ah(e,t,r,i,n);case 7:return Ve(e,t,t.pendingProps,n),t.child;case 8:return Ve(e,t,t.pendingProps.children,n),t.child;case 12:return Ve(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,o=t.memoizedProps,s=i.value,oe(ds,r._currentValue),r._currentValue=s,o!==null)if(Dt(o.value,s)){if(o.children===i.children&&!Ge.current){t=Xt(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var l=o.dependencies;if(l!==null){s=o.child;for(var a=l.firstContext;a!==null;){if(a.context===r){if(o.tag===1){a=Wt(-1,n&-n),a.tag=2;var u=o.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?a.next=a:(a.next=c.next,c.next=a),u.pending=a}}o.lanes|=n,a=o.alternate,a!==null&&(a.lanes|=n),$a(o.return,n,t),l.lanes|=n;break}a=a.next}}else if(o.tag===10)s=o.type===t.type?null:o.child;else if(o.tag===18){if(s=o.return,s===null)throw Error(R(341));s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),$a(s,n,t),s=o.sibling}else s=o.child;if(s!==null)s.return=o;else for(s=o;s!==null;){if(s===t){s=null;break}if(o=s.sibling,o!==null){o.return=s.return,s=o;break}s=s.return}o=s}Ve(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,Dr(t,n),i=mt(i),r=r(i),t.flags|=1,Ve(e,t,r,n),t.child;case 14:return r=t.type,i=St(r,t.pendingProps),i=St(r.type,i),Dh(e,t,r,i,n);case 15:return Xg(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:St(r,i),Wo(e,t),t.tag=1,Ye(r)?(e=!0,cs(t)):e=!1,Dr(t,n),Gg(t,r,i),Wa(t,r,i,n),Ga(null,t,r,!0,e,n);case 19:return ny(e,t,n);case 22:return Zg(e,t,n)}throw Error(R(156,t.tag))};function vy(e,t){return Wm(e,t)}function Iw(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ht(e,t,n,r){return new Iw(e,t,n,r)}function Cc(e){return e=e.prototype,!(!e||!e.isReactComponent)}function _w(e){if(typeof e=="function")return Cc(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Uu)return 11;if(e===$u)return 14}return 2}function wn(e,t){var n=e.alternate;return n===null?(n=ht(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Go(e,t,n,r,i,o){var s=2;if(r=e,typeof e=="function")Cc(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case ar:return $n(n.children,i,o,t);case Bu:s=8,i|=8;break;case ma:return e=ht(12,n,t,i|2),e.elementType=ma,e.lanes=o,e;case ga:return e=ht(13,n,t,i),e.elementType=ga,e.lanes=o,e;case ya:return e=ht(19,n,t,i),e.elementType=ya,e.lanes=o,e;case Dm:return Ys(n,i,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Pm:s=10;break e;case Am:s=9;break e;case Uu:s=11;break e;case $u:s=14;break e;case sn:s=16,r=null;break e}throw Error(R(130,e==null?e:typeof e,""))}return t=ht(s,n,t,i),t.elementType=e,t.type=r,t.lanes=o,t}function $n(e,t,n,r){return e=ht(7,e,r,t),e.lanes=n,e}function Ys(e,t,n,r){return e=ht(22,e,r,t),e.elementType=Dm,e.lanes=n,e.stateNode={isHidden:!1},e}function Ol(e,t,n){return e=ht(6,e,null,t),e.lanes=n,e}function zl(e,t,n){return t=ht(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Ow(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=gl(0),this.expirationTimes=gl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gl(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function Ec(e,t,n,r,i,o,s,l,a){return e=new Ow(e,t,n,l,a),t===1?(t=1,o===!0&&(t|=8)):t=0,o=ht(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},lc(o),e}function zw(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:lr,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function xy(e){if(!e)return Sn;e=e._reactInternals;e:{if(Zn(e)!==e||e.tag!==1)throw Error(R(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ye(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(R(171))}if(e.tag===1){var n=e.type;if(Ye(n))return xg(e,n,t)}return t}function wy(e,t,n,r,i,o,s,l,a){return e=Ec(n,r,!0,e,i,o,s,l,a),e.context=xy(null),n=e.current,r=Ue(),i=xn(n),o=Wt(r,i),o.callback=t??null,yn(n,o,i),e.current.lanes=i,Ji(e,i,r),qe(e,r),e}function qs(e,t,n,r){var i=t.current,o=Ue(),s=xn(i);return n=xy(n),t.context===null?t.context=n:t.pendingContext=n,t=Wt(o,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=yn(i,t,s),e!==null&&(Pt(e,i,s,o),Uo(e,i,s)),s}function Cs(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Vh(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Tc(e,t){Vh(e,t),(e=e.alternate)&&Vh(e,t)}function Fw(){return null}var ky=typeof reportError=="function"?reportError:function(e){console.error(e)};function Pc(e){this._internalRoot=e}Xs.prototype.render=Pc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(R(409));qs(e,t,null,null)};Xs.prototype.unmount=Pc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Yn(function(){qs(null,e,null,null)}),t[Yt]=null}};function Xs(e){this._internalRoot=e}Xs.prototype.unstable_scheduleHydration=function(e){if(e){var t=Zm();e={blockedOn:null,target:e,priority:t};for(var n=0;n<an.length&&t!==0&&t<an[n].priority;n++);an.splice(n,0,e),n===0&&eg(e)}};function Ac(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Zs(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Bh(){}function Nw(e,t,n,r,i){if(i){if(typeof r=="function"){var o=r;r=function(){var u=Cs(s);o.call(u)}}var s=wy(t,r,e,0,null,!1,!1,"",Bh);return e._reactRootContainer=s,e[Yt]=s.current,Fi(e.nodeType===8?e.parentNode:e),Yn(),s}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var l=r;r=function(){var u=Cs(a);l.call(u)}}var a=Ec(e,0,!1,null,null,!1,!1,"",Bh);return e._reactRootContainer=a,e[Yt]=a.current,Fi(e.nodeType===8?e.parentNode:e),Yn(function(){qs(t,a,n,r)}),a}function Js(e,t,n,r,i){var o=n._reactRootContainer;if(o){var s=o;if(typeof i=="function"){var l=i;i=function(){var a=Cs(s);l.call(a)}}qs(t,s,e,i)}else s=Nw(n,t,e,i,r);return Cs(s)}qm=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=ui(t.pendingLanes);n!==0&&(Ku(t,n|1),qe(t,we()),!(Z&6)&&(Fr=we()+500,An()))}break;case 13:Yn(function(){var r=qt(e,1);if(r!==null){var i=Ue();Pt(r,e,1,i)}}),Tc(e,1)}};Qu=function(e){if(e.tag===13){var t=qt(e,134217728);if(t!==null){var n=Ue();Pt(t,e,134217728,n)}Tc(e,134217728)}};Xm=function(e){if(e.tag===13){var t=xn(e),n=qt(e,t);if(n!==null){var r=Ue();Pt(n,e,t,r)}Tc(e,t)}};Zm=function(){return te};Jm=function(e,t){var n=te;try{return te=e,t()}finally{te=n}};Aa=function(e,t,n){switch(t){case"input":if(wa(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=$s(r);if(!i)throw Error(R(90));bm(r),wa(r,i)}}}break;case"textarea":Mm(e,n);break;case"select":t=n.value,t!=null&&Er(e,!!n.multiple,t,!1)}};jm=wc;Vm=Yn;var jw={usingClientEntryPoint:!1,Events:[to,hr,$s,Fm,Nm,wc]},ri={findFiberByHostInstance:Nn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Vw={bundleType:ri.bundleType,version:ri.version,rendererPackageName:ri.rendererPackageName,rendererConfig:ri.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Jt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=$m(e),e===null?null:e.stateNode},findFiberByHostInstance:ri.findFiberByHostInstance||Fw,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Do=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Do.isDisabled&&Do.supportsFiber)try{js=Do.inject(Vw),_t=Do}catch{}}ot.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=jw;ot.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Ac(t))throw Error(R(200));return zw(e,t,null,n)};ot.createRoot=function(e,t){if(!Ac(e))throw Error(R(299));var n=!1,r="",i=ky;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=Ec(e,1,!1,null,null,n,!1,r,i),e[Yt]=t.current,Fi(e.nodeType===8?e.parentNode:e),new Pc(t)};ot.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(R(188)):(e=Object.keys(e).join(","),Error(R(268,e)));return e=$m(t),e=e===null?null:e.stateNode,e};ot.flushSync=function(e){return Yn(e)};ot.hydrate=function(e,t,n){if(!Zs(t))throw Error(R(200));return Js(null,e,t,!0,n)};ot.hydrateRoot=function(e,t,n){if(!Ac(e))throw Error(R(405));var r=n!=null&&n.hydratedSources||null,i=!1,o="",s=ky;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=wy(t,null,e,1,n??null,i,!1,o,s),e[Yt]=t.current,Fi(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new Xs(t)};ot.render=function(e,t,n){if(!Zs(t))throw Error(R(200));return Js(null,e,t,!1,n)};ot.unmountComponentAtNode=function(e){if(!Zs(e))throw Error(R(40));return e._reactRootContainer?(Yn(function(){Js(null,null,e,!1,function(){e._reactRootContainer=null,e[Yt]=null})}),!0):!1};ot.unstable_batchedUpdates=wc;ot.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Zs(n))throw Error(R(200));if(e==null||e._reactInternals===void 0)throw Error(R(38));return Js(e,t,n,!1,r)};ot.version="18.3.1-next-f1338f8080-20240426";function Sy(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Sy)}catch(e){console.error(e)}}Sy(),Sm.exports=ot;var Bw=Sm.exports,Uh=Bw;da.createRoot=Uh.createRoot,da.hydrateRoot=Uh.hydrateRoot;function su(){return su=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},su.apply(null,arguments)}function Uw(e,t){if(e==null)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.includes(r))continue;n[r]=e[r]}return n}var $w=b.useLayoutEffect,Hw=function(t){var n=b.useRef(t);return $w(function(){n.current=t}),n},$h=function(t,n){if(typeof t=="function"){t(n);return}t.current=n},Ww=function(t,n){var r=b.useRef();return b.useCallback(function(i){t.current=i,r.current&&$h(r.current,null),r.current=n,n&&$h(n,i)},[n])},Hh={"min-height":"0","max-height":"none",height:"0",visibility:"hidden",overflow:"hidden",position:"absolute","z-index":"-1000",top:"0",right:"0"},Kw=function(t){Object.keys(Hh).forEach(function(n){t.style.setProperty(n,Hh[n],"important")})},Wh=Kw,je=null,Kh=function(t,n){var r=t.scrollHeight;return n.sizingStyle.boxSizing==="border-box"?r+n.borderSize:r-n.paddingSize};function Qw(e,t,n,r){n===void 0&&(n=1),r===void 0&&(r=1/0),je||(je=document.createElement("textarea"),je.setAttribute("tabindex","-1"),je.setAttribute("aria-hidden","true"),Wh(je)),je.parentNode===null&&document.body.appendChild(je);var i=e.paddingSize,o=e.borderSize,s=e.sizingStyle,l=s.boxSizing;Object.keys(s).forEach(function(h){var d=h;je.style[d]=s[d]}),Wh(je),je.value=t;var a=Kh(je,e);je.value=t,a=Kh(je,e),je.value="x";var u=je.scrollHeight-i,c=u*n;l==="border-box"&&(c=c+i+o),a=Math.max(c,a);var f=u*r;return l==="border-box"&&(f=f+i+o),a=Math.min(f,a),[a,u]}var Qh=function(){},Gw=function(t,n){return t.reduce(function(r,i){return r[i]=n[i],r},{})},Yw=["borderBottomWidth","borderLeftWidth","borderRightWidth","borderTopWidth","boxSizing","fontFamily","fontSize","fontStyle","fontWeight","letterSpacing","lineHeight","paddingBottom","paddingLeft","paddingRight","paddingTop","tabSize","textIndent","textRendering","textTransform","width","wordBreak"],qw=!!document.documentElement.currentStyle,Xw=function(t){var n=window.getComputedStyle(t);if(n===null)return null;var r=Gw(Yw,n),i=r.boxSizing;if(i==="")return null;qw&&i==="border-box"&&(r.width=parseFloat(r.width)+parseFloat(r.borderRightWidth)+parseFloat(r.borderLeftWidth)+parseFloat(r.paddingRight)+parseFloat(r.paddingLeft)+"px");var o=parseFloat(r.paddingBottom)+parseFloat(r.paddingTop),s=parseFloat(r.borderBottomWidth)+parseFloat(r.borderTopWidth);return{sizingStyle:r,paddingSize:o,borderSize:s}},Zw=Xw;function Cy(e,t,n){var r=Hw(n);b.useLayoutEffect(function(){var i=function(s){return r.current(s)};if(e)return e.addEventListener(t,i),function(){return e.removeEventListener(t,i)}},[])}var Jw=function(t){Cy(window,"resize",t)},ek=function(t){Cy(document.fonts,"loadingdone",t)},tk=["cacheMeasurements","maxRows","minRows","onChange","onHeightChange"],nk=function(t,n){var r=t.cacheMeasurements,i=t.maxRows,o=t.minRows,s=t.onChange,l=s===void 0?Qh:s,a=t.onHeightChange,u=a===void 0?Qh:a,c=Uw(t,tk),f=c.value!==void 0,h=b.useRef(null),d=Ww(h,n),g=b.useRef(0),v=b.useRef(),w=function(){var y=h.current,C=r&&v.current?v.current:Zw(y);if(C){v.current=C;var T=Qw(C,y.value||y.placeholder||"x",o,i),k=T[0],P=T[1];g.current!==k&&(g.current=k,y.style.setProperty("height",k+"px","important"),u(k,{rowHeight:P}))}},p=function(y){f||w(),l(y)};return b.useLayoutEffect(w),Jw(w),ek(w),b.createElement("textarea",su({},c,{onChange:p,ref:d}))},rk=b.forwardRef(nk),Ey={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},Gh=hn.createContext&&hn.createContext(Ey),ik=["attr","size","title"];function ok(e,t){if(e==null)return{};var n=sk(e,t),r,i;if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(i=0;i<o.length;i++)r=o[i],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}function sk(e,t){if(e==null)return{};var n={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}function Es(){return Es=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Es.apply(this,arguments)}function Yh(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),n.push.apply(n,r)}return n}function Ts(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?Yh(Object(n),!0).forEach(function(r){lk(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Yh(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function lk(e,t,n){return t=ak(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ak(e){var t=uk(e,"string");return typeof t=="symbol"?t:t+""}function uk(e,t){if(typeof e!="object"||!e)return e;var n=e[Symbol.toPrimitive];if(n!==void 0){var r=n.call(e,t||"default");if(typeof r!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Ty(e){return e&&e.map((t,n)=>hn.createElement(t.tag,Ts({key:n},t.attr),Ty(t.child)))}function Dc(e){return t=>hn.createElement(ck,Es({attr:Ts({},e.attr)},t),Ty(e.child))}function ck(e){var t=n=>{var{attr:r,size:i,title:o}=e,s=ok(e,ik),l=i||n.size||"1em",a;return n.className&&(a=n.className),e.className&&(a=(a?a+" ":"")+e.className),hn.createElement("svg",Es({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},n.attr,r,s,{className:a,style:Ts(Ts({color:e.color||n.color},n.style),e.style),height:l,width:l,xmlns:"http://www.w3.org/2000/svg"}),o&&hn.createElement("title",null,o),e.children)};return Gh!==void 0?hn.createElement(Gh.Consumer,null,n=>t(n)):t(Ey)}function fk(e){return Dc({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M412.6 227.1L278.6 89c-5.8-6-13.7-9-22.4-9h-.4c-8.7 0-16.6 3-22.4 9l-134 138.1c-12.5 12-12.5 31.3 0 43.2 12.5 11.9 32.7 11.9 45.2 0l79.4-83v214c0 16.9 14.3 30.6 32 30.6 18 0 32-13.7 32-30.6v-214l79.4 83c12.5 11.9 32.7 11.9 45.2 0s12.5-31.2 0-43.2z"},child:[]}]})(e)}function hk(e,t){const n={};return(e[e.length-1]===""?[...e,""]:e).join((n.padRight?" ":"")+","+(n.padLeft===!1?"":" ")).trim()}const dk=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,pk=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,mk={};function qh(e,t){return(mk.jsx?pk:dk).test(e)}const gk=/[ \t\n\f\r]/g;function yk(e){return typeof e=="object"?e.type==="text"?Xh(e.value):!1:Xh(e)}function Xh(e){return e.replace(gk,"")===""}class ro{constructor(t,n,r){this.property=t,this.normal=n,r&&(this.space=r)}}ro.prototype.property={};ro.prototype.normal={};ro.prototype.space=null;function Py(e,t){const n={},r={};let i=-1;for(;++i<e.length;)Object.assign(n,e[i].property),Object.assign(r,e[i].normal);return new ro(n,r,t)}function lu(e){return e.toLowerCase()}class yt{constructor(t,n){this.property=t,this.attribute=n}}yt.prototype.space=null;yt.prototype.boolean=!1;yt.prototype.booleanish=!1;yt.prototype.overloadedBoolean=!1;yt.prototype.number=!1;yt.prototype.commaSeparated=!1;yt.prototype.spaceSeparated=!1;yt.prototype.commaOrSpaceSeparated=!1;yt.prototype.mustUseProperty=!1;yt.prototype.defined=!1;let vk=0;const H=Jn(),ke=Jn(),Ay=Jn(),M=Jn(),ie=Jn(),br=Jn(),Ze=Jn();function Jn(){return 2**++vk}const au=Object.freeze(Object.defineProperty({__proto__:null,boolean:H,booleanish:ke,commaOrSpaceSeparated:Ze,commaSeparated:br,number:M,overloadedBoolean:Ay,spaceSeparated:ie},Symbol.toStringTag,{value:"Module"})),Fl=Object.keys(au);class Lc extends yt{constructor(t,n,r,i){let o=-1;if(super(t,n),Zh(this,"space",i),typeof r=="number")for(;++o<Fl.length;){const s=Fl[o];Zh(this,Fl[o],(r&au[s])===au[s])}}}Lc.prototype.defined=!0;function Zh(e,t,n){n&&(e[t]=n)}const xk={}.hasOwnProperty;function Hr(e){const t={},n={};let r;for(r in e.properties)if(xk.call(e.properties,r)){const i=e.properties[r],o=new Lc(r,e.transform(e.attributes||{},r),i,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(o.mustUseProperty=!0),t[r]=o,n[lu(r)]=r,n[lu(o.attribute)]=r}return new ro(t,n,e.space)}const Dy=Hr({space:"xlink",transform(e,t){return"xlink:"+t.slice(5).toLowerCase()},properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null}}),Ly=Hr({space:"xml",transform(e,t){return"xml:"+t.slice(3).toLowerCase()},properties:{xmlLang:null,xmlBase:null,xmlSpace:null}});function by(e,t){return t in e?e[t]:t}function Ry(e,t){return by(e,t.toLowerCase())}const My=Hr({space:"xmlns",attributes:{xmlnsxlink:"xmlns:xlink"},transform:Ry,properties:{xmlns:null,xmlnsXLink:null}}),Iy=Hr({transform(e,t){return t==="role"?t:"aria-"+t.slice(4).toLowerCase()},properties:{ariaActiveDescendant:null,ariaAtomic:ke,ariaAutoComplete:null,ariaBusy:ke,ariaChecked:ke,ariaColCount:M,ariaColIndex:M,ariaColSpan:M,ariaControls:ie,ariaCurrent:null,ariaDescribedBy:ie,ariaDetails:null,ariaDisabled:ke,ariaDropEffect:ie,ariaErrorMessage:null,ariaExpanded:ke,ariaFlowTo:ie,ariaGrabbed:ke,ariaHasPopup:null,ariaHidden:ke,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:ie,ariaLevel:M,ariaLive:null,ariaModal:ke,ariaMultiLine:ke,ariaMultiSelectable:ke,ariaOrientation:null,ariaOwns:ie,ariaPlaceholder:null,ariaPosInSet:M,ariaPressed:ke,ariaReadOnly:ke,ariaRelevant:null,ariaRequired:ke,ariaRoleDescription:ie,ariaRowCount:M,ariaRowIndex:M,ariaRowSpan:M,ariaSelected:ke,ariaSetSize:M,ariaSort:null,ariaValueMax:M,ariaValueMin:M,ariaValueNow:M,ariaValueText:null,role:null}}),wk=Hr({space:"html",attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},transform:Ry,mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:br,acceptCharset:ie,accessKey:ie,action:null,allow:null,allowFullScreen:H,allowPaymentRequest:H,allowUserMedia:H,alt:null,as:null,async:H,autoCapitalize:null,autoComplete:ie,autoFocus:H,autoPlay:H,blocking:ie,capture:null,charSet:null,checked:H,cite:null,className:ie,cols:M,colSpan:null,content:null,contentEditable:ke,controls:H,controlsList:ie,coords:M|br,crossOrigin:null,data:null,dateTime:null,decoding:null,default:H,defer:H,dir:null,dirName:null,disabled:H,download:Ay,draggable:ke,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:H,formTarget:null,headers:ie,height:M,hidden:H,high:M,href:null,hrefLang:null,htmlFor:ie,httpEquiv:ie,id:null,imageSizes:null,imageSrcSet:null,inert:H,inputMode:null,integrity:null,is:null,isMap:H,itemId:null,itemProp:ie,itemRef:ie,itemScope:H,itemType:ie,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:H,low:M,manifest:null,max:null,maxLength:M,media:null,method:null,min:null,minLength:M,multiple:H,muted:H,name:null,nonce:null,noModule:H,noValidate:H,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:H,optimum:M,pattern:null,ping:ie,placeholder:null,playsInline:H,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:H,referrerPolicy:null,rel:ie,required:H,reversed:H,rows:M,rowSpan:M,sandbox:ie,scope:null,scoped:H,seamless:H,selected:H,shadowRootClonable:H,shadowRootDelegatesFocus:H,shadowRootMode:null,shape:null,size:M,sizes:null,slot:null,span:M,spellCheck:ke,src:null,srcDoc:null,srcLang:null,srcSet:null,start:M,step:null,style:null,tabIndex:M,target:null,title:null,translate:null,type:null,typeMustMatch:H,useMap:null,value:ke,width:M,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:ie,axis:null,background:null,bgColor:null,border:M,borderColor:null,bottomMargin:M,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:H,declare:H,event:null,face:null,frame:null,frameBorder:null,hSpace:M,leftMargin:M,link:null,longDesc:null,lowSrc:null,marginHeight:M,marginWidth:M,noResize:H,noHref:H,noShade:H,noWrap:H,object:null,profile:null,prompt:null,rev:null,rightMargin:M,rules:null,scheme:null,scrolling:ke,standby:null,summary:null,text:null,topMargin:M,valueType:null,version:null,vAlign:null,vLink:null,vSpace:M,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:H,disableRemotePlayback:H,prefix:null,property:null,results:M,security:null,unselectable:null}}),kk=Hr({space:"svg",attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},transform:by,properties:{about:Ze,accentHeight:M,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:M,amplitude:M,arabicForm:null,ascent:M,attributeName:null,attributeType:null,azimuth:M,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:M,by:null,calcMode:null,capHeight:M,className:ie,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:M,diffuseConstant:M,direction:null,display:null,dur:null,divisor:M,dominantBaseline:null,download:H,dx:null,dy:null,edgeMode:null,editable:null,elevation:M,enableBackground:null,end:null,event:null,exponent:M,externalResourcesRequired:null,fill:null,fillOpacity:M,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:br,g2:br,glyphName:br,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:M,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:M,horizOriginX:M,horizOriginY:M,id:null,ideographic:M,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:M,k:M,k1:M,k2:M,k3:M,k4:M,kernelMatrix:Ze,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:M,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:M,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:M,overlineThickness:M,paintOrder:null,panose1:null,path:null,pathLength:M,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:ie,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:M,pointsAtY:M,pointsAtZ:M,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:Ze,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:Ze,rev:Ze,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:Ze,requiredFeatures:Ze,requiredFonts:Ze,requiredFormats:Ze,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:M,specularExponent:M,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:M,strikethroughThickness:M,string:null,stroke:null,strokeDashArray:Ze,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:M,strokeOpacity:M,strokeWidth:null,style:null,surfaceScale:M,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:Ze,tabIndex:M,tableValues:null,target:null,targetX:M,targetY:M,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:Ze,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:M,underlineThickness:M,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:M,values:null,vAlphabetic:M,vMathematical:M,vectorEffect:null,vHanging:M,vIdeographic:M,version:null,vertAdvY:M,vertOriginX:M,vertOriginY:M,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:M,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null}}),Sk=/^data[-\w.:]+$/i,Jh=/-[a-z]/g,Ck=/[A-Z]/g;function Ek(e,t){const n=lu(t);let r=t,i=yt;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&n.slice(0,4)==="data"&&Sk.test(t)){if(t.charAt(4)==="-"){const o=t.slice(5).replace(Jh,Pk);r="data"+o.charAt(0).toUpperCase()+o.slice(1)}else{const o=t.slice(4);if(!Jh.test(o)){let s=o.replace(Ck,Tk);s.charAt(0)!=="-"&&(s="-"+s),t="data"+s}}i=Lc}return new i(r,t)}function Tk(e){return"-"+e.toLowerCase()}function Pk(e){return e.charAt(1).toUpperCase()}const Ak={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"},Dk=Py([Ly,Dy,My,Iy,wk],"html"),bc=Py([Ly,Dy,My,Iy,kk],"svg");function Lk(e){return e.join(" ").trim()}var _y={},ed=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,bk=/\n/g,Rk=/^\s*/,Mk=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,Ik=/^:\s*/,_k=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,Ok=/^[;\s]*/,zk=/^\s+|\s+$/g,Fk=`
`,td="/",nd="*",Fn="",Nk="comment",jk="declaration",Vk=function(e,t){if(typeof e!="string")throw new TypeError("First argument must be a string");if(!e)return[];t=t||{};var n=1,r=1;function i(g){var v=g.match(bk);v&&(n+=v.length);var w=g.lastIndexOf(Fk);r=~w?g.length-w:r+g.length}function o(){var g={line:n,column:r};return function(v){return v.position=new s(g),u(),v}}function s(g){this.start=g,this.end={line:n,column:r},this.source=t.source}s.prototype.content=e;function l(g){var v=new Error(t.source+":"+n+":"+r+": "+g);if(v.reason=g,v.filename=t.source,v.line=n,v.column=r,v.source=e,!t.silent)throw v}function a(g){var v=g.exec(e);if(v){var w=v[0];return i(w),e=e.slice(w.length),v}}function u(){a(Rk)}function c(g){var v;for(g=g||[];v=f();)v!==!1&&g.push(v);return g}function f(){var g=o();if(!(td!=e.charAt(0)||nd!=e.charAt(1))){for(var v=2;Fn!=e.charAt(v)&&(nd!=e.charAt(v)||td!=e.charAt(v+1));)++v;if(v+=2,Fn===e.charAt(v-1))return l("End of comment missing");var w=e.slice(2,v-2);return r+=2,i(w),e=e.slice(v),r+=2,g({type:Nk,comment:w})}}function h(){var g=o(),v=a(Mk);if(v){if(f(),!a(Ik))return l("property missing ':'");var w=a(_k),p=g({type:jk,property:rd(v[0].replace(ed,Fn)),value:w?rd(w[0].replace(ed,Fn)):Fn});return a(Ok),p}}function d(){var g=[];c(g);for(var v;v=h();)v!==!1&&(g.push(v),c(g));return g}return u(),d()};function rd(e){return e?e.replace(zk,Fn):Fn}var Bk=If&&If.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(_y,"__esModule",{value:!0});var Uk=Bk(Vk);function $k(e,t){var n=null;if(!e||typeof e!="string")return n;var r=(0,Uk.default)(e),i=typeof t=="function";return r.forEach(function(o){if(o.type==="declaration"){var s=o.property,l=o.value;i?t(s,l,o):l&&(n=n||{},n[s]=l)}}),n}var id=_y.default=$k;const Hk=id.default||id,Oy=zy("end"),Rc=zy("start");function zy(e){return t;function t(n){const r=n&&n.position&&n.position[e]||{};if(typeof r.line=="number"&&r.line>0&&typeof r.column=="number"&&r.column>0)return{line:r.line,column:r.column,offset:typeof r.offset=="number"&&r.offset>-1?r.offset:void 0}}}function Wk(e){const t=Rc(e),n=Oy(e);if(t&&n)return{start:t,end:n}}function Ci(e){return!e||typeof e!="object"?"":"position"in e||"type"in e?od(e.position):"start"in e||"end"in e?od(e):"line"in e||"column"in e?uu(e):""}function uu(e){return sd(e&&e.line)+":"+sd(e&&e.column)}function od(e){return uu(e&&e.start)+"-"+uu(e&&e.end)}function sd(e){return e&&typeof e=="number"?e:1}class Ne extends Error{constructor(t,n,r){super(),typeof n=="string"&&(r=n,n=void 0);let i="",o={},s=!1;if(n&&("line"in n&&"column"in n?o={place:n}:"start"in n&&"end"in n?o={place:n}:"type"in n?o={ancestors:[n],place:n.position}:o={...n}),typeof t=="string"?i=t:!o.cause&&t&&(s=!0,i=t.message,o.cause=t),!o.ruleId&&!o.source&&typeof r=="string"){const a=r.indexOf(":");a===-1?o.ruleId=r:(o.source=r.slice(0,a),o.ruleId=r.slice(a+1))}if(!o.place&&o.ancestors&&o.ancestors){const a=o.ancestors[o.ancestors.length-1];a&&(o.place=a.position)}const l=o.place&&"start"in o.place?o.place.start:o.place;this.ancestors=o.ancestors||void 0,this.cause=o.cause||void 0,this.column=l?l.column:void 0,this.fatal=void 0,this.file,this.message=i,this.line=l?l.line:void 0,this.name=Ci(o.place)||"1:1",this.place=o.place||void 0,this.reason=this.message,this.ruleId=o.ruleId||void 0,this.source=o.source||void 0,this.stack=s&&o.cause&&typeof o.cause.stack=="string"?o.cause.stack:"",this.actual,this.expected,this.note,this.url}}Ne.prototype.file="";Ne.prototype.name="";Ne.prototype.reason="";Ne.prototype.message="";Ne.prototype.stack="";Ne.prototype.column=void 0;Ne.prototype.line=void 0;Ne.prototype.ancestors=void 0;Ne.prototype.cause=void 0;Ne.prototype.fatal=void 0;Ne.prototype.place=void 0;Ne.prototype.ruleId=void 0;Ne.prototype.source=void 0;const Mc={}.hasOwnProperty,Kk=new Map,Qk=/[A-Z]/g,Gk=/-([a-z])/g,Yk=new Set(["table","tbody","thead","tfoot","tr"]),qk=new Set(["td","th"]),Fy="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function Xk(e,t){if(!t||t.Fragment===void 0)throw new TypeError("Expected `Fragment` in options");const n=t.filePath||void 0;let r;if(t.development){if(typeof t.jsxDEV!="function")throw new TypeError("Expected `jsxDEV` in options when `development: true`");r=oS(n,t.jsxDEV)}else{if(typeof t.jsx!="function")throw new TypeError("Expected `jsx` in production options");if(typeof t.jsxs!="function")throw new TypeError("Expected `jsxs` in production options");r=iS(n,t.jsx,t.jsxs)}const i={Fragment:t.Fragment,ancestors:[],components:t.components||{},create:r,elementAttributeNameCase:t.elementAttributeNameCase||"react",evaluater:t.createEvaluater?t.createEvaluater():void 0,filePath:n,ignoreInvalidStyle:t.ignoreInvalidStyle||!1,passKeys:t.passKeys!==!1,passNode:t.passNode||!1,schema:t.space==="svg"?bc:Dk,stylePropertyNameCase:t.stylePropertyNameCase||"dom",tableCellAlignToStyle:t.tableCellAlignToStyle!==!1},o=Ny(i,e,void 0);return o&&typeof o!="string"?o:i.create(e,i.Fragment,{children:o||void 0},void 0)}function Ny(e,t,n){if(t.type==="element")return Zk(e,t,n);if(t.type==="mdxFlowExpression"||t.type==="mdxTextExpression")return Jk(e,t);if(t.type==="mdxJsxFlowElement"||t.type==="mdxJsxTextElement")return tS(e,t,n);if(t.type==="mdxjsEsm")return eS(e,t);if(t.type==="root")return nS(e,t,n);if(t.type==="text")return rS(e,t)}function Zk(e,t,n){const r=e.schema;let i=r;t.tagName.toLowerCase()==="svg"&&r.space==="html"&&(i=bc,e.schema=i),e.ancestors.push(t);const o=Vy(e,t.tagName,!1),s=sS(e,t);let l=_c(e,t);return Yk.has(t.tagName)&&(l=l.filter(function(a){return typeof a=="string"?!yk(a):!0})),jy(e,s,o,t),Ic(s,l),e.ancestors.pop(),e.schema=r,e.create(t,o,s,n)}function Jk(e,t){if(t.data&&t.data.estree&&e.evaluater){const r=t.data.estree.body[0];return r.type,e.evaluater.evaluateExpression(r.expression)}Ki(e,t.position)}function eS(e,t){if(t.data&&t.data.estree&&e.evaluater)return e.evaluater.evaluateProgram(t.data.estree);Ki(e,t.position)}function tS(e,t,n){const r=e.schema;let i=r;t.name==="svg"&&r.space==="html"&&(i=bc,e.schema=i),e.ancestors.push(t);const o=t.name===null?e.Fragment:Vy(e,t.name,!0),s=lS(e,t),l=_c(e,t);return jy(e,s,o,t),Ic(s,l),e.ancestors.pop(),e.schema=r,e.create(t,o,s,n)}function nS(e,t,n){const r={};return Ic(r,_c(e,t)),e.create(t,e.Fragment,r,n)}function rS(e,t){return t.value}function jy(e,t,n,r){typeof n!="string"&&n!==e.Fragment&&e.passNode&&(t.node=r)}function Ic(e,t){if(t.length>0){const n=t.length>1?t:t[0];n&&(e.children=n)}}function iS(e,t,n){return r;function r(i,o,s,l){const u=Array.isArray(s.children)?n:t;return l?u(o,s,l):u(o,s)}}function oS(e,t){return n;function n(r,i,o,s){const l=Array.isArray(o.children),a=Rc(r);return t(i,o,s,l,{columnNumber:a?a.column-1:void 0,fileName:e,lineNumber:a?a.line:void 0},void 0)}}function sS(e,t){const n={};let r,i;for(i in t.properties)if(i!=="children"&&Mc.call(t.properties,i)){const o=aS(e,i,t.properties[i]);if(o){const[s,l]=o;e.tableCellAlignToStyle&&s==="align"&&typeof l=="string"&&qk.has(t.tagName)?r=l:n[s]=l}}if(r){const o=n.style||(n.style={});o[e.stylePropertyNameCase==="css"?"text-align":"textAlign"]=r}return n}function lS(e,t){const n={};for(const r of t.attributes)if(r.type==="mdxJsxExpressionAttribute")if(r.data&&r.data.estree&&e.evaluater){const o=r.data.estree.body[0];o.type;const s=o.expression;s.type;const l=s.properties[0];l.type,Object.assign(n,e.evaluater.evaluateExpression(l.argument))}else Ki(e,t.position);else{const i=r.name;let o;if(r.value&&typeof r.value=="object")if(r.value.data&&r.value.data.estree&&e.evaluater){const l=r.value.data.estree.body[0];l.type,o=e.evaluater.evaluateExpression(l.expression)}else Ki(e,t.position);else o=r.value===null?!0:r.value;n[i]=o}return n}function _c(e,t){const n=[];let r=-1;const i=e.passKeys?new Map:Kk;for(;++r<t.children.length;){const o=t.children[r];let s;if(e.passKeys){const a=o.type==="element"?o.tagName:o.type==="mdxJsxFlowElement"||o.type==="mdxJsxTextElement"?o.name:void 0;if(a){const u=i.get(a)||0;s=a+"-"+u,i.set(a,u+1)}}const l=Ny(e,o,s);l!==void 0&&n.push(l)}return n}function aS(e,t,n){const r=Ek(e.schema,t);if(!(n==null||typeof n=="number"&&Number.isNaN(n))){if(Array.isArray(n)&&(n=r.commaSeparated?hk(n):Lk(n)),r.property==="style"){let i=typeof n=="object"?n:uS(e,String(n));return e.stylePropertyNameCase==="css"&&(i=cS(i)),["style",i]}return[e.elementAttributeNameCase==="react"&&r.space?Ak[r.property]||r.property:r.attribute,n]}}function uS(e,t){const n={};try{Hk(t,r)}catch(i){if(!e.ignoreInvalidStyle){const o=i,s=new Ne("Cannot parse `style` attribute",{ancestors:e.ancestors,cause:o,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw s.file=e.filePath||void 0,s.url=Fy+"#cannot-parse-style-attribute",s}}return n;function r(i,o){let s=i;s.slice(0,2)!=="--"&&(s.slice(0,4)==="-ms-"&&(s="ms-"+s.slice(4)),s=s.replace(Gk,hS)),n[s]=o}}function Vy(e,t,n){let r;if(!n)r={type:"Literal",value:t};else if(t.includes(".")){const i=t.split(".");let o=-1,s;for(;++o<i.length;){const l=qh(i[o])?{type:"Identifier",name:i[o]}:{type:"Literal",value:i[o]};s=s?{type:"MemberExpression",object:s,property:l,computed:!!(o&&l.type==="Literal"),optional:!1}:l}r=s}else r=qh(t)&&!/^[a-z]/.test(t)?{type:"Identifier",name:t}:{type:"Literal",value:t};if(r.type==="Literal"){const i=r.value;return Mc.call(e.components,i)?e.components[i]:i}if(e.evaluater)return e.evaluater.evaluateExpression(r);Ki(e)}function Ki(e,t){const n=new Ne("Cannot handle MDX estrees without `createEvaluater`",{ancestors:e.ancestors,place:t,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=e.filePath||void 0,n.url=Fy+"#cannot-handle-mdx-estrees-without-createevaluater",n}function cS(e){const t={};let n;for(n in e)Mc.call(e,n)&&(t[fS(n)]=e[n]);return t}function fS(e){let t=e.replace(Qk,dS);return t.slice(0,3)==="ms-"&&(t="-"+t),t}function hS(e,t){return t.toUpperCase()}function dS(e){return"-"+e.toLowerCase()}const Nl={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]},pS={};function Oc(e,t){const n=pS,r=typeof n.includeImageAlt=="boolean"?n.includeImageAlt:!0,i=typeof n.includeHtml=="boolean"?n.includeHtml:!0;return By(e,r,i)}function By(e,t,n){if(mS(e)){if("value"in e)return e.type==="html"&&!n?"":e.value;if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return ld(e.children,t,n)}return Array.isArray(e)?ld(e,t,n):""}function ld(e,t,n){const r=[];let i=-1;for(;++i<e.length;)r[i]=By(e[i],t,n);return r.join("")}function mS(e){return!!(e&&typeof e=="object")}const ad=document.createElement("i");function zc(e){const t="&"+e+";";ad.innerHTML=t;const n=ad.textContent;return n.charCodeAt(n.length-1)===59&&e!=="semi"||n===t?!1:n}function pt(e,t,n,r){const i=e.length;let o=0,s;if(t<0?t=-t>i?0:i+t:t=t>i?i:t,n=n>0?n:0,r.length<1e4)s=Array.from(r),s.unshift(t,n),e.splice(...s);else for(n&&e.splice(t,n);o<r.length;)s=r.slice(o,o+1e4),s.unshift(t,0),e.splice(...s),o+=1e4,t+=1e4}function ft(e,t){return e.length>0?(pt(e,e.length,0,t),e):t}const ud={}.hasOwnProperty;function Uy(e){const t={};let n=-1;for(;++n<e.length;)gS(t,e[n]);return t}function gS(e,t){let n;for(n in t){const i=(ud.call(e,n)?e[n]:void 0)||(e[n]={}),o=t[n];let s;if(o)for(s in o){ud.call(i,s)||(i[s]=[]);const l=o[s];yS(i[s],Array.isArray(l)?l:l?[l]:[])}}}function yS(e,t){let n=-1;const r=[];for(;++n<t.length;)(t[n].add==="after"?e:r).push(t[n]);pt(e,0,0,r)}function $y(e,t){const n=Number.parseInt(e,t);return n<9||n===11||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||(n&65535)===65535||(n&65535)===65534||n>1114111?"�":String.fromCodePoint(n)}function At(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}const Be=Dn(/[A-Za-z]/),Oe=Dn(/[\dA-Za-z]/),vS=Dn(/[#-'*+\--9=?A-Z^-~]/);function Ps(e){return e!==null&&(e<32||e===127)}const cu=Dn(/\d/),xS=Dn(/[\dA-Fa-f]/),wS=Dn(/[!-/:-@[-`{-~]/);function B(e){return e!==null&&e<-2}function re(e){return e!==null&&(e<0||e===32)}function Q(e){return e===-2||e===-1||e===32}const el=Dn(new RegExp("\\p{P}|\\p{S}","u")),qn=Dn(/\s/);function Dn(e){return t;function t(n){return n!==null&&n>-1&&e.test(String.fromCharCode(n))}}function Wr(e){const t=[];let n=-1,r=0,i=0;for(;++n<e.length;){const o=e.charCodeAt(n);let s="";if(o===37&&Oe(e.charCodeAt(n+1))&&Oe(e.charCodeAt(n+2)))i=2;else if(o<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(o))||(s=String.fromCharCode(o));else if(o>55295&&o<57344){const l=e.charCodeAt(n+1);o<56320&&l>56319&&l<57344?(s=String.fromCharCode(o,l),i=1):s="�"}else s=String.fromCharCode(o);s&&(t.push(e.slice(r,n),encodeURIComponent(s)),r=n+i+1,s=""),i&&(n+=i,i=0)}return t.join("")+e.slice(r)}function X(e,t,n,r){const i=r?r-1:Number.POSITIVE_INFINITY;let o=0;return s;function s(a){return Q(a)?(e.enter(n),l(a)):t(a)}function l(a){return Q(a)&&o++<i?(e.consume(a),l):(e.exit(n),t(a))}}const kS={tokenize:SS};function SS(e){const t=e.attempt(this.parser.constructs.contentInitial,r,i);let n;return t;function r(l){if(l===null){e.consume(l);return}return e.enter("lineEnding"),e.consume(l),e.exit("lineEnding"),X(e,t,"linePrefix")}function i(l){return e.enter("paragraph"),o(l)}function o(l){const a=e.enter("chunkText",{contentType:"text",previous:n});return n&&(n.next=a),n=a,s(l)}function s(l){if(l===null){e.exit("chunkText"),e.exit("paragraph"),e.consume(l);return}return B(l)?(e.consume(l),e.exit("chunkText"),o):(e.consume(l),s)}}const CS={tokenize:ES},cd={tokenize:TS};function ES(e){const t=this,n=[];let r=0,i,o,s;return l;function l(y){if(r<n.length){const C=n[r];return t.containerState=C[1],e.attempt(C[0].continuation,a,u)(y)}return u(y)}function a(y){if(r++,t.containerState._closeFlow){t.containerState._closeFlow=void 0,i&&m();const C=t.events.length;let T=C,k;for(;T--;)if(t.events[T][0]==="exit"&&t.events[T][1].type==="chunkFlow"){k=t.events[T][1].end;break}p(r);let P=C;for(;P<t.events.length;)t.events[P][1].end=Object.assign({},k),P++;return pt(t.events,T+1,0,t.events.slice(C)),t.events.length=P,u(y)}return l(y)}function u(y){if(r===n.length){if(!i)return h(y);if(i.currentConstruct&&i.currentConstruct.concrete)return g(y);t.interrupt=!!(i.currentConstruct&&!i._gfmTableDynamicInterruptHack)}return t.containerState={},e.check(cd,c,f)(y)}function c(y){return i&&m(),p(r),h(y)}function f(y){return t.parser.lazy[t.now().line]=r!==n.length,s=t.now().offset,g(y)}function h(y){return t.containerState={},e.attempt(cd,d,g)(y)}function d(y){return r++,n.push([t.currentConstruct,t.containerState]),h(y)}function g(y){if(y===null){i&&m(),p(0),e.consume(y);return}return i=i||t.parser.flow(t.now()),e.enter("chunkFlow",{contentType:"flow",previous:o,_tokenizer:i}),v(y)}function v(y){if(y===null){w(e.exit("chunkFlow"),!0),p(0),e.consume(y);return}return B(y)?(e.consume(y),w(e.exit("chunkFlow")),r=0,t.interrupt=void 0,l):(e.consume(y),v)}function w(y,C){const T=t.sliceStream(y);if(C&&T.push(null),y.previous=o,o&&(o.next=y),o=y,i.defineSkip(y.start),i.write(T),t.parser.lazy[y.start.line]){let k=i.events.length;for(;k--;)if(i.events[k][1].start.offset<s&&(!i.events[k][1].end||i.events[k][1].end.offset>s))return;const P=t.events.length;let A=P,O,E;for(;A--;)if(t.events[A][0]==="exit"&&t.events[A][1].type==="chunkFlow"){if(O){E=t.events[A][1].end;break}O=!0}for(p(r),k=P;k<t.events.length;)t.events[k][1].end=Object.assign({},E),k++;pt(t.events,A+1,0,t.events.slice(P)),t.events.length=k}}function p(y){let C=n.length;for(;C-- >y;){const T=n[C];t.containerState=T[1],T[0].exit.call(t,e)}n.length=y}function m(){i.write([null]),o=void 0,i=void 0,t.containerState._closeFlow=void 0}}function TS(e,t,n){return X(e,e.attempt(this.parser.constructs.document,t,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}function As(e){if(e===null||re(e)||qn(e))return 1;if(el(e))return 2}function tl(e,t,n){const r=[];let i=-1;for(;++i<e.length;){const o=e[i].resolveAll;o&&!r.includes(o)&&(t=o(t,n),r.push(o))}return t}const fu={name:"attention",tokenize:AS,resolveAll:PS};function PS(e,t){let n=-1,r,i,o,s,l,a,u,c;for(;++n<e.length;)if(e[n][0]==="enter"&&e[n][1].type==="attentionSequence"&&e[n][1]._close){for(r=n;r--;)if(e[r][0]==="exit"&&e[r][1].type==="attentionSequence"&&e[r][1]._open&&t.sliceSerialize(e[r][1]).charCodeAt(0)===t.sliceSerialize(e[n][1]).charCodeAt(0)){if((e[r][1]._close||e[n][1]._open)&&(e[n][1].end.offset-e[n][1].start.offset)%3&&!((e[r][1].end.offset-e[r][1].start.offset+e[n][1].end.offset-e[n][1].start.offset)%3))continue;a=e[r][1].end.offset-e[r][1].start.offset>1&&e[n][1].end.offset-e[n][1].start.offset>1?2:1;const f=Object.assign({},e[r][1].end),h=Object.assign({},e[n][1].start);fd(f,-a),fd(h,a),s={type:a>1?"strongSequence":"emphasisSequence",start:f,end:Object.assign({},e[r][1].end)},l={type:a>1?"strongSequence":"emphasisSequence",start:Object.assign({},e[n][1].start),end:h},o={type:a>1?"strongText":"emphasisText",start:Object.assign({},e[r][1].end),end:Object.assign({},e[n][1].start)},i={type:a>1?"strong":"emphasis",start:Object.assign({},s.start),end:Object.assign({},l.end)},e[r][1].end=Object.assign({},s.start),e[n][1].start=Object.assign({},l.end),u=[],e[r][1].end.offset-e[r][1].start.offset&&(u=ft(u,[["enter",e[r][1],t],["exit",e[r][1],t]])),u=ft(u,[["enter",i,t],["enter",s,t],["exit",s,t],["enter",o,t]]),u=ft(u,tl(t.parser.constructs.insideSpan.null,e.slice(r+1,n),t)),u=ft(u,[["exit",o,t],["enter",l,t],["exit",l,t],["exit",i,t]]),e[n][1].end.offset-e[n][1].start.offset?(c=2,u=ft(u,[["enter",e[n][1],t],["exit",e[n][1],t]])):c=0,pt(e,r-1,n-r+3,u),n=r+u.length-c-2;break}}for(n=-1;++n<e.length;)e[n][1].type==="attentionSequence"&&(e[n][1].type="data");return e}function AS(e,t){const n=this.parser.constructs.attentionMarkers.null,r=this.previous,i=As(r);let o;return s;function s(a){return o=a,e.enter("attentionSequence"),l(a)}function l(a){if(a===o)return e.consume(a),l;const u=e.exit("attentionSequence"),c=As(a),f=!c||c===2&&i||n.includes(a),h=!i||i===2&&c||n.includes(r);return u._open=!!(o===42?f:f&&(i||!h)),u._close=!!(o===42?h:h&&(c||!f)),t(a)}}function fd(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}const DS={name:"autolink",tokenize:LS};function LS(e,t,n){let r=0;return i;function i(d){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(d),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),o}function o(d){return Be(d)?(e.consume(d),s):d===64?n(d):u(d)}function s(d){return d===43||d===45||d===46||Oe(d)?(r=1,l(d)):u(d)}function l(d){return d===58?(e.consume(d),r=0,a):(d===43||d===45||d===46||Oe(d))&&r++<32?(e.consume(d),l):(r=0,u(d))}function a(d){return d===62?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(d),e.exit("autolinkMarker"),e.exit("autolink"),t):d===null||d===32||d===60||Ps(d)?n(d):(e.consume(d),a)}function u(d){return d===64?(e.consume(d),c):vS(d)?(e.consume(d),u):n(d)}function c(d){return Oe(d)?f(d):n(d)}function f(d){return d===46?(e.consume(d),r=0,c):d===62?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(d),e.exit("autolinkMarker"),e.exit("autolink"),t):h(d)}function h(d){if((d===45||Oe(d))&&r++<63){const g=d===45?h:f;return e.consume(d),g}return n(d)}}const io={tokenize:bS,partial:!0};function bS(e,t,n){return r;function r(o){return Q(o)?X(e,i,"linePrefix")(o):i(o)}function i(o){return o===null||B(o)?t(o):n(o)}}const Hy={name:"blockQuote",tokenize:RS,continuation:{tokenize:MS},exit:IS};function RS(e,t,n){const r=this;return i;function i(s){if(s===62){const l=r.containerState;return l.open||(e.enter("blockQuote",{_container:!0}),l.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(s),e.exit("blockQuoteMarker"),o}return n(s)}function o(s){return Q(s)?(e.enter("blockQuotePrefixWhitespace"),e.consume(s),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t):(e.exit("blockQuotePrefix"),t(s))}}function MS(e,t,n){const r=this;return i;function i(s){return Q(s)?X(e,o,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(s):o(s)}function o(s){return e.attempt(Hy,t,n)(s)}}function IS(e){e.exit("blockQuote")}const Wy={name:"characterEscape",tokenize:_S};function _S(e,t,n){return r;function r(o){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(o),e.exit("escapeMarker"),i}function i(o){return wS(o)?(e.enter("characterEscapeValue"),e.consume(o),e.exit("characterEscapeValue"),e.exit("characterEscape"),t):n(o)}}const Ky={name:"characterReference",tokenize:OS};function OS(e,t,n){const r=this;let i=0,o,s;return l;function l(f){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(f),e.exit("characterReferenceMarker"),a}function a(f){return f===35?(e.enter("characterReferenceMarkerNumeric"),e.consume(f),e.exit("characterReferenceMarkerNumeric"),u):(e.enter("characterReferenceValue"),o=31,s=Oe,c(f))}function u(f){return f===88||f===120?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(f),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),o=6,s=xS,c):(e.enter("characterReferenceValue"),o=7,s=cu,c(f))}function c(f){if(f===59&&i){const h=e.exit("characterReferenceValue");return s===Oe&&!zc(r.sliceSerialize(h))?n(f):(e.enter("characterReferenceMarker"),e.consume(f),e.exit("characterReferenceMarker"),e.exit("characterReference"),t)}return s(f)&&i++<o?(e.consume(f),c):n(f)}}const hd={tokenize:FS,partial:!0},dd={name:"codeFenced",tokenize:zS,concrete:!0};function zS(e,t,n){const r=this,i={tokenize:T,partial:!0};let o=0,s=0,l;return a;function a(k){return u(k)}function u(k){const P=r.events[r.events.length-1];return o=P&&P[1].type==="linePrefix"?P[2].sliceSerialize(P[1],!0).length:0,l=k,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),c(k)}function c(k){return k===l?(s++,e.consume(k),c):s<3?n(k):(e.exit("codeFencedFenceSequence"),Q(k)?X(e,f,"whitespace")(k):f(k))}function f(k){return k===null||B(k)?(e.exit("codeFencedFence"),r.interrupt?t(k):e.check(hd,v,C)(k)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),h(k))}function h(k){return k===null||B(k)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),f(k)):Q(k)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),X(e,d,"whitespace")(k)):k===96&&k===l?n(k):(e.consume(k),h)}function d(k){return k===null||B(k)?f(k):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),g(k))}function g(k){return k===null||B(k)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),f(k)):k===96&&k===l?n(k):(e.consume(k),g)}function v(k){return e.attempt(i,C,w)(k)}function w(k){return e.enter("lineEnding"),e.consume(k),e.exit("lineEnding"),p}function p(k){return o>0&&Q(k)?X(e,m,"linePrefix",o+1)(k):m(k)}function m(k){return k===null||B(k)?e.check(hd,v,C)(k):(e.enter("codeFlowValue"),y(k))}function y(k){return k===null||B(k)?(e.exit("codeFlowValue"),m(k)):(e.consume(k),y)}function C(k){return e.exit("codeFenced"),t(k)}function T(k,P,A){let O=0;return E;function E(K){return k.enter("lineEnding"),k.consume(K),k.exit("lineEnding"),_}function _(K){return k.enter("codeFencedFence"),Q(K)?X(k,N,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(K):N(K)}function N(K){return K===l?(k.enter("codeFencedFenceSequence"),Y(K)):A(K)}function Y(K){return K===l?(O++,k.consume(K),Y):O>=s?(k.exit("codeFencedFenceSequence"),Q(K)?X(k,J,"whitespace")(K):J(K)):A(K)}function J(K){return K===null||B(K)?(k.exit("codeFencedFence"),P(K)):A(K)}}}function FS(e,t,n){const r=this;return i;function i(s){return s===null?n(s):(e.enter("lineEnding"),e.consume(s),e.exit("lineEnding"),o)}function o(s){return r.parser.lazy[r.now().line]?n(s):t(s)}}const jl={name:"codeIndented",tokenize:jS},NS={tokenize:VS,partial:!0};function jS(e,t,n){const r=this;return i;function i(u){return e.enter("codeIndented"),X(e,o,"linePrefix",5)(u)}function o(u){const c=r.events[r.events.length-1];return c&&c[1].type==="linePrefix"&&c[2].sliceSerialize(c[1],!0).length>=4?s(u):n(u)}function s(u){return u===null?a(u):B(u)?e.attempt(NS,s,a)(u):(e.enter("codeFlowValue"),l(u))}function l(u){return u===null||B(u)?(e.exit("codeFlowValue"),s(u)):(e.consume(u),l)}function a(u){return e.exit("codeIndented"),t(u)}}function VS(e,t,n){const r=this;return i;function i(s){return r.parser.lazy[r.now().line]?n(s):B(s)?(e.enter("lineEnding"),e.consume(s),e.exit("lineEnding"),i):X(e,o,"linePrefix",5)(s)}function o(s){const l=r.events[r.events.length-1];return l&&l[1].type==="linePrefix"&&l[2].sliceSerialize(l[1],!0).length>=4?t(s):B(s)?i(s):n(s)}}const BS={name:"codeText",tokenize:HS,resolve:US,previous:$S};function US(e){let t=e.length-4,n=3,r,i;if((e[n][1].type==="lineEnding"||e[n][1].type==="space")&&(e[t][1].type==="lineEnding"||e[t][1].type==="space")){for(r=n;++r<t;)if(e[r][1].type==="codeTextData"){e[n][1].type="codeTextPadding",e[t][1].type="codeTextPadding",n+=2,t-=2;break}}for(r=n-1,t++;++r<=t;)i===void 0?r!==t&&e[r][1].type!=="lineEnding"&&(i=r):(r===t||e[r][1].type==="lineEnding")&&(e[i][1].type="codeTextData",r!==i+2&&(e[i][1].end=e[r-1][1].end,e.splice(i+2,r-i-2),t-=r-i-2,r=i+2),i=void 0);return e}function $S(e){return e!==96||this.events[this.events.length-1][1].type==="characterEscape"}function HS(e,t,n){let r=0,i,o;return s;function s(f){return e.enter("codeText"),e.enter("codeTextSequence"),l(f)}function l(f){return f===96?(e.consume(f),r++,l):(e.exit("codeTextSequence"),a(f))}function a(f){return f===null?n(f):f===32?(e.enter("space"),e.consume(f),e.exit("space"),a):f===96?(o=e.enter("codeTextSequence"),i=0,c(f)):B(f)?(e.enter("lineEnding"),e.consume(f),e.exit("lineEnding"),a):(e.enter("codeTextData"),u(f))}function u(f){return f===null||f===32||f===96||B(f)?(e.exit("codeTextData"),a(f)):(e.consume(f),u)}function c(f){return f===96?(e.consume(f),i++,c):i===r?(e.exit("codeTextSequence"),e.exit("codeText"),t(f)):(o.type="codeTextData",u(f))}}class WS{constructor(t){this.left=t?[...t]:[],this.right=[]}get(t){if(t<0||t>=this.left.length+this.right.length)throw new RangeError("Cannot access index `"+t+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return t<this.left.length?this.left[t]:this.right[this.right.length-t+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(t,n){const r=n??Number.POSITIVE_INFINITY;return r<this.left.length?this.left.slice(t,r):t>this.left.length?this.right.slice(this.right.length-r+this.left.length,this.right.length-t+this.left.length).reverse():this.left.slice(t).concat(this.right.slice(this.right.length-r+this.left.length).reverse())}splice(t,n,r){const i=n||0;this.setCursor(Math.trunc(t));const o=this.right.splice(this.right.length-i,Number.POSITIVE_INFINITY);return r&&ii(this.left,r),o.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(t){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(t)}pushMany(t){this.setCursor(Number.POSITIVE_INFINITY),ii(this.left,t)}unshift(t){this.setCursor(0),this.right.push(t)}unshiftMany(t){this.setCursor(0),ii(this.right,t.reverse())}setCursor(t){if(!(t===this.left.length||t>this.left.length&&this.right.length===0||t<0&&this.left.length===0))if(t<this.left.length){const n=this.left.splice(t,Number.POSITIVE_INFINITY);ii(this.right,n.reverse())}else{const n=this.right.splice(this.left.length+this.right.length-t,Number.POSITIVE_INFINITY);ii(this.left,n.reverse())}}}function ii(e,t){let n=0;if(t.length<1e4)e.push(...t);else for(;n<t.length;)e.push(...t.slice(n,n+1e4)),n+=1e4}function Qy(e){const t={};let n=-1,r,i,o,s,l,a,u;const c=new WS(e);for(;++n<c.length;){for(;n in t;)n=t[n];if(r=c.get(n),n&&r[1].type==="chunkFlow"&&c.get(n-1)[1].type==="listItemPrefix"&&(a=r[1]._tokenizer.events,o=0,o<a.length&&a[o][1].type==="lineEndingBlank"&&(o+=2),o<a.length&&a[o][1].type==="content"))for(;++o<a.length&&a[o][1].type!=="content";)a[o][1].type==="chunkText"&&(a[o][1]._isInFirstContentOfListItem=!0,o++);if(r[0]==="enter")r[1].contentType&&(Object.assign(t,KS(c,n)),n=t[n],u=!0);else if(r[1]._container){for(o=n,i=void 0;o--&&(s=c.get(o),s[1].type==="lineEnding"||s[1].type==="lineEndingBlank");)s[0]==="enter"&&(i&&(c.get(i)[1].type="lineEndingBlank"),s[1].type="lineEnding",i=o);i&&(r[1].end=Object.assign({},c.get(i)[1].start),l=c.slice(i,n),l.unshift(r),c.splice(i,n-i+1,l))}}return pt(e,0,Number.POSITIVE_INFINITY,c.slice(0)),!u}function KS(e,t){const n=e.get(t)[1],r=e.get(t)[2];let i=t-1;const o=[],s=n._tokenizer||r.parser[n.contentType](n.start),l=s.events,a=[],u={};let c,f,h=-1,d=n,g=0,v=0;const w=[v];for(;d;){for(;e.get(++i)[1]!==d;);o.push(i),d._tokenizer||(c=r.sliceStream(d),d.next||c.push(null),f&&s.defineSkip(d.start),d._isInFirstContentOfListItem&&(s._gfmTasklistFirstContentOfListItem=!0),s.write(c),d._isInFirstContentOfListItem&&(s._gfmTasklistFirstContentOfListItem=void 0)),f=d,d=d.next}for(d=n;++h<l.length;)l[h][0]==="exit"&&l[h-1][0]==="enter"&&l[h][1].type===l[h-1][1].type&&l[h][1].start.line!==l[h][1].end.line&&(v=h+1,w.push(v),d._tokenizer=void 0,d.previous=void 0,d=d.next);for(s.events=[],d?(d._tokenizer=void 0,d.previous=void 0):w.pop(),h=w.length;h--;){const p=l.slice(w[h],w[h+1]),m=o.pop();a.push([m,m+p.length-1]),e.splice(m,2,p)}for(a.reverse(),h=-1;++h<a.length;)u[g+a[h][0]]=g+a[h][1],g+=a[h][1]-a[h][0]-1;return u}const QS={tokenize:qS,resolve:YS},GS={tokenize:XS,partial:!0};function YS(e){return Qy(e),e}function qS(e,t){let n;return r;function r(l){return e.enter("content"),n=e.enter("chunkContent",{contentType:"content"}),i(l)}function i(l){return l===null?o(l):B(l)?e.check(GS,s,o)(l):(e.consume(l),i)}function o(l){return e.exit("chunkContent"),e.exit("content"),t(l)}function s(l){return e.consume(l),e.exit("chunkContent"),n.next=e.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,i}}function XS(e,t,n){const r=this;return i;function i(s){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(s),e.exit("lineEnding"),X(e,o,"linePrefix")}function o(s){if(s===null||B(s))return n(s);const l=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&l&&l[1].type==="linePrefix"&&l[2].sliceSerialize(l[1],!0).length>=4?t(s):e.interrupt(r.parser.constructs.flow,n,t)(s)}}function Gy(e,t,n,r,i,o,s,l,a){const u=a||Number.POSITIVE_INFINITY;let c=0;return f;function f(p){return p===60?(e.enter(r),e.enter(i),e.enter(o),e.consume(p),e.exit(o),h):p===null||p===32||p===41||Ps(p)?n(p):(e.enter(r),e.enter(s),e.enter(l),e.enter("chunkString",{contentType:"string"}),v(p))}function h(p){return p===62?(e.enter(o),e.consume(p),e.exit(o),e.exit(i),e.exit(r),t):(e.enter(l),e.enter("chunkString",{contentType:"string"}),d(p))}function d(p){return p===62?(e.exit("chunkString"),e.exit(l),h(p)):p===null||p===60||B(p)?n(p):(e.consume(p),p===92?g:d)}function g(p){return p===60||p===62||p===92?(e.consume(p),d):d(p)}function v(p){return!c&&(p===null||p===41||re(p))?(e.exit("chunkString"),e.exit(l),e.exit(s),e.exit(r),t(p)):c<u&&p===40?(e.consume(p),c++,v):p===41?(e.consume(p),c--,v):p===null||p===32||p===40||Ps(p)?n(p):(e.consume(p),p===92?w:v)}function w(p){return p===40||p===41||p===92?(e.consume(p),v):v(p)}}function Yy(e,t,n,r,i,o){const s=this;let l=0,a;return u;function u(d){return e.enter(r),e.enter(i),e.consume(d),e.exit(i),e.enter(o),c}function c(d){return l>999||d===null||d===91||d===93&&!a||d===94&&!l&&"_hiddenFootnoteSupport"in s.parser.constructs?n(d):d===93?(e.exit(o),e.enter(i),e.consume(d),e.exit(i),e.exit(r),t):B(d)?(e.enter("lineEnding"),e.consume(d),e.exit("lineEnding"),c):(e.enter("chunkString",{contentType:"string"}),f(d))}function f(d){return d===null||d===91||d===93||B(d)||l++>999?(e.exit("chunkString"),c(d)):(e.consume(d),a||(a=!Q(d)),d===92?h:f)}function h(d){return d===91||d===92||d===93?(e.consume(d),l++,f):f(d)}}function qy(e,t,n,r,i,o){let s;return l;function l(h){return h===34||h===39||h===40?(e.enter(r),e.enter(i),e.consume(h),e.exit(i),s=h===40?41:h,a):n(h)}function a(h){return h===s?(e.enter(i),e.consume(h),e.exit(i),e.exit(r),t):(e.enter(o),u(h))}function u(h){return h===s?(e.exit(o),a(s)):h===null?n(h):B(h)?(e.enter("lineEnding"),e.consume(h),e.exit("lineEnding"),X(e,u,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),c(h))}function c(h){return h===s||h===null||B(h)?(e.exit("chunkString"),u(h)):(e.consume(h),h===92?f:c)}function f(h){return h===s||h===92?(e.consume(h),c):c(h)}}function Ei(e,t){let n;return r;function r(i){return B(i)?(e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),n=!0,r):Q(i)?X(e,r,n?"linePrefix":"lineSuffix")(i):t(i)}}const ZS={name:"definition",tokenize:eC},JS={tokenize:tC,partial:!0};function eC(e,t,n){const r=this;let i;return o;function o(d){return e.enter("definition"),s(d)}function s(d){return Yy.call(r,e,l,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(d)}function l(d){return i=At(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)),d===58?(e.enter("definitionMarker"),e.consume(d),e.exit("definitionMarker"),a):n(d)}function a(d){return re(d)?Ei(e,u)(d):u(d)}function u(d){return Gy(e,c,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(d)}function c(d){return e.attempt(JS,f,f)(d)}function f(d){return Q(d)?X(e,h,"whitespace")(d):h(d)}function h(d){return d===null||B(d)?(e.exit("definition"),r.parser.defined.push(i),t(d)):n(d)}}function tC(e,t,n){return r;function r(l){return re(l)?Ei(e,i)(l):n(l)}function i(l){return qy(e,o,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(l)}function o(l){return Q(l)?X(e,s,"whitespace")(l):s(l)}function s(l){return l===null||B(l)?t(l):n(l)}}const nC={name:"hardBreakEscape",tokenize:rC};function rC(e,t,n){return r;function r(o){return e.enter("hardBreakEscape"),e.consume(o),i}function i(o){return B(o)?(e.exit("hardBreakEscape"),t(o)):n(o)}}const iC={name:"headingAtx",tokenize:sC,resolve:oC};function oC(e,t){let n=e.length-2,r=3,i,o;return e[r][1].type==="whitespace"&&(r+=2),n-2>r&&e[n][1].type==="whitespace"&&(n-=2),e[n][1].type==="atxHeadingSequence"&&(r===n-1||n-4>r&&e[n-2][1].type==="whitespace")&&(n-=r+1===n?2:4),n>r&&(i={type:"atxHeadingText",start:e[r][1].start,end:e[n][1].end},o={type:"chunkText",start:e[r][1].start,end:e[n][1].end,contentType:"text"},pt(e,r,n-r+1,[["enter",i,t],["enter",o,t],["exit",o,t],["exit",i,t]])),e}function sC(e,t,n){let r=0;return i;function i(c){return e.enter("atxHeading"),o(c)}function o(c){return e.enter("atxHeadingSequence"),s(c)}function s(c){return c===35&&r++<6?(e.consume(c),s):c===null||re(c)?(e.exit("atxHeadingSequence"),l(c)):n(c)}function l(c){return c===35?(e.enter("atxHeadingSequence"),a(c)):c===null||B(c)?(e.exit("atxHeading"),t(c)):Q(c)?X(e,l,"whitespace")(c):(e.enter("atxHeadingText"),u(c))}function a(c){return c===35?(e.consume(c),a):(e.exit("atxHeadingSequence"),l(c))}function u(c){return c===null||c===35||re(c)?(e.exit("atxHeadingText"),l(c)):(e.consume(c),u)}}const lC=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],pd=["pre","script","style","textarea"],aC={name:"htmlFlow",tokenize:hC,resolveTo:fC,concrete:!0},uC={tokenize:pC,partial:!0},cC={tokenize:dC,partial:!0};function fC(e){let t=e.length;for(;t--&&!(e[t][0]==="enter"&&e[t][1].type==="htmlFlow"););return t>1&&e[t-2][1].type==="linePrefix"&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2)),e}function hC(e,t,n){const r=this;let i,o,s,l,a;return u;function u(S){return c(S)}function c(S){return e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(S),f}function f(S){return S===33?(e.consume(S),h):S===47?(e.consume(S),o=!0,v):S===63?(e.consume(S),i=3,r.interrupt?t:x):Be(S)?(e.consume(S),s=String.fromCharCode(S),w):n(S)}function h(S){return S===45?(e.consume(S),i=2,d):S===91?(e.consume(S),i=5,l=0,g):Be(S)?(e.consume(S),i=4,r.interrupt?t:x):n(S)}function d(S){return S===45?(e.consume(S),r.interrupt?t:x):n(S)}function g(S){const fe="CDATA[";return S===fe.charCodeAt(l++)?(e.consume(S),l===fe.length?r.interrupt?t:N:g):n(S)}function v(S){return Be(S)?(e.consume(S),s=String.fromCharCode(S),w):n(S)}function w(S){if(S===null||S===47||S===62||re(S)){const fe=S===47,vt=s.toLowerCase();return!fe&&!o&&pd.includes(vt)?(i=1,r.interrupt?t(S):N(S)):lC.includes(s.toLowerCase())?(i=6,fe?(e.consume(S),p):r.interrupt?t(S):N(S)):(i=7,r.interrupt&&!r.parser.lazy[r.now().line]?n(S):o?m(S):y(S))}return S===45||Oe(S)?(e.consume(S),s+=String.fromCharCode(S),w):n(S)}function p(S){return S===62?(e.consume(S),r.interrupt?t:N):n(S)}function m(S){return Q(S)?(e.consume(S),m):E(S)}function y(S){return S===47?(e.consume(S),E):S===58||S===95||Be(S)?(e.consume(S),C):Q(S)?(e.consume(S),y):E(S)}function C(S){return S===45||S===46||S===58||S===95||Oe(S)?(e.consume(S),C):T(S)}function T(S){return S===61?(e.consume(S),k):Q(S)?(e.consume(S),T):y(S)}function k(S){return S===null||S===60||S===61||S===62||S===96?n(S):S===34||S===39?(e.consume(S),a=S,P):Q(S)?(e.consume(S),k):A(S)}function P(S){return S===a?(e.consume(S),a=null,O):S===null||B(S)?n(S):(e.consume(S),P)}function A(S){return S===null||S===34||S===39||S===47||S===60||S===61||S===62||S===96||re(S)?T(S):(e.consume(S),A)}function O(S){return S===47||S===62||Q(S)?y(S):n(S)}function E(S){return S===62?(e.consume(S),_):n(S)}function _(S){return S===null||B(S)?N(S):Q(S)?(e.consume(S),_):n(S)}function N(S){return S===45&&i===2?(e.consume(S),ge):S===60&&i===1?(e.consume(S),F):S===62&&i===4?(e.consume(S),W):S===63&&i===3?(e.consume(S),x):S===93&&i===5?(e.consume(S),z):B(S)&&(i===6||i===7)?(e.exit("htmlFlowData"),e.check(uC,ee,Y)(S)):S===null||B(S)?(e.exit("htmlFlowData"),Y(S)):(e.consume(S),N)}function Y(S){return e.check(cC,J,ee)(S)}function J(S){return e.enter("lineEnding"),e.consume(S),e.exit("lineEnding"),K}function K(S){return S===null||B(S)?Y(S):(e.enter("htmlFlowData"),N(S))}function ge(S){return S===45?(e.consume(S),x):N(S)}function F(S){return S===47?(e.consume(S),s="",L):N(S)}function L(S){if(S===62){const fe=s.toLowerCase();return pd.includes(fe)?(e.consume(S),W):N(S)}return Be(S)&&s.length<8?(e.consume(S),s+=String.fromCharCode(S),L):N(S)}function z(S){return S===93?(e.consume(S),x):N(S)}function x(S){return S===62?(e.consume(S),W):S===45&&i===2?(e.consume(S),x):N(S)}function W(S){return S===null||B(S)?(e.exit("htmlFlowData"),ee(S)):(e.consume(S),W)}function ee(S){return e.exit("htmlFlow"),t(S)}}function dC(e,t,n){const r=this;return i;function i(s){return B(s)?(e.enter("lineEnding"),e.consume(s),e.exit("lineEnding"),o):n(s)}function o(s){return r.parser.lazy[r.now().line]?n(s):t(s)}}function pC(e,t,n){return r;function r(i){return e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),e.attempt(io,t,n)}}const mC={name:"htmlText",tokenize:gC};function gC(e,t,n){const r=this;let i,o,s;return l;function l(x){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(x),a}function a(x){return x===33?(e.consume(x),u):x===47?(e.consume(x),T):x===63?(e.consume(x),y):Be(x)?(e.consume(x),A):n(x)}function u(x){return x===45?(e.consume(x),c):x===91?(e.consume(x),o=0,g):Be(x)?(e.consume(x),m):n(x)}function c(x){return x===45?(e.consume(x),d):n(x)}function f(x){return x===null?n(x):x===45?(e.consume(x),h):B(x)?(s=f,F(x)):(e.consume(x),f)}function h(x){return x===45?(e.consume(x),d):f(x)}function d(x){return x===62?ge(x):x===45?h(x):f(x)}function g(x){const W="CDATA[";return x===W.charCodeAt(o++)?(e.consume(x),o===W.length?v:g):n(x)}function v(x){return x===null?n(x):x===93?(e.consume(x),w):B(x)?(s=v,F(x)):(e.consume(x),v)}function w(x){return x===93?(e.consume(x),p):v(x)}function p(x){return x===62?ge(x):x===93?(e.consume(x),p):v(x)}function m(x){return x===null||x===62?ge(x):B(x)?(s=m,F(x)):(e.consume(x),m)}function y(x){return x===null?n(x):x===63?(e.consume(x),C):B(x)?(s=y,F(x)):(e.consume(x),y)}function C(x){return x===62?ge(x):y(x)}function T(x){return Be(x)?(e.consume(x),k):n(x)}function k(x){return x===45||Oe(x)?(e.consume(x),k):P(x)}function P(x){return B(x)?(s=P,F(x)):Q(x)?(e.consume(x),P):ge(x)}function A(x){return x===45||Oe(x)?(e.consume(x),A):x===47||x===62||re(x)?O(x):n(x)}function O(x){return x===47?(e.consume(x),ge):x===58||x===95||Be(x)?(e.consume(x),E):B(x)?(s=O,F(x)):Q(x)?(e.consume(x),O):ge(x)}function E(x){return x===45||x===46||x===58||x===95||Oe(x)?(e.consume(x),E):_(x)}function _(x){return x===61?(e.consume(x),N):B(x)?(s=_,F(x)):Q(x)?(e.consume(x),_):O(x)}function N(x){return x===null||x===60||x===61||x===62||x===96?n(x):x===34||x===39?(e.consume(x),i=x,Y):B(x)?(s=N,F(x)):Q(x)?(e.consume(x),N):(e.consume(x),J)}function Y(x){return x===i?(e.consume(x),i=void 0,K):x===null?n(x):B(x)?(s=Y,F(x)):(e.consume(x),Y)}function J(x){return x===null||x===34||x===39||x===60||x===61||x===96?n(x):x===47||x===62||re(x)?O(x):(e.consume(x),J)}function K(x){return x===47||x===62||re(x)?O(x):n(x)}function ge(x){return x===62?(e.consume(x),e.exit("htmlTextData"),e.exit("htmlText"),t):n(x)}function F(x){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(x),e.exit("lineEnding"),L}function L(x){return Q(x)?X(e,z,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(x):z(x)}function z(x){return e.enter("htmlTextData"),s(x)}}const Fc={name:"labelEnd",tokenize:SC,resolveTo:kC,resolveAll:wC},yC={tokenize:CC},vC={tokenize:EC},xC={tokenize:TC};function wC(e){let t=-1;for(;++t<e.length;){const n=e[t][1];(n.type==="labelImage"||n.type==="labelLink"||n.type==="labelEnd")&&(e.splice(t+1,n.type==="labelImage"?4:2),n.type="data",t++)}return e}function kC(e,t){let n=e.length,r=0,i,o,s,l;for(;n--;)if(i=e[n][1],o){if(i.type==="link"||i.type==="labelLink"&&i._inactive)break;e[n][0]==="enter"&&i.type==="labelLink"&&(i._inactive=!0)}else if(s){if(e[n][0]==="enter"&&(i.type==="labelImage"||i.type==="labelLink")&&!i._balanced&&(o=n,i.type!=="labelLink")){r=2;break}}else i.type==="labelEnd"&&(s=n);const a={type:e[o][1].type==="labelLink"?"link":"image",start:Object.assign({},e[o][1].start),end:Object.assign({},e[e.length-1][1].end)},u={type:"label",start:Object.assign({},e[o][1].start),end:Object.assign({},e[s][1].end)},c={type:"labelText",start:Object.assign({},e[o+r+2][1].end),end:Object.assign({},e[s-2][1].start)};return l=[["enter",a,t],["enter",u,t]],l=ft(l,e.slice(o+1,o+r+3)),l=ft(l,[["enter",c,t]]),l=ft(l,tl(t.parser.constructs.insideSpan.null,e.slice(o+r+4,s-3),t)),l=ft(l,[["exit",c,t],e[s-2],e[s-1],["exit",u,t]]),l=ft(l,e.slice(s+1)),l=ft(l,[["exit",a,t]]),pt(e,o,e.length,l),e}function SC(e,t,n){const r=this;let i=r.events.length,o,s;for(;i--;)if((r.events[i][1].type==="labelImage"||r.events[i][1].type==="labelLink")&&!r.events[i][1]._balanced){o=r.events[i][1];break}return l;function l(h){return o?o._inactive?f(h):(s=r.parser.defined.includes(At(r.sliceSerialize({start:o.end,end:r.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(h),e.exit("labelMarker"),e.exit("labelEnd"),a):n(h)}function a(h){return h===40?e.attempt(yC,c,s?c:f)(h):h===91?e.attempt(vC,c,s?u:f)(h):s?c(h):f(h)}function u(h){return e.attempt(xC,c,f)(h)}function c(h){return t(h)}function f(h){return o._balanced=!0,n(h)}}function CC(e,t,n){return r;function r(f){return e.enter("resource"),e.enter("resourceMarker"),e.consume(f),e.exit("resourceMarker"),i}function i(f){return re(f)?Ei(e,o)(f):o(f)}function o(f){return f===41?c(f):Gy(e,s,l,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(f)}function s(f){return re(f)?Ei(e,a)(f):c(f)}function l(f){return n(f)}function a(f){return f===34||f===39||f===40?qy(e,u,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(f):c(f)}function u(f){return re(f)?Ei(e,c)(f):c(f)}function c(f){return f===41?(e.enter("resourceMarker"),e.consume(f),e.exit("resourceMarker"),e.exit("resource"),t):n(f)}}function EC(e,t,n){const r=this;return i;function i(l){return Yy.call(r,e,o,s,"reference","referenceMarker","referenceString")(l)}function o(l){return r.parser.defined.includes(At(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?t(l):n(l)}function s(l){return n(l)}}function TC(e,t,n){return r;function r(o){return e.enter("reference"),e.enter("referenceMarker"),e.consume(o),e.exit("referenceMarker"),i}function i(o){return o===93?(e.enter("referenceMarker"),e.consume(o),e.exit("referenceMarker"),e.exit("reference"),t):n(o)}}const PC={name:"labelStartImage",tokenize:AC,resolveAll:Fc.resolveAll};function AC(e,t,n){const r=this;return i;function i(l){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(l),e.exit("labelImageMarker"),o}function o(l){return l===91?(e.enter("labelMarker"),e.consume(l),e.exit("labelMarker"),e.exit("labelImage"),s):n(l)}function s(l){return l===94&&"_hiddenFootnoteSupport"in r.parser.constructs?n(l):t(l)}}const DC={name:"labelStartLink",tokenize:LC,resolveAll:Fc.resolveAll};function LC(e,t,n){const r=this;return i;function i(s){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(s),e.exit("labelMarker"),e.exit("labelLink"),o}function o(s){return s===94&&"_hiddenFootnoteSupport"in r.parser.constructs?n(s):t(s)}}const Vl={name:"lineEnding",tokenize:bC};function bC(e,t){return n;function n(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),X(e,t,"linePrefix")}}const Yo={name:"thematicBreak",tokenize:RC};function RC(e,t,n){let r=0,i;return o;function o(u){return e.enter("thematicBreak"),s(u)}function s(u){return i=u,l(u)}function l(u){return u===i?(e.enter("thematicBreakSequence"),a(u)):r>=3&&(u===null||B(u))?(e.exit("thematicBreak"),t(u)):n(u)}function a(u){return u===i?(e.consume(u),r++,a):(e.exit("thematicBreakSequence"),Q(u)?X(e,l,"whitespace")(u):l(u))}}const We={name:"list",tokenize:_C,continuation:{tokenize:OC},exit:FC},MC={tokenize:NC,partial:!0},IC={tokenize:zC,partial:!0};function _C(e,t,n){const r=this,i=r.events[r.events.length-1];let o=i&&i[1].type==="linePrefix"?i[2].sliceSerialize(i[1],!0).length:0,s=0;return l;function l(d){const g=r.containerState.type||(d===42||d===43||d===45?"listUnordered":"listOrdered");if(g==="listUnordered"?!r.containerState.marker||d===r.containerState.marker:cu(d)){if(r.containerState.type||(r.containerState.type=g,e.enter(g,{_container:!0})),g==="listUnordered")return e.enter("listItemPrefix"),d===42||d===45?e.check(Yo,n,u)(d):u(d);if(!r.interrupt||d===49)return e.enter("listItemPrefix"),e.enter("listItemValue"),a(d)}return n(d)}function a(d){return cu(d)&&++s<10?(e.consume(d),a):(!r.interrupt||s<2)&&(r.containerState.marker?d===r.containerState.marker:d===41||d===46)?(e.exit("listItemValue"),u(d)):n(d)}function u(d){return e.enter("listItemMarker"),e.consume(d),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||d,e.check(io,r.interrupt?n:c,e.attempt(MC,h,f))}function c(d){return r.containerState.initialBlankLine=!0,o++,h(d)}function f(d){return Q(d)?(e.enter("listItemPrefixWhitespace"),e.consume(d),e.exit("listItemPrefixWhitespace"),h):n(d)}function h(d){return r.containerState.size=o+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(d)}}function OC(e,t,n){const r=this;return r.containerState._closeFlow=void 0,e.check(io,i,o);function i(l){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,X(e,t,"listItemIndent",r.containerState.size+1)(l)}function o(l){return r.containerState.furtherBlankLines||!Q(l)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,s(l)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(IC,t,s)(l))}function s(l){return r.containerState._closeFlow=!0,r.interrupt=void 0,X(e,e.attempt(We,t,n),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(l)}}function zC(e,t,n){const r=this;return X(e,i,"listItemIndent",r.containerState.size+1);function i(o){const s=r.events[r.events.length-1];return s&&s[1].type==="listItemIndent"&&s[2].sliceSerialize(s[1],!0).length===r.containerState.size?t(o):n(o)}}function FC(e){e.exit(this.containerState.type)}function NC(e,t,n){const r=this;return X(e,i,"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5);function i(o){const s=r.events[r.events.length-1];return!Q(o)&&s&&s[1].type==="listItemPrefixWhitespace"?t(o):n(o)}}const md={name:"setextUnderline",tokenize:VC,resolveTo:jC};function jC(e,t){let n=e.length,r,i,o;for(;n--;)if(e[n][0]==="enter"){if(e[n][1].type==="content"){r=n;break}e[n][1].type==="paragraph"&&(i=n)}else e[n][1].type==="content"&&e.splice(n,1),!o&&e[n][1].type==="definition"&&(o=n);const s={type:"setextHeading",start:Object.assign({},e[i][1].start),end:Object.assign({},e[e.length-1][1].end)};return e[i][1].type="setextHeadingText",o?(e.splice(i,0,["enter",s,t]),e.splice(o+1,0,["exit",e[r][1],t]),e[r][1].end=Object.assign({},e[o][1].end)):e[r][1]=s,e.push(["exit",s,t]),e}function VC(e,t,n){const r=this;let i;return o;function o(u){let c=r.events.length,f;for(;c--;)if(r.events[c][1].type!=="lineEnding"&&r.events[c][1].type!=="linePrefix"&&r.events[c][1].type!=="content"){f=r.events[c][1].type==="paragraph";break}return!r.parser.lazy[r.now().line]&&(r.interrupt||f)?(e.enter("setextHeadingLine"),i=u,s(u)):n(u)}function s(u){return e.enter("setextHeadingLineSequence"),l(u)}function l(u){return u===i?(e.consume(u),l):(e.exit("setextHeadingLineSequence"),Q(u)?X(e,a,"lineSuffix")(u):a(u))}function a(u){return u===null||B(u)?(e.exit("setextHeadingLine"),t(u)):n(u)}}const BC={tokenize:UC};function UC(e){const t=this,n=e.attempt(io,r,e.attempt(this.parser.constructs.flowInitial,i,X(e,e.attempt(this.parser.constructs.flow,i,e.attempt(QS,i)),"linePrefix")));return n;function r(o){if(o===null){e.consume(o);return}return e.enter("lineEndingBlank"),e.consume(o),e.exit("lineEndingBlank"),t.currentConstruct=void 0,n}function i(o){if(o===null){e.consume(o);return}return e.enter("lineEnding"),e.consume(o),e.exit("lineEnding"),t.currentConstruct=void 0,n}}const $C={resolveAll:Zy()},HC=Xy("string"),WC=Xy("text");function Xy(e){return{tokenize:t,resolveAll:Zy(e==="text"?KC:void 0)};function t(n){const r=this,i=this.parser.constructs[e],o=n.attempt(i,s,l);return s;function s(c){return u(c)?o(c):l(c)}function l(c){if(c===null){n.consume(c);return}return n.enter("data"),n.consume(c),a}function a(c){return u(c)?(n.exit("data"),o(c)):(n.consume(c),a)}function u(c){if(c===null)return!0;const f=i[c];let h=-1;if(f)for(;++h<f.length;){const d=f[h];if(!d.previous||d.previous.call(r,r.previous))return!0}return!1}}}function Zy(e){return t;function t(n,r){let i=-1,o;for(;++i<=n.length;)o===void 0?n[i]&&n[i][1].type==="data"&&(o=i,i++):(!n[i]||n[i][1].type!=="data")&&(i!==o+2&&(n[o][1].end=n[i-1][1].end,n.splice(o+2,i-o-2),i=o+2),o=void 0);return e?e(n,r):n}}function KC(e,t){let n=0;for(;++n<=e.length;)if((n===e.length||e[n][1].type==="lineEnding")&&e[n-1][1].type==="data"){const r=e[n-1][1],i=t.sliceStream(r);let o=i.length,s=-1,l=0,a;for(;o--;){const u=i[o];if(typeof u=="string"){for(s=u.length;u.charCodeAt(s-1)===32;)l++,s--;if(s)break;s=-1}else if(u===-2)a=!0,l++;else if(u!==-1){o++;break}}if(l){const u={type:n===e.length||a||l<2?"lineSuffix":"hardBreakTrailing",start:{line:r.end.line,column:r.end.column-l,offset:r.end.offset-l,_index:r.start._index+o,_bufferIndex:o?s:r.start._bufferIndex+s},end:Object.assign({},r.end)};r.end=Object.assign({},u.start),r.start.offset===r.end.offset?Object.assign(r,u):(e.splice(n,0,["enter",u,t],["exit",u,t]),n+=2)}n++}return e}function QC(e,t,n){let r=Object.assign(n?Object.assign({},n):{line:1,column:1,offset:0},{_index:0,_bufferIndex:-1});const i={},o=[];let s=[],l=[];const a={consume:m,enter:y,exit:C,attempt:P(T),check:P(k),interrupt:P(k,{interrupt:!0})},u={previous:null,code:null,containerState:{},events:[],parser:e,sliceStream:d,sliceSerialize:h,now:g,defineSkip:v,write:f};let c=t.tokenize.call(u,a);return t.resolveAll&&o.push(t),u;function f(_){return s=ft(s,_),w(),s[s.length-1]!==null?[]:(A(t,0),u.events=tl(o,u.events,u),u.events)}function h(_,N){return YC(d(_),N)}function d(_){return GC(s,_)}function g(){const{line:_,column:N,offset:Y,_index:J,_bufferIndex:K}=r;return{line:_,column:N,offset:Y,_index:J,_bufferIndex:K}}function v(_){i[_.line]=_.column,E()}function w(){let _;for(;r._index<s.length;){const N=s[r._index];if(typeof N=="string")for(_=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===_&&r._bufferIndex<N.length;)p(N.charCodeAt(r._bufferIndex));else p(N)}}function p(_){c=c(_)}function m(_){B(_)?(r.line++,r.column=1,r.offset+=_===-3?2:1,E()):_!==-1&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===s[r._index].length&&(r._bufferIndex=-1,r._index++)),u.previous=_}function y(_,N){const Y=N||{};return Y.type=_,Y.start=g(),u.events.push(["enter",Y,u]),l.push(Y),Y}function C(_){const N=l.pop();return N.end=g(),u.events.push(["exit",N,u]),N}function T(_,N){A(_,N.from)}function k(_,N){N.restore()}function P(_,N){return Y;function Y(J,K,ge){let F,L,z,x;return Array.isArray(J)?ee(J):"tokenize"in J?ee([J]):W(J);function W(se){return Lt;function Lt(tn){const tr=tn!==null&&se[tn],nr=tn!==null&&se.null,co=[...Array.isArray(tr)?tr:tr?[tr]:[],...Array.isArray(nr)?nr:nr?[nr]:[]];return ee(co)(tn)}}function ee(se){return F=se,L=0,se.length===0?ge:S(se[L])}function S(se){return Lt;function Lt(tn){return x=O(),z=se,se.partial||(u.currentConstruct=se),se.name&&u.parser.constructs.disable.null.includes(se.name)?vt():se.tokenize.call(N?Object.assign(Object.create(u),N):u,a,fe,vt)(tn)}}function fe(se){return _(z,x),K}function vt(se){return x.restore(),++L<F.length?S(F[L]):ge}}}function A(_,N){_.resolveAll&&!o.includes(_)&&o.push(_),_.resolve&&pt(u.events,N,u.events.length-N,_.resolve(u.events.slice(N),u)),_.resolveTo&&(u.events=_.resolveTo(u.events,u))}function O(){const _=g(),N=u.previous,Y=u.currentConstruct,J=u.events.length,K=Array.from(l);return{restore:ge,from:J};function ge(){r=_,u.previous=N,u.currentConstruct=Y,u.events.length=J,l=K,E()}}function E(){r.line in i&&r.column<2&&(r.column=i[r.line],r.offset+=i[r.line]-1)}}function GC(e,t){const n=t.start._index,r=t.start._bufferIndex,i=t.end._index,o=t.end._bufferIndex;let s;if(n===i)s=[e[n].slice(r,o)];else{if(s=e.slice(n,i),r>-1){const l=s[0];typeof l=="string"?s[0]=l.slice(r):s.shift()}o>0&&s.push(e[i].slice(0,o))}return s}function YC(e,t){let n=-1;const r=[];let i;for(;++n<e.length;){const o=e[n];let s;if(typeof o=="string")s=o;else switch(o){case-5:{s="\r";break}case-4:{s=`
`;break}case-3:{s=`\r
`;break}case-2:{s=t?" ":"	";break}case-1:{if(!t&&i)continue;s=" ";break}default:s=String.fromCharCode(o)}i=o===-2,r.push(s)}return r.join("")}const qC={42:We,43:We,45:We,48:We,49:We,50:We,51:We,52:We,53:We,54:We,55:We,56:We,57:We,62:Hy},XC={91:ZS},ZC={[-2]:jl,[-1]:jl,32:jl},JC={35:iC,42:Yo,45:[md,Yo],60:aC,61:md,95:Yo,96:dd,126:dd},eE={38:Ky,92:Wy},tE={[-5]:Vl,[-4]:Vl,[-3]:Vl,33:PC,38:Ky,42:fu,60:[DS,mC],91:DC,92:[nC,Wy],93:Fc,95:fu,96:BS},nE={null:[fu,$C]},rE={null:[42,95]},iE={null:[]},oE=Object.freeze(Object.defineProperty({__proto__:null,attentionMarkers:rE,contentInitial:XC,disable:iE,document:qC,flow:JC,flowInitial:ZC,insideSpan:nE,string:eE,text:tE},Symbol.toStringTag,{value:"Module"}));function sE(e){const n=Uy([oE,...(e||{}).extensions||[]]),r={defined:[],lazy:{},constructs:n,content:i(kS),document:i(CS),flow:i(BC),string:i(HC),text:i(WC)};return r;function i(o){return s;function s(l){return QC(r,o,l)}}}function lE(e){for(;!Qy(e););return e}const gd=/[\0\t\n\r]/g;function aE(){let e=1,t="",n=!0,r;return i;function i(o,s,l){const a=[];let u,c,f,h,d;for(o=t+(typeof o=="string"?o.toString():new TextDecoder(s||void 0).decode(o)),f=0,t="",n&&(o.charCodeAt(0)===65279&&f++,n=void 0);f<o.length;){if(gd.lastIndex=f,u=gd.exec(o),h=u&&u.index!==void 0?u.index:o.length,d=o.charCodeAt(h),!u){t=o.slice(f);break}if(d===10&&f===h&&r)a.push(-3),r=void 0;else switch(r&&(a.push(-5),r=void 0),f<h&&(a.push(o.slice(f,h)),e+=h-f),d){case 0:{a.push(65533),e++;break}case 9:{for(c=Math.ceil(e/4)*4,a.push(-2);e++<c;)a.push(-1);break}case 10:{a.push(-4),e=1;break}default:r=!0,e=1}f=h+1}return l&&(r&&a.push(-5),t&&a.push(t),a.push(null)),a}}const uE=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function cE(e){return e.replace(uE,fE)}function fE(e,t,n){if(t)return t;if(n.charCodeAt(0)===35){const i=n.charCodeAt(1),o=i===120||i===88;return $y(n.slice(o?2:1),o?16:10)}return zc(n)||e}const Jy={}.hasOwnProperty;function hE(e,t,n){return typeof t!="string"&&(n=t,t=void 0),dE(n)(lE(sE(n).document().write(aE()(e,t,!0))))}function dE(e){const t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:o(Rf),autolinkProtocol:O,autolinkEmail:O,atxHeading:o(Df),blockQuote:o(nr),characterEscape:O,characterReference:O,codeFenced:o(co),codeFencedFenceInfo:s,codeFencedFenceMeta:s,codeIndented:o(co,s),codeText:o(l1,s),codeTextData:O,data:O,codeFlowValue:O,definition:o(a1),definitionDestinationString:s,definitionLabelString:s,definitionTitleString:s,emphasis:o(u1),hardBreakEscape:o(Lf),hardBreakTrailing:o(Lf),htmlFlow:o(bf,s),htmlFlowData:O,htmlText:o(bf,s),htmlTextData:O,image:o(c1),label:s,link:o(Rf),listItem:o(f1),listItemValue:h,listOrdered:o(Mf,f),listUnordered:o(Mf),paragraph:o(h1),reference:S,referenceString:s,resourceDestinationString:s,resourceTitleString:s,setextHeading:o(Df),strong:o(d1),thematicBreak:o(m1)},exit:{atxHeading:a(),atxHeadingSequence:T,autolink:a(),autolinkEmail:tr,autolinkProtocol:tn,blockQuote:a(),characterEscapeValue:E,characterReferenceMarkerHexadecimal:vt,characterReferenceMarkerNumeric:vt,characterReferenceValue:se,characterReference:Lt,codeFenced:a(w),codeFencedFence:v,codeFencedFenceInfo:d,codeFencedFenceMeta:g,codeFlowValue:E,codeIndented:a(p),codeText:a(K),codeTextData:E,data:E,definition:a(),definitionDestinationString:C,definitionLabelString:m,definitionTitleString:y,emphasis:a(),hardBreakEscape:a(N),hardBreakTrailing:a(N),htmlFlow:a(Y),htmlFlowData:E,htmlText:a(J),htmlTextData:E,image:a(F),label:z,labelText:L,lineEnding:_,link:a(ge),listItem:a(),listOrdered:a(),listUnordered:a(),paragraph:a(),referenceString:fe,resourceDestinationString:x,resourceTitleString:W,resource:ee,setextHeading:a(A),setextHeadingLineSequence:P,setextHeadingText:k,strong:a(),thematicBreak:a()}};e0(t,(e||{}).mdastExtensions||[]);const n={};return r;function r(D){let I={type:"root",children:[]};const $={stack:[I],tokenStack:[],config:t,enter:l,exit:u,buffer:s,resume:c,data:n},q=[];let ne=-1;for(;++ne<D.length;)if(D[ne][1].type==="listOrdered"||D[ne][1].type==="listUnordered")if(D[ne][0]==="enter")q.push(ne);else{const xt=q.pop();ne=i(D,xt,ne)}for(ne=-1;++ne<D.length;){const xt=t[D[ne][0]];Jy.call(xt,D[ne][1].type)&&xt[D[ne][1].type].call(Object.assign({sliceSerialize:D[ne][2].sliceSerialize},$),D[ne][1])}if($.tokenStack.length>0){const xt=$.tokenStack[$.tokenStack.length-1];(xt[1]||yd).call($,void 0,xt[0])}for(I.position={start:rn(D.length>0?D[0][1].start:{line:1,column:1,offset:0}),end:rn(D.length>0?D[D.length-2][1].end:{line:1,column:1,offset:0})},ne=-1;++ne<t.transforms.length;)I=t.transforms[ne](I)||I;return I}function i(D,I,$){let q=I-1,ne=-1,xt=!1,bn,jt,Gr,Yr;for(;++q<=$;){const Xe=D[q];switch(Xe[1].type){case"listUnordered":case"listOrdered":case"blockQuote":{Xe[0]==="enter"?ne++:ne--,Yr=void 0;break}case"lineEndingBlank":{Xe[0]==="enter"&&(bn&&!Yr&&!ne&&!Gr&&(Gr=q),Yr=void 0);break}case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:Yr=void 0}if(!ne&&Xe[0]==="enter"&&Xe[1].type==="listItemPrefix"||ne===-1&&Xe[0]==="exit"&&(Xe[1].type==="listUnordered"||Xe[1].type==="listOrdered")){if(bn){let rr=q;for(jt=void 0;rr--;){const Vt=D[rr];if(Vt[1].type==="lineEnding"||Vt[1].type==="lineEndingBlank"){if(Vt[0]==="exit")continue;jt&&(D[jt][1].type="lineEndingBlank",xt=!0),Vt[1].type="lineEnding",jt=rr}else if(!(Vt[1].type==="linePrefix"||Vt[1].type==="blockQuotePrefix"||Vt[1].type==="blockQuotePrefixWhitespace"||Vt[1].type==="blockQuoteMarker"||Vt[1].type==="listItemIndent"))break}Gr&&(!jt||Gr<jt)&&(bn._spread=!0),bn.end=Object.assign({},jt?D[jt][1].start:Xe[1].end),D.splice(jt||q,0,["exit",bn,Xe[2]]),q++,$++}if(Xe[1].type==="listItemPrefix"){const rr={type:"listItem",_spread:!1,start:Object.assign({},Xe[1].start),end:void 0};bn=rr,D.splice(q,0,["enter",rr,Xe[2]]),q++,$++,Gr=void 0,Yr=!0}}}return D[I][1]._spread=xt,$}function o(D,I){return $;function $(q){l.call(this,D(q),q),I&&I.call(this,q)}}function s(){this.stack.push({type:"fragment",children:[]})}function l(D,I,$){this.stack[this.stack.length-1].children.push(D),this.stack.push(D),this.tokenStack.push([I,$]),D.position={start:rn(I.start),end:void 0}}function a(D){return I;function I($){D&&D.call(this,$),u.call(this,$)}}function u(D,I){const $=this.stack.pop(),q=this.tokenStack.pop();if(q)q[0].type!==D.type&&(I?I.call(this,D,q[0]):(q[1]||yd).call(this,D,q[0]));else throw new Error("Cannot close `"+D.type+"` ("+Ci({start:D.start,end:D.end})+"): it’s not open");$.position.end=rn(D.end)}function c(){return Oc(this.stack.pop())}function f(){this.data.expectingFirstListItemValue=!0}function h(D){if(this.data.expectingFirstListItemValue){const I=this.stack[this.stack.length-2];I.start=Number.parseInt(this.sliceSerialize(D),10),this.data.expectingFirstListItemValue=void 0}}function d(){const D=this.resume(),I=this.stack[this.stack.length-1];I.lang=D}function g(){const D=this.resume(),I=this.stack[this.stack.length-1];I.meta=D}function v(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)}function w(){const D=this.resume(),I=this.stack[this.stack.length-1];I.value=D.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}function p(){const D=this.resume(),I=this.stack[this.stack.length-1];I.value=D.replace(/(\r?\n|\r)$/g,"")}function m(D){const I=this.resume(),$=this.stack[this.stack.length-1];$.label=I,$.identifier=At(this.sliceSerialize(D)).toLowerCase()}function y(){const D=this.resume(),I=this.stack[this.stack.length-1];I.title=D}function C(){const D=this.resume(),I=this.stack[this.stack.length-1];I.url=D}function T(D){const I=this.stack[this.stack.length-1];if(!I.depth){const $=this.sliceSerialize(D).length;I.depth=$}}function k(){this.data.setextHeadingSlurpLineEnding=!0}function P(D){const I=this.stack[this.stack.length-1];I.depth=this.sliceSerialize(D).codePointAt(0)===61?1:2}function A(){this.data.setextHeadingSlurpLineEnding=void 0}function O(D){const $=this.stack[this.stack.length-1].children;let q=$[$.length-1];(!q||q.type!=="text")&&(q=p1(),q.position={start:rn(D.start),end:void 0},$.push(q)),this.stack.push(q)}function E(D){const I=this.stack.pop();I.value+=this.sliceSerialize(D),I.position.end=rn(D.end)}function _(D){const I=this.stack[this.stack.length-1];if(this.data.atHardBreak){const $=I.children[I.children.length-1];$.position.end=rn(D.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(I.type)&&(O.call(this,D),E.call(this,D))}function N(){this.data.atHardBreak=!0}function Y(){const D=this.resume(),I=this.stack[this.stack.length-1];I.value=D}function J(){const D=this.resume(),I=this.stack[this.stack.length-1];I.value=D}function K(){const D=this.resume(),I=this.stack[this.stack.length-1];I.value=D}function ge(){const D=this.stack[this.stack.length-1];if(this.data.inReference){const I=this.data.referenceType||"shortcut";D.type+="Reference",D.referenceType=I,delete D.url,delete D.title}else delete D.identifier,delete D.label;this.data.referenceType=void 0}function F(){const D=this.stack[this.stack.length-1];if(this.data.inReference){const I=this.data.referenceType||"shortcut";D.type+="Reference",D.referenceType=I,delete D.url,delete D.title}else delete D.identifier,delete D.label;this.data.referenceType=void 0}function L(D){const I=this.sliceSerialize(D),$=this.stack[this.stack.length-2];$.label=cE(I),$.identifier=At(I).toLowerCase()}function z(){const D=this.stack[this.stack.length-1],I=this.resume(),$=this.stack[this.stack.length-1];if(this.data.inReference=!0,$.type==="link"){const q=D.children;$.children=q}else $.alt=I}function x(){const D=this.resume(),I=this.stack[this.stack.length-1];I.url=D}function W(){const D=this.resume(),I=this.stack[this.stack.length-1];I.title=D}function ee(){this.data.inReference=void 0}function S(){this.data.referenceType="collapsed"}function fe(D){const I=this.resume(),$=this.stack[this.stack.length-1];$.label=I,$.identifier=At(this.sliceSerialize(D)).toLowerCase(),this.data.referenceType="full"}function vt(D){this.data.characterReferenceType=D.type}function se(D){const I=this.sliceSerialize(D),$=this.data.characterReferenceType;let q;$?(q=$y(I,$==="characterReferenceMarkerNumeric"?10:16),this.data.characterReferenceType=void 0):q=zc(I);const ne=this.stack[this.stack.length-1];ne.value+=q}function Lt(D){const I=this.stack.pop();I.position.end=rn(D.end)}function tn(D){E.call(this,D);const I=this.stack[this.stack.length-1];I.url=this.sliceSerialize(D)}function tr(D){E.call(this,D);const I=this.stack[this.stack.length-1];I.url="mailto:"+this.sliceSerialize(D)}function nr(){return{type:"blockquote",children:[]}}function co(){return{type:"code",lang:null,meta:null,value:""}}function l1(){return{type:"inlineCode",value:""}}function a1(){return{type:"definition",identifier:"",label:null,title:null,url:""}}function u1(){return{type:"emphasis",children:[]}}function Df(){return{type:"heading",depth:0,children:[]}}function Lf(){return{type:"break"}}function bf(){return{type:"html",value:""}}function c1(){return{type:"image",title:null,url:"",alt:null}}function Rf(){return{type:"link",title:null,url:"",children:[]}}function Mf(D){return{type:"list",ordered:D.type==="listOrdered",start:null,spread:D._spread,children:[]}}function f1(D){return{type:"listItem",spread:D._spread,checked:null,children:[]}}function h1(){return{type:"paragraph",children:[]}}function d1(){return{type:"strong",children:[]}}function p1(){return{type:"text",value:""}}function m1(){return{type:"thematicBreak"}}}function rn(e){return{line:e.line,column:e.column,offset:e.offset}}function e0(e,t){let n=-1;for(;++n<t.length;){const r=t[n];Array.isArray(r)?e0(e,r):pE(e,r)}}function pE(e,t){let n;for(n in t)if(Jy.call(t,n))switch(n){case"canContainEols":{const r=t[n];r&&e[n].push(...r);break}case"transforms":{const r=t[n];r&&e[n].push(...r);break}case"enter":case"exit":{const r=t[n];r&&Object.assign(e[n],r);break}}}function yd(e,t){throw e?new Error("Cannot close `"+e.type+"` ("+Ci({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+Ci({start:t.start,end:t.end})+") is open"):new Error("Cannot close document, a token (`"+t.type+"`, "+Ci({start:t.start,end:t.end})+") is still open")}function mE(e){const t=this;t.parser=n;function n(r){return hE(r,{...t.data("settings"),...e,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})}}function gE(e,t){const n={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(t),!0)};return e.patch(t,n),e.applyData(t,n)}function yE(e,t){const n={type:"element",tagName:"br",properties:{},children:[]};return e.patch(t,n),[e.applyData(t,n),{type:"text",value:`
`}]}function vE(e,t){const n=t.value?t.value+`
`:"",r={};t.lang&&(r.className=["language-"+t.lang]);let i={type:"element",tagName:"code",properties:r,children:[{type:"text",value:n}]};return t.meta&&(i.data={meta:t.meta}),e.patch(t,i),i=e.applyData(t,i),i={type:"element",tagName:"pre",properties:{},children:[i]},e.patch(t,i),i}function xE(e,t){const n={type:"element",tagName:"del",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function wE(e,t){const n={type:"element",tagName:"em",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function kE(e,t){const n=typeof e.options.clobberPrefix=="string"?e.options.clobberPrefix:"user-content-",r=String(t.identifier).toUpperCase(),i=Wr(r.toLowerCase()),o=e.footnoteOrder.indexOf(r);let s,l=e.footnoteCounts.get(r);l===void 0?(l=0,e.footnoteOrder.push(r),s=e.footnoteOrder.length):s=o+1,l+=1,e.footnoteCounts.set(r,l);const a={type:"element",tagName:"a",properties:{href:"#"+n+"fn-"+i,id:n+"fnref-"+i+(l>1?"-"+l:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(s)}]};e.patch(t,a);const u={type:"element",tagName:"sup",properties:{},children:[a]};return e.patch(t,u),e.applyData(t,u)}function SE(e,t){const n={type:"element",tagName:"h"+t.depth,properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function CE(e,t){if(e.options.allowDangerousHtml){const n={type:"raw",value:t.value};return e.patch(t,n),e.applyData(t,n)}}function t0(e,t){const n=t.referenceType;let r="]";if(n==="collapsed"?r+="[]":n==="full"&&(r+="["+(t.label||t.identifier)+"]"),t.type==="imageReference")return[{type:"text",value:"!["+t.alt+r}];const i=e.all(t),o=i[0];o&&o.type==="text"?o.value="["+o.value:i.unshift({type:"text",value:"["});const s=i[i.length-1];return s&&s.type==="text"?s.value+=r:i.push({type:"text",value:r}),i}function EE(e,t){const n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return t0(e,t);const i={src:Wr(r.url||""),alt:t.alt};r.title!==null&&r.title!==void 0&&(i.title=r.title);const o={type:"element",tagName:"img",properties:i,children:[]};return e.patch(t,o),e.applyData(t,o)}function TE(e,t){const n={src:Wr(t.url)};t.alt!==null&&t.alt!==void 0&&(n.alt=t.alt),t.title!==null&&t.title!==void 0&&(n.title=t.title);const r={type:"element",tagName:"img",properties:n,children:[]};return e.patch(t,r),e.applyData(t,r)}function PE(e,t){const n={type:"text",value:t.value.replace(/\r?\n|\r/g," ")};e.patch(t,n);const r={type:"element",tagName:"code",properties:{},children:[n]};return e.patch(t,r),e.applyData(t,r)}function AE(e,t){const n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return t0(e,t);const i={href:Wr(r.url||"")};r.title!==null&&r.title!==void 0&&(i.title=r.title);const o={type:"element",tagName:"a",properties:i,children:e.all(t)};return e.patch(t,o),e.applyData(t,o)}function DE(e,t){const n={href:Wr(t.url)};t.title!==null&&t.title!==void 0&&(n.title=t.title);const r={type:"element",tagName:"a",properties:n,children:e.all(t)};return e.patch(t,r),e.applyData(t,r)}function LE(e,t,n){const r=e.all(t),i=n?bE(n):n0(t),o={},s=[];if(typeof t.checked=="boolean"){const c=r[0];let f;c&&c.type==="element"&&c.tagName==="p"?f=c:(f={type:"element",tagName:"p",properties:{},children:[]},r.unshift(f)),f.children.length>0&&f.children.unshift({type:"text",value:" "}),f.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:t.checked,disabled:!0},children:[]}),o.className=["task-list-item"]}let l=-1;for(;++l<r.length;){const c=r[l];(i||l!==0||c.type!=="element"||c.tagName!=="p")&&s.push({type:"text",value:`
`}),c.type==="element"&&c.tagName==="p"&&!i?s.push(...c.children):s.push(c)}const a=r[r.length-1];a&&(i||a.type!=="element"||a.tagName!=="p")&&s.push({type:"text",value:`
`});const u={type:"element",tagName:"li",properties:o,children:s};return e.patch(t,u),e.applyData(t,u)}function bE(e){let t=!1;if(e.type==="list"){t=e.spread||!1;const n=e.children;let r=-1;for(;!t&&++r<n.length;)t=n0(n[r])}return t}function n0(e){const t=e.spread;return t??e.children.length>1}function RE(e,t){const n={},r=e.all(t);let i=-1;for(typeof t.start=="number"&&t.start!==1&&(n.start=t.start);++i<r.length;){const s=r[i];if(s.type==="element"&&s.tagName==="li"&&s.properties&&Array.isArray(s.properties.className)&&s.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}const o={type:"element",tagName:t.ordered?"ol":"ul",properties:n,children:e.wrap(r,!0)};return e.patch(t,o),e.applyData(t,o)}function ME(e,t){const n={type:"element",tagName:"p",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function IE(e,t){const n={type:"root",children:e.wrap(e.all(t))};return e.patch(t,n),e.applyData(t,n)}function _E(e,t){const n={type:"element",tagName:"strong",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function OE(e,t){const n=e.all(t),r=n.shift(),i=[];if(r){const s={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(t.children[0],s),i.push(s)}if(n.length>0){const s={type:"element",tagName:"tbody",properties:{},children:e.wrap(n,!0)},l=Rc(t.children[1]),a=Oy(t.children[t.children.length-1]);l&&a&&(s.position={start:l,end:a}),i.push(s)}const o={type:"element",tagName:"table",properties:{},children:e.wrap(i,!0)};return e.patch(t,o),e.applyData(t,o)}function zE(e,t,n){const r=n?n.children:void 0,o=(r?r.indexOf(t):1)===0?"th":"td",s=n&&n.type==="table"?n.align:void 0,l=s?s.length:t.children.length;let a=-1;const u=[];for(;++a<l;){const f=t.children[a],h={},d=s?s[a]:void 0;d&&(h.align=d);let g={type:"element",tagName:o,properties:h,children:[]};f&&(g.children=e.all(f),e.patch(f,g),g=e.applyData(f,g)),u.push(g)}const c={type:"element",tagName:"tr",properties:{},children:e.wrap(u,!0)};return e.patch(t,c),e.applyData(t,c)}function FE(e,t){const n={type:"element",tagName:"td",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}const vd=9,xd=32;function NE(e){const t=String(e),n=/\r?\n|\r/g;let r=n.exec(t),i=0;const o=[];for(;r;)o.push(wd(t.slice(i,r.index),i>0,!0),r[0]),i=r.index+r[0].length,r=n.exec(t);return o.push(wd(t.slice(i),i>0,!1)),o.join("")}function wd(e,t,n){let r=0,i=e.length;if(t){let o=e.codePointAt(r);for(;o===vd||o===xd;)r++,o=e.codePointAt(r)}if(n){let o=e.codePointAt(i-1);for(;o===vd||o===xd;)i--,o=e.codePointAt(i-1)}return i>r?e.slice(r,i):""}function jE(e,t){const n={type:"text",value:NE(String(t.value))};return e.patch(t,n),e.applyData(t,n)}function VE(e,t){const n={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(t,n),e.applyData(t,n)}const BE={blockquote:gE,break:yE,code:vE,delete:xE,emphasis:wE,footnoteReference:kE,heading:SE,html:CE,imageReference:EE,image:TE,inlineCode:PE,linkReference:AE,link:DE,listItem:LE,list:RE,paragraph:ME,root:IE,strong:_E,table:OE,tableCell:FE,tableRow:zE,text:jE,thematicBreak:VE,toml:Lo,yaml:Lo,definition:Lo,footnoteDefinition:Lo};function Lo(){}const r0=-1,nl=0,Ds=1,Ls=2,Nc=3,jc=4,Vc=5,Bc=6,i0=7,o0=8,kd=typeof self=="object"?self:globalThis,UE=(e,t)=>{const n=(i,o)=>(e.set(o,i),i),r=i=>{if(e.has(i))return e.get(i);const[o,s]=t[i];switch(o){case nl:case r0:return n(s,i);case Ds:{const l=n([],i);for(const a of s)l.push(r(a));return l}case Ls:{const l=n({},i);for(const[a,u]of s)l[r(a)]=r(u);return l}case Nc:return n(new Date(s),i);case jc:{const{source:l,flags:a}=s;return n(new RegExp(l,a),i)}case Vc:{const l=n(new Map,i);for(const[a,u]of s)l.set(r(a),r(u));return l}case Bc:{const l=n(new Set,i);for(const a of s)l.add(r(a));return l}case i0:{const{name:l,message:a}=s;return n(new kd[l](a),i)}case o0:return n(BigInt(s),i);case"BigInt":return n(Object(BigInt(s)),i)}return n(new kd[o](s),i)};return r},Sd=e=>UE(new Map,e)(0),or="",{toString:$E}={},{keys:HE}=Object,oi=e=>{const t=typeof e;if(t!=="object"||!e)return[nl,t];const n=$E.call(e).slice(8,-1);switch(n){case"Array":return[Ds,or];case"Object":return[Ls,or];case"Date":return[Nc,or];case"RegExp":return[jc,or];case"Map":return[Vc,or];case"Set":return[Bc,or]}return n.includes("Array")?[Ds,n]:n.includes("Error")?[i0,n]:[Ls,n]},bo=([e,t])=>e===nl&&(t==="function"||t==="symbol"),WE=(e,t,n,r)=>{const i=(s,l)=>{const a=r.push(s)-1;return n.set(l,a),a},o=s=>{if(n.has(s))return n.get(s);let[l,a]=oi(s);switch(l){case nl:{let c=s;switch(a){case"bigint":l=o0,c=s.toString();break;case"function":case"symbol":if(e)throw new TypeError("unable to serialize "+a);c=null;break;case"undefined":return i([r0],s)}return i([l,c],s)}case Ds:{if(a)return i([a,[...s]],s);const c=[],f=i([l,c],s);for(const h of s)c.push(o(h));return f}case Ls:{if(a)switch(a){case"BigInt":return i([a,s.toString()],s);case"Boolean":case"Number":case"String":return i([a,s.valueOf()],s)}if(t&&"toJSON"in s)return o(s.toJSON());const c=[],f=i([l,c],s);for(const h of HE(s))(e||!bo(oi(s[h])))&&c.push([o(h),o(s[h])]);return f}case Nc:return i([l,s.toISOString()],s);case jc:{const{source:c,flags:f}=s;return i([l,{source:c,flags:f}],s)}case Vc:{const c=[],f=i([l,c],s);for(const[h,d]of s)(e||!(bo(oi(h))||bo(oi(d))))&&c.push([o(h),o(d)]);return f}case Bc:{const c=[],f=i([l,c],s);for(const h of s)(e||!bo(oi(h)))&&c.push(o(h));return f}}const{message:u}=s;return i([l,{name:a,message:u}],s)};return o},Cd=(e,{json:t,lossy:n}={})=>{const r=[];return WE(!(t||n),!!t,new Map,r)(e),r},bs=typeof structuredClone=="function"?(e,t)=>t&&("json"in t||"lossy"in t)?Sd(Cd(e,t)):structuredClone(e):(e,t)=>Sd(Cd(e,t));function KE(e,t){const n=[{type:"text",value:"↩"}];return t>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(t)}]}),n}function QE(e,t){return"Back to reference "+(e+1)+(t>1?"-"+t:"")}function GE(e){const t=typeof e.options.clobberPrefix=="string"?e.options.clobberPrefix:"user-content-",n=e.options.footnoteBackContent||KE,r=e.options.footnoteBackLabel||QE,i=e.options.footnoteLabel||"Footnotes",o=e.options.footnoteLabelTagName||"h2",s=e.options.footnoteLabelProperties||{className:["sr-only"]},l=[];let a=-1;for(;++a<e.footnoteOrder.length;){const u=e.footnoteById.get(e.footnoteOrder[a]);if(!u)continue;const c=e.all(u),f=String(u.identifier).toUpperCase(),h=Wr(f.toLowerCase());let d=0;const g=[],v=e.footnoteCounts.get(f);for(;v!==void 0&&++d<=v;){g.length>0&&g.push({type:"text",value:" "});let m=typeof n=="string"?n:n(a,d);typeof m=="string"&&(m={type:"text",value:m}),g.push({type:"element",tagName:"a",properties:{href:"#"+t+"fnref-"+h+(d>1?"-"+d:""),dataFootnoteBackref:"",ariaLabel:typeof r=="string"?r:r(a,d),className:["data-footnote-backref"]},children:Array.isArray(m)?m:[m]})}const w=c[c.length-1];if(w&&w.type==="element"&&w.tagName==="p"){const m=w.children[w.children.length-1];m&&m.type==="text"?m.value+=" ":w.children.push({type:"text",value:" "}),w.children.push(...g)}else c.push(...g);const p={type:"element",tagName:"li",properties:{id:t+"fn-"+h},children:e.wrap(c,!0)};e.patch(u,p),l.push(p)}if(l.length!==0)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:o,properties:{...bs(s),id:"footnote-label"},children:[{type:"text",value:i}]},{type:"text",value:`
`},{type:"element",tagName:"ol",properties:{},children:e.wrap(l,!0)},{type:"text",value:`
`}]}}const rl=function(e){if(e==null)return ZE;if(typeof e=="function")return il(e);if(typeof e=="object")return Array.isArray(e)?YE(e):qE(e);if(typeof e=="string")return XE(e);throw new Error("Expected function, string, or object as test")};function YE(e){const t=[];let n=-1;for(;++n<e.length;)t[n]=rl(e[n]);return il(r);function r(...i){let o=-1;for(;++o<t.length;)if(t[o].apply(this,i))return!0;return!1}}function qE(e){const t=e;return il(n);function n(r){const i=r;let o;for(o in e)if(i[o]!==t[o])return!1;return!0}}function XE(e){return il(t);function t(n){return n&&n.type===e}}function il(e){return t;function t(n,r,i){return!!(JE(n)&&e.call(this,n,typeof r=="number"?r:void 0,i||void 0))}}function ZE(){return!0}function JE(e){return e!==null&&typeof e=="object"&&"type"in e}const s0=[],eT=!0,hu=!1,tT="skip";function l0(e,t,n,r){let i;typeof t=="function"&&typeof n!="function"?(r=n,n=t):i=t;const o=rl(i),s=r?-1:1;l(e,void 0,[])();function l(a,u,c){const f=a&&typeof a=="object"?a:{};if(typeof f.type=="string"){const d=typeof f.tagName=="string"?f.tagName:typeof f.name=="string"?f.name:void 0;Object.defineProperty(h,"name",{value:"node ("+(a.type+(d?"<"+d+">":""))+")"})}return h;function h(){let d=s0,g,v,w;if((!t||o(a,u,c[c.length-1]||void 0))&&(d=nT(n(a,c)),d[0]===hu))return d;if("children"in a&&a.children){const p=a;if(p.children&&d[0]!==tT)for(v=(r?p.children.length:-1)+s,w=c.concat(p);v>-1&&v<p.children.length;){const m=p.children[v];if(g=l(m,v,w)(),g[0]===hu)return g;v=typeof g[1]=="number"?g[1]:v+s}}return d}}}function nT(e){return Array.isArray(e)?e:typeof e=="number"?[eT,e]:e==null?s0:[e]}function Uc(e,t,n,r){let i,o,s;typeof t=="function"&&typeof n!="function"?(o=void 0,s=t,i=n):(o=t,s=n,i=r),l0(e,o,l,i);function l(a,u){const c=u[u.length-1],f=c?c.children.indexOf(a):void 0;return s(a,f,c)}}const du={}.hasOwnProperty,rT={};function iT(e,t){const n=t||rT,r=new Map,i=new Map,o=new Map,s={...BE,...n.handlers},l={all:u,applyData:sT,definitionById:r,footnoteById:i,footnoteCounts:o,footnoteOrder:[],handlers:s,one:a,options:n,patch:oT,wrap:aT};return Uc(e,function(c){if(c.type==="definition"||c.type==="footnoteDefinition"){const f=c.type==="definition"?r:i,h=String(c.identifier).toUpperCase();f.has(h)||f.set(h,c)}}),l;function a(c,f){const h=c.type,d=l.handlers[h];if(du.call(l.handlers,h)&&d)return d(l,c,f);if(l.options.passThrough&&l.options.passThrough.includes(h)){if("children"in c){const{children:v,...w}=c,p=bs(w);return p.children=l.all(c),p}return bs(c)}return(l.options.unknownHandler||lT)(l,c,f)}function u(c){const f=[];if("children"in c){const h=c.children;let d=-1;for(;++d<h.length;){const g=l.one(h[d],c);if(g){if(d&&h[d-1].type==="break"&&(!Array.isArray(g)&&g.type==="text"&&(g.value=Ed(g.value)),!Array.isArray(g)&&g.type==="element")){const v=g.children[0];v&&v.type==="text"&&(v.value=Ed(v.value))}Array.isArray(g)?f.push(...g):f.push(g)}}}return f}}function oT(e,t){e.position&&(t.position=Wk(e))}function sT(e,t){let n=t;if(e&&e.data){const r=e.data.hName,i=e.data.hChildren,o=e.data.hProperties;if(typeof r=="string")if(n.type==="element")n.tagName=r;else{const s="children"in n?n.children:[n];n={type:"element",tagName:r,properties:{},children:s}}n.type==="element"&&o&&Object.assign(n.properties,bs(o)),"children"in n&&n.children&&i!==null&&i!==void 0&&(n.children=i)}return n}function lT(e,t){const n=t.data||{},r="value"in t&&!(du.call(n,"hProperties")||du.call(n,"hChildren"))?{type:"text",value:t.value}:{type:"element",tagName:"div",properties:{},children:e.all(t)};return e.patch(t,r),e.applyData(t,r)}function aT(e,t){const n=[];let r=-1;for(t&&n.push({type:"text",value:`
`});++r<e.length;)r&&n.push({type:"text",value:`
`}),n.push(e[r]);return t&&e.length>0&&n.push({type:"text",value:`
`}),n}function Ed(e){let t=0,n=e.charCodeAt(t);for(;n===9||n===32;)t++,n=e.charCodeAt(t);return e.slice(t)}function Td(e,t){const n=iT(e,t),r=n.one(e,void 0),i=GE(n),o=Array.isArray(r)?{type:"root",children:r}:r||{type:"root",children:[]};return i&&o.children.push({type:"text",value:`
`},i),o}function uT(e,t){return e&&"run"in e?async function(n,r){const i=Td(n,{file:r,...t});await e.run(i,r)}:function(n,r){return Td(n,{file:r,...t||e})}}function Pd(e){if(e)throw e}var qo=Object.prototype.hasOwnProperty,a0=Object.prototype.toString,Ad=Object.defineProperty,Dd=Object.getOwnPropertyDescriptor,Ld=function(t){return typeof Array.isArray=="function"?Array.isArray(t):a0.call(t)==="[object Array]"},bd=function(t){if(!t||a0.call(t)!=="[object Object]")return!1;var n=qo.call(t,"constructor"),r=t.constructor&&t.constructor.prototype&&qo.call(t.constructor.prototype,"isPrototypeOf");if(t.constructor&&!n&&!r)return!1;var i;for(i in t);return typeof i>"u"||qo.call(t,i)},Rd=function(t,n){Ad&&n.name==="__proto__"?Ad(t,n.name,{enumerable:!0,configurable:!0,value:n.newValue,writable:!0}):t[n.name]=n.newValue},Md=function(t,n){if(n==="__proto__")if(qo.call(t,n)){if(Dd)return Dd(t,n).value}else return;return t[n]},cT=function e(){var t,n,r,i,o,s,l=arguments[0],a=1,u=arguments.length,c=!1;for(typeof l=="boolean"&&(c=l,l=arguments[1]||{},a=2),(l==null||typeof l!="object"&&typeof l!="function")&&(l={});a<u;++a)if(t=arguments[a],t!=null)for(n in t)r=Md(l,n),i=Md(t,n),l!==i&&(c&&i&&(bd(i)||(o=Ld(i)))?(o?(o=!1,s=r&&Ld(r)?r:[]):s=r&&bd(r)?r:{},Rd(l,{name:n,newValue:e(c,s,i)})):typeof i<"u"&&Rd(l,{name:n,newValue:i}));return l};const Bl=cm(cT);function pu(e){if(typeof e!="object"||e===null)return!1;const t=Object.getPrototypeOf(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}function fT(){const e=[],t={run:n,use:r};return t;function n(...i){let o=-1;const s=i.pop();if(typeof s!="function")throw new TypeError("Expected function as last argument, not "+s);l(null,...i);function l(a,...u){const c=e[++o];let f=-1;if(a){s(a);return}for(;++f<i.length;)(u[f]===null||u[f]===void 0)&&(u[f]=i[f]);i=u,c?hT(c,l)(...u):s(null,...u)}}function r(i){if(typeof i!="function")throw new TypeError("Expected `middelware` to be a function, not "+i);return e.push(i),t}}function hT(e,t){let n;return r;function r(...s){const l=e.length>s.length;let a;l&&s.push(i);try{a=e.apply(this,s)}catch(u){const c=u;if(l&&n)throw c;return i(c)}l||(a&&a.then&&typeof a.then=="function"?a.then(o,i):a instanceof Error?i(a):o(a))}function i(s,...l){n||(n=!0,t(s,...l))}function o(s){i(null,s)}}const Mt={basename:dT,dirname:pT,extname:mT,join:gT,sep:"/"};function dT(e,t){if(t!==void 0&&typeof t!="string")throw new TypeError('"ext" argument must be a string');oo(e);let n=0,r=-1,i=e.length,o;if(t===void 0||t.length===0||t.length>e.length){for(;i--;)if(e.codePointAt(i)===47){if(o){n=i+1;break}}else r<0&&(o=!0,r=i+1);return r<0?"":e.slice(n,r)}if(t===e)return"";let s=-1,l=t.length-1;for(;i--;)if(e.codePointAt(i)===47){if(o){n=i+1;break}}else s<0&&(o=!0,s=i+1),l>-1&&(e.codePointAt(i)===t.codePointAt(l--)?l<0&&(r=i):(l=-1,r=s));return n===r?r=s:r<0&&(r=e.length),e.slice(n,r)}function pT(e){if(oo(e),e.length===0)return".";let t=-1,n=e.length,r;for(;--n;)if(e.codePointAt(n)===47){if(r){t=n;break}}else r||(r=!0);return t<0?e.codePointAt(0)===47?"/":".":t===1&&e.codePointAt(0)===47?"//":e.slice(0,t)}function mT(e){oo(e);let t=e.length,n=-1,r=0,i=-1,o=0,s;for(;t--;){const l=e.codePointAt(t);if(l===47){if(s){r=t+1;break}continue}n<0&&(s=!0,n=t+1),l===46?i<0?i=t:o!==1&&(o=1):i>-1&&(o=-1)}return i<0||n<0||o===0||o===1&&i===n-1&&i===r+1?"":e.slice(i,n)}function gT(...e){let t=-1,n;for(;++t<e.length;)oo(e[t]),e[t]&&(n=n===void 0?e[t]:n+"/"+e[t]);return n===void 0?".":yT(n)}function yT(e){oo(e);const t=e.codePointAt(0)===47;let n=vT(e,!t);return n.length===0&&!t&&(n="."),n.length>0&&e.codePointAt(e.length-1)===47&&(n+="/"),t?"/"+n:n}function vT(e,t){let n="",r=0,i=-1,o=0,s=-1,l,a;for(;++s<=e.length;){if(s<e.length)l=e.codePointAt(s);else{if(l===47)break;l=47}if(l===47){if(!(i===s-1||o===1))if(i!==s-1&&o===2){if(n.length<2||r!==2||n.codePointAt(n.length-1)!==46||n.codePointAt(n.length-2)!==46){if(n.length>2){if(a=n.lastIndexOf("/"),a!==n.length-1){a<0?(n="",r=0):(n=n.slice(0,a),r=n.length-1-n.lastIndexOf("/")),i=s,o=0;continue}}else if(n.length>0){n="",r=0,i=s,o=0;continue}}t&&(n=n.length>0?n+"/..":"..",r=2)}else n.length>0?n+="/"+e.slice(i+1,s):n=e.slice(i+1,s),r=s-i-1;i=s,o=0}else l===46&&o>-1?o++:o=-1}return n}function oo(e){if(typeof e!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}const xT={cwd:wT};function wT(){return"/"}function mu(e){return!!(e!==null&&typeof e=="object"&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&e.auth===void 0)}function kT(e){if(typeof e=="string")e=new URL(e);else if(!mu(e)){const t=new TypeError('The "path" argument must be of type string or an instance of URL. Received `'+e+"`");throw t.code="ERR_INVALID_ARG_TYPE",t}if(e.protocol!=="file:"){const t=new TypeError("The URL must be of scheme file");throw t.code="ERR_INVALID_URL_SCHEME",t}return ST(e)}function ST(e){if(e.hostname!==""){const r=new TypeError('File URL host must be "localhost" or empty on darwin');throw r.code="ERR_INVALID_FILE_URL_HOST",r}const t=e.pathname;let n=-1;for(;++n<t.length;)if(t.codePointAt(n)===37&&t.codePointAt(n+1)===50){const r=t.codePointAt(n+2);if(r===70||r===102){const i=new TypeError("File URL path must not include encoded / characters");throw i.code="ERR_INVALID_FILE_URL_PATH",i}}return decodeURIComponent(t)}const Ul=["history","path","basename","stem","extname","dirname"];class u0{constructor(t){let n;t?mu(t)?n={path:t}:typeof t=="string"||CT(t)?n={value:t}:n=t:n={},this.cwd="cwd"in n?"":xT.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let r=-1;for(;++r<Ul.length;){const o=Ul[r];o in n&&n[o]!==void 0&&n[o]!==null&&(this[o]=o==="history"?[...n[o]]:n[o])}let i;for(i in n)Ul.includes(i)||(this[i]=n[i])}get basename(){return typeof this.path=="string"?Mt.basename(this.path):void 0}set basename(t){Hl(t,"basename"),$l(t,"basename"),this.path=Mt.join(this.dirname||"",t)}get dirname(){return typeof this.path=="string"?Mt.dirname(this.path):void 0}set dirname(t){Id(this.basename,"dirname"),this.path=Mt.join(t||"",this.basename)}get extname(){return typeof this.path=="string"?Mt.extname(this.path):void 0}set extname(t){if($l(t,"extname"),Id(this.dirname,"extname"),t){if(t.codePointAt(0)!==46)throw new Error("`extname` must start with `.`");if(t.includes(".",1))throw new Error("`extname` cannot contain multiple dots")}this.path=Mt.join(this.dirname,this.stem+(t||""))}get path(){return this.history[this.history.length-1]}set path(t){mu(t)&&(t=kT(t)),Hl(t,"path"),this.path!==t&&this.history.push(t)}get stem(){return typeof this.path=="string"?Mt.basename(this.path,this.extname):void 0}set stem(t){Hl(t,"stem"),$l(t,"stem"),this.path=Mt.join(this.dirname||"",t+(this.extname||""))}fail(t,n,r){const i=this.message(t,n,r);throw i.fatal=!0,i}info(t,n,r){const i=this.message(t,n,r);return i.fatal=void 0,i}message(t,n,r){const i=new Ne(t,n,r);return this.path&&(i.name=this.path+":"+i.name,i.file=this.path),i.fatal=!1,this.messages.push(i),i}toString(t){return this.value===void 0?"":typeof this.value=="string"?this.value:new TextDecoder(t||void 0).decode(this.value)}}function $l(e,t){if(e&&e.includes(Mt.sep))throw new Error("`"+t+"` cannot be a path: did not expect `"+Mt.sep+"`")}function Hl(e,t){if(!e)throw new Error("`"+t+"` cannot be empty")}function Id(e,t){if(!e)throw new Error("Setting `"+t+"` requires `path` to be set too")}function CT(e){return!!(e&&typeof e=="object"&&"byteLength"in e&&"byteOffset"in e)}const ET=function(e){const r=this.constructor.prototype,i=r[e],o=function(){return i.apply(o,arguments)};return Object.setPrototypeOf(o,r),o},TT={}.hasOwnProperty;class $c extends ET{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=fT()}copy(){const t=new $c;let n=-1;for(;++n<this.attachers.length;){const r=this.attachers[n];t.use(...r)}return t.data(Bl(!0,{},this.namespace)),t}data(t,n){return typeof t=="string"?arguments.length===2?(Ql("data",this.frozen),this.namespace[t]=n,this):TT.call(this.namespace,t)&&this.namespace[t]||void 0:t?(Ql("data",this.frozen),this.namespace=t,this):this.namespace}freeze(){if(this.frozen)return this;const t=this;for(;++this.freezeIndex<this.attachers.length;){const[n,...r]=this.attachers[this.freezeIndex];if(r[0]===!1)continue;r[0]===!0&&(r[0]=void 0);const i=n.call(t,...r);typeof i=="function"&&this.transformers.use(i)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(t){this.freeze();const n=Ro(t),r=this.parser||this.Parser;return Wl("parse",r),r(String(n),n)}process(t,n){const r=this;return this.freeze(),Wl("process",this.parser||this.Parser),Kl("process",this.compiler||this.Compiler),n?i(void 0,n):new Promise(i);function i(o,s){const l=Ro(t),a=r.parse(l);r.run(a,l,function(c,f,h){if(c||!f||!h)return u(c);const d=f,g=r.stringify(d,h);DT(g)?h.value=g:h.result=g,u(c,h)});function u(c,f){c||!f?s(c):o?o(f):n(void 0,f)}}}processSync(t){let n=!1,r;return this.freeze(),Wl("processSync",this.parser||this.Parser),Kl("processSync",this.compiler||this.Compiler),this.process(t,i),Od("processSync","process",n),r;function i(o,s){n=!0,Pd(o),r=s}}run(t,n,r){_d(t),this.freeze();const i=this.transformers;return!r&&typeof n=="function"&&(r=n,n=void 0),r?o(void 0,r):new Promise(o);function o(s,l){const a=Ro(n);i.run(t,a,u);function u(c,f,h){const d=f||t;c?l(c):s?s(d):r(void 0,d,h)}}}runSync(t,n){let r=!1,i;return this.run(t,n,o),Od("runSync","run",r),i;function o(s,l){Pd(s),i=l,r=!0}}stringify(t,n){this.freeze();const r=Ro(n),i=this.compiler||this.Compiler;return Kl("stringify",i),_d(t),i(t,r)}use(t,...n){const r=this.attachers,i=this.namespace;if(Ql("use",this.frozen),t!=null)if(typeof t=="function")a(t,n);else if(typeof t=="object")Array.isArray(t)?l(t):s(t);else throw new TypeError("Expected usable value, not `"+t+"`");return this;function o(u){if(typeof u=="function")a(u,[]);else if(typeof u=="object")if(Array.isArray(u)){const[c,...f]=u;a(c,f)}else s(u);else throw new TypeError("Expected usable value, not `"+u+"`")}function s(u){if(!("plugins"in u)&&!("settings"in u))throw new Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");l(u.plugins),u.settings&&(i.settings=Bl(!0,i.settings,u.settings))}function l(u){let c=-1;if(u!=null)if(Array.isArray(u))for(;++c<u.length;){const f=u[c];o(f)}else throw new TypeError("Expected a list of plugins, not `"+u+"`")}function a(u,c){let f=-1,h=-1;for(;++f<r.length;)if(r[f][0]===u){h=f;break}if(h===-1)r.push([u,...c]);else if(c.length>0){let[d,...g]=c;const v=r[h][1];pu(v)&&pu(d)&&(d=Bl(!0,v,d)),r[h]=[u,d,...g]}}}}const PT=new $c().freeze();function Wl(e,t){if(typeof t!="function")throw new TypeError("Cannot `"+e+"` without `parser`")}function Kl(e,t){if(typeof t!="function")throw new TypeError("Cannot `"+e+"` without `compiler`")}function Ql(e,t){if(t)throw new Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function _d(e){if(!pu(e)||typeof e.type!="string")throw new TypeError("Expected node, got `"+e+"`")}function Od(e,t,n){if(!n)throw new Error("`"+e+"` finished async. Use `"+t+"` instead")}function Ro(e){return AT(e)?e:new u0(e)}function AT(e){return!!(e&&typeof e=="object"&&"message"in e&&"messages"in e)}function DT(e){return typeof e=="string"||LT(e)}function LT(e){return!!(e&&typeof e=="object"&&"byteLength"in e&&"byteOffset"in e)}const bT="https://github.com/remarkjs/react-markdown/blob/main/changelog.md",zd=[],Fd={allowDangerousHtml:!0},RT=/^(https?|ircs?|mailto|xmpp)$/i,MT=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function Nd(e){const t=e.allowedElements,n=e.allowElement,r=e.children||"",i=e.className,o=e.components,s=e.disallowedElements,l=e.rehypePlugins||zd,a=e.remarkPlugins||zd,u=e.remarkRehypeOptions?{...e.remarkRehypeOptions,...Fd}:Fd,c=e.skipHtml,f=e.unwrapDisallowed,h=e.urlTransform||IT,d=PT().use(mE).use(a).use(uT,u).use(l),g=new u0;typeof r=="string"&&(g.value=r);for(const m of MT)Object.hasOwn(e,m.from)&&(""+m.from+(m.to?"use `"+m.to+"` instead":"remove it")+bT+m.id,void 0);const v=d.parse(g);let w=d.runSync(v,g);return i&&(w={type:"element",tagName:"div",properties:{className:i},children:w.type==="root"?w.children:[w]}),Uc(w,p),Xk(w,{Fragment:V.Fragment,components:o,ignoreInvalidStyle:!0,jsx:V.jsx,jsxs:V.jsxs,passKeys:!0,passNode:!0});function p(m,y,C){if(m.type==="raw"&&C&&typeof y=="number")return c?C.children.splice(y,1):C.children[y]={type:"text",value:m.value},y;if(m.type==="element"){let T;for(T in Nl)if(Object.hasOwn(Nl,T)&&Object.hasOwn(m.properties,T)){const k=m.properties[T],P=Nl[T];(P===null||P.includes(m.tagName))&&(m.properties[T]=h(String(k||""),T,m))}}if(m.type==="element"){let T=t?!t.includes(m.tagName):s?s.includes(m.tagName):!1;if(!T&&n&&typeof y=="number"&&(T=!n(m,y,C)),T&&C&&typeof y=="number")return f&&m.children?C.children.splice(y,1,...m.children):C.children.splice(y,1),y}}}function IT(e){const t=e.indexOf(":"),n=e.indexOf("?"),r=e.indexOf("#"),i=e.indexOf("/");return t<0||i>-1&&t>i||n>-1&&t>n||r>-1&&t>r||RT.test(e.slice(0,t))?e:""}function jd(e,t){const n=String(e);if(typeof t!="string")throw new TypeError("Expected character");let r=0,i=n.indexOf(t);for(;i!==-1;)r++,i=n.indexOf(t,i+t.length);return r}function _T(e){if(typeof e!="string")throw new TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}function OT(e,t,n){const i=rl((n||{}).ignore||[]),o=zT(t);let s=-1;for(;++s<o.length;)l0(e,"text",l);function l(u,c){let f=-1,h;for(;++f<c.length;){const d=c[f],g=h?h.children:void 0;if(i(d,g?g.indexOf(d):void 0,h))return;h=d}if(h)return a(u,c)}function a(u,c){const f=c[c.length-1],h=o[s][0],d=o[s][1];let g=0;const w=f.children.indexOf(u);let p=!1,m=[];h.lastIndex=0;let y=h.exec(u.value);for(;y;){const C=y.index,T={index:y.index,input:y.input,stack:[...c,u]};let k=d(...y,T);if(typeof k=="string"&&(k=k.length>0?{type:"text",value:k}:void 0),k===!1?h.lastIndex=C+1:(g!==C&&m.push({type:"text",value:u.value.slice(g,C)}),Array.isArray(k)?m.push(...k):k&&m.push(k),g=C+y[0].length,p=!0),!h.global)break;y=h.exec(u.value)}return p?(g<u.value.length&&m.push({type:"text",value:u.value.slice(g)}),f.children.splice(w,1,...m)):m=[u],w+m.length}}function zT(e){const t=[];if(!Array.isArray(e))throw new TypeError("Expected find and replace tuple or list of tuples");const n=!e[0]||Array.isArray(e[0])?e:[e];let r=-1;for(;++r<n.length;){const i=n[r];t.push([FT(i[0]),NT(i[1])])}return t}function FT(e){return typeof e=="string"?new RegExp(_T(e),"g"):e}function NT(e){return typeof e=="function"?e:function(){return e}}const Gl="phrasing",Yl=["autolink","link","image","label"];function jT(){return{transforms:[KT],enter:{literalAutolink:BT,literalAutolinkEmail:ql,literalAutolinkHttp:ql,literalAutolinkWww:ql},exit:{literalAutolink:WT,literalAutolinkEmail:HT,literalAutolinkHttp:UT,literalAutolinkWww:$T}}}function VT(){return{unsafe:[{character:"@",before:"[+\\-.\\w]",after:"[\\-.\\w]",inConstruct:Gl,notInConstruct:Yl},{character:".",before:"[Ww]",after:"[\\-.\\w]",inConstruct:Gl,notInConstruct:Yl},{character:":",before:"[ps]",after:"\\/",inConstruct:Gl,notInConstruct:Yl}]}}function BT(e){this.enter({type:"link",title:null,url:"",children:[]},e)}function ql(e){this.config.enter.autolinkProtocol.call(this,e)}function UT(e){this.config.exit.autolinkProtocol.call(this,e)}function $T(e){this.config.exit.data.call(this,e);const t=this.stack[this.stack.length-1];t.type,t.url="http://"+this.sliceSerialize(e)}function HT(e){this.config.exit.autolinkEmail.call(this,e)}function WT(e){this.exit(e)}function KT(e){OT(e,[[/(https?:\/\/|www(?=\.))([-.\w]+)([^ \t\r\n]*)/gi,QT],[/([-.\w+]+)@([-\w]+(?:\.[-\w]+)+)/g,GT]],{ignore:["link","linkReference"]})}function QT(e,t,n,r,i){let o="";if(!c0(i)||(/^w/i.test(t)&&(n=t+n,t="",o="http://"),!YT(n)))return!1;const s=qT(n+r);if(!s[0])return!1;const l={type:"link",title:null,url:o+t+s[0],children:[{type:"text",value:t+s[0]}]};return s[1]?[l,{type:"text",value:s[1]}]:l}function GT(e,t,n,r){return!c0(r,!0)||/[-\d_]$/.test(n)?!1:{type:"link",title:null,url:"mailto:"+t+"@"+n,children:[{type:"text",value:t+"@"+n}]}}function YT(e){const t=e.split(".");return!(t.length<2||t[t.length-1]&&(/_/.test(t[t.length-1])||!/[a-zA-Z\d]/.test(t[t.length-1]))||t[t.length-2]&&(/_/.test(t[t.length-2])||!/[a-zA-Z\d]/.test(t[t.length-2])))}function qT(e){const t=/[!"&'),.:;<>?\]}]+$/.exec(e);if(!t)return[e,void 0];e=e.slice(0,t.index);let n=t[0],r=n.indexOf(")");const i=jd(e,"(");let o=jd(e,")");for(;r!==-1&&i>o;)e+=n.slice(0,r+1),n=n.slice(r+1),r=n.indexOf(")"),o++;return[e,n]}function c0(e,t){const n=e.input.charCodeAt(e.index-1);return(e.index===0||qn(n)||el(n))&&(!t||n!==47)}f0.peek=lP;function XT(){return{enter:{gfmFootnoteDefinition:JT,gfmFootnoteDefinitionLabelString:eP,gfmFootnoteCall:rP,gfmFootnoteCallString:iP},exit:{gfmFootnoteDefinition:nP,gfmFootnoteDefinitionLabelString:tP,gfmFootnoteCall:sP,gfmFootnoteCallString:oP}}}function ZT(){return{unsafe:[{character:"[",inConstruct:["phrasing","label","reference"]}],handlers:{footnoteDefinition:aP,footnoteReference:f0}}}function JT(e){this.enter({type:"footnoteDefinition",identifier:"",label:"",children:[]},e)}function eP(){this.buffer()}function tP(e){const t=this.resume(),n=this.stack[this.stack.length-1];n.type,n.label=t,n.identifier=At(this.sliceSerialize(e)).toLowerCase()}function nP(e){this.exit(e)}function rP(e){this.enter({type:"footnoteReference",identifier:"",label:""},e)}function iP(){this.buffer()}function oP(e){const t=this.resume(),n=this.stack[this.stack.length-1];n.type,n.label=t,n.identifier=At(this.sliceSerialize(e)).toLowerCase()}function sP(e){this.exit(e)}function f0(e,t,n,r){const i=n.createTracker(r);let o=i.move("[^");const s=n.enter("footnoteReference"),l=n.enter("reference");return o+=i.move(n.safe(n.associationId(e),{...i.current(),before:o,after:"]"})),l(),s(),o+=i.move("]"),o}function lP(){return"["}function aP(e,t,n,r){const i=n.createTracker(r);let o=i.move("[^");const s=n.enter("footnoteDefinition"),l=n.enter("label");return o+=i.move(n.safe(n.associationId(e),{...i.current(),before:o,after:"]"})),l(),o+=i.move("]:"+(e.children&&e.children.length>0?" ":"")),i.shift(4),o+=i.move(n.indentLines(n.containerFlow(e,i.current()),uP)),s(),o}function uP(e,t,n){return t===0?e:(n?"":"    ")+e}const cP=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"];h0.peek=mP;function fP(){return{canContainEols:["delete"],enter:{strikethrough:dP},exit:{strikethrough:pP}}}function hP(){return{unsafe:[{character:"~",inConstruct:"phrasing",notInConstruct:cP}],handlers:{delete:h0}}}function dP(e){this.enter({type:"delete",children:[]},e)}function pP(e){this.exit(e)}function h0(e,t,n,r){const i=n.createTracker(r),o=n.enter("strikethrough");let s=i.move("~~");return s+=n.containerPhrasing(e,{...i.current(),before:s,after:"~"}),s+=i.move("~~"),o(),s}function mP(){return"~"}function gP(e,t={}){const n=(t.align||[]).concat(),r=t.stringLength||vP,i=[],o=[],s=[],l=[];let a=0,u=-1;for(;++u<e.length;){const g=[],v=[];let w=-1;for(e[u].length>a&&(a=e[u].length);++w<e[u].length;){const p=yP(e[u][w]);if(t.alignDelimiters!==!1){const m=r(p);v[w]=m,(l[w]===void 0||m>l[w])&&(l[w]=m)}g.push(p)}o[u]=g,s[u]=v}let c=-1;if(typeof n=="object"&&"length"in n)for(;++c<a;)i[c]=Vd(n[c]);else{const g=Vd(n);for(;++c<a;)i[c]=g}c=-1;const f=[],h=[];for(;++c<a;){const g=i[c];let v="",w="";g===99?(v=":",w=":"):g===108?v=":":g===114&&(w=":");let p=t.alignDelimiters===!1?1:Math.max(1,l[c]-v.length-w.length);const m=v+"-".repeat(p)+w;t.alignDelimiters!==!1&&(p=v.length+p+w.length,p>l[c]&&(l[c]=p),h[c]=p),f[c]=m}o.splice(1,0,f),s.splice(1,0,h),u=-1;const d=[];for(;++u<o.length;){const g=o[u],v=s[u];c=-1;const w=[];for(;++c<a;){const p=g[c]||"";let m="",y="";if(t.alignDelimiters!==!1){const C=l[c]-(v[c]||0),T=i[c];T===114?m=" ".repeat(C):T===99?C%2?(m=" ".repeat(C/2+.5),y=" ".repeat(C/2-.5)):(m=" ".repeat(C/2),y=m):y=" ".repeat(C)}t.delimiterStart!==!1&&!c&&w.push("|"),t.padding!==!1&&!(t.alignDelimiters===!1&&p==="")&&(t.delimiterStart!==!1||c)&&w.push(" "),t.alignDelimiters!==!1&&w.push(m),w.push(p),t.alignDelimiters!==!1&&w.push(y),t.padding!==!1&&w.push(" "),(t.delimiterEnd!==!1||c!==a-1)&&w.push("|")}d.push(t.delimiterEnd===!1?w.join("").replace(/ +$/,""):w.join(""))}return d.join(`
`)}function yP(e){return e==null?"":String(e)}function vP(e){return e.length}function Vd(e){const t=typeof e=="string"?e.codePointAt(0):0;return t===67||t===99?99:t===76||t===108?108:t===82||t===114?114:0}function xP(e,t,n,r){const i=n.enter("blockquote"),o=n.createTracker(r);o.move("> "),o.shift(2);const s=n.indentLines(n.containerFlow(e,o.current()),wP);return i(),s}function wP(e,t,n){return">"+(n?"":" ")+e}function kP(e,t){return Bd(e,t.inConstruct,!0)&&!Bd(e,t.notInConstruct,!1)}function Bd(e,t,n){if(typeof t=="string"&&(t=[t]),!t||t.length===0)return n;let r=-1;for(;++r<t.length;)if(e.includes(t[r]))return!0;return!1}function Ud(e,t,n,r){let i=-1;for(;++i<n.unsafe.length;)if(n.unsafe[i].character===`
`&&kP(n.stack,n.unsafe[i]))return/[ \t]/.test(r.before)?"":" ";return`\\
`}function SP(e,t){const n=String(e);let r=n.indexOf(t),i=r,o=0,s=0;if(typeof t!="string")throw new TypeError("Expected substring");for(;r!==-1;)r===i?++o>s&&(s=o):o=1,i=r+t.length,r=n.indexOf(t,i);return s}function CP(e,t){return!!(t.options.fences===!1&&e.value&&!e.lang&&/[^ \r\n]/.test(e.value)&&!/^[\t ]*(?:[\r\n]|$)|(?:^|[\r\n])[\t ]*$/.test(e.value))}function EP(e){const t=e.options.fence||"`";if(t!=="`"&&t!=="~")throw new Error("Cannot serialize code with `"+t+"` for `options.fence`, expected `` ` `` or `~`");return t}function TP(e,t,n,r){const i=EP(n),o=e.value||"",s=i==="`"?"GraveAccent":"Tilde";if(CP(e,n)){const f=n.enter("codeIndented"),h=n.indentLines(o,PP);return f(),h}const l=n.createTracker(r),a=i.repeat(Math.max(SP(o,i)+1,3)),u=n.enter("codeFenced");let c=l.move(a);if(e.lang){const f=n.enter(`codeFencedLang${s}`);c+=l.move(n.safe(e.lang,{before:c,after:" ",encode:["`"],...l.current()})),f()}if(e.lang&&e.meta){const f=n.enter(`codeFencedMeta${s}`);c+=l.move(" "),c+=l.move(n.safe(e.meta,{before:c,after:`
`,encode:["`"],...l.current()})),f()}return c+=l.move(`
`),o&&(c+=l.move(o+`
`)),c+=l.move(a),u(),c}function PP(e,t,n){return(n?"":"    ")+e}function Hc(e){const t=e.options.quote||'"';if(t!=='"'&&t!=="'")throw new Error("Cannot serialize title with `"+t+"` for `options.quote`, expected `\"`, or `'`");return t}function AP(e,t,n,r){const i=Hc(n),o=i==='"'?"Quote":"Apostrophe",s=n.enter("definition");let l=n.enter("label");const a=n.createTracker(r);let u=a.move("[");return u+=a.move(n.safe(n.associationId(e),{before:u,after:"]",...a.current()})),u+=a.move("]: "),l(),!e.url||/[\0- \u007F]/.test(e.url)?(l=n.enter("destinationLiteral"),u+=a.move("<"),u+=a.move(n.safe(e.url,{before:u,after:">",...a.current()})),u+=a.move(">")):(l=n.enter("destinationRaw"),u+=a.move(n.safe(e.url,{before:u,after:e.title?" ":`
`,...a.current()}))),l(),e.title&&(l=n.enter(`title${o}`),u+=a.move(" "+i),u+=a.move(n.safe(e.title,{before:u,after:i,...a.current()})),u+=a.move(i),l()),s(),u}function DP(e){const t=e.options.emphasis||"*";if(t!=="*"&&t!=="_")throw new Error("Cannot serialize emphasis with `"+t+"` for `options.emphasis`, expected `*`, or `_`");return t}d0.peek=LP;function d0(e,t,n,r){const i=DP(n),o=n.enter("emphasis"),s=n.createTracker(r);let l=s.move(i);return l+=s.move(n.containerPhrasing(e,{before:l,after:i,...s.current()})),l+=s.move(i),o(),l}function LP(e,t,n){return n.options.emphasis||"*"}function bP(e,t){let n=!1;return Uc(e,function(r){if("value"in r&&/\r?\n|\r/.test(r.value)||r.type==="break")return n=!0,hu}),!!((!e.depth||e.depth<3)&&Oc(e)&&(t.options.setext||n))}function RP(e,t,n,r){const i=Math.max(Math.min(6,e.depth||1),1),o=n.createTracker(r);if(bP(e,n)){const c=n.enter("headingSetext"),f=n.enter("phrasing"),h=n.containerPhrasing(e,{...o.current(),before:`
`,after:`
`});return f(),c(),h+`
`+(i===1?"=":"-").repeat(h.length-(Math.max(h.lastIndexOf("\r"),h.lastIndexOf(`
`))+1))}const s="#".repeat(i),l=n.enter("headingAtx"),a=n.enter("phrasing");o.move(s+" ");let u=n.containerPhrasing(e,{before:"# ",after:`
`,...o.current()});return/^[\t ]/.test(u)&&(u="&#x"+u.charCodeAt(0).toString(16).toUpperCase()+";"+u.slice(1)),u=u?s+" "+u:s,n.options.closeAtx&&(u+=" "+s),a(),l(),u}p0.peek=MP;function p0(e){return e.value||""}function MP(){return"<"}m0.peek=IP;function m0(e,t,n,r){const i=Hc(n),o=i==='"'?"Quote":"Apostrophe",s=n.enter("image");let l=n.enter("label");const a=n.createTracker(r);let u=a.move("![");return u+=a.move(n.safe(e.alt,{before:u,after:"]",...a.current()})),u+=a.move("]("),l(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(l=n.enter("destinationLiteral"),u+=a.move("<"),u+=a.move(n.safe(e.url,{before:u,after:">",...a.current()})),u+=a.move(">")):(l=n.enter("destinationRaw"),u+=a.move(n.safe(e.url,{before:u,after:e.title?" ":")",...a.current()}))),l(),e.title&&(l=n.enter(`title${o}`),u+=a.move(" "+i),u+=a.move(n.safe(e.title,{before:u,after:i,...a.current()})),u+=a.move(i),l()),u+=a.move(")"),s(),u}function IP(){return"!"}g0.peek=_P;function g0(e,t,n,r){const i=e.referenceType,o=n.enter("imageReference");let s=n.enter("label");const l=n.createTracker(r);let a=l.move("![");const u=n.safe(e.alt,{before:a,after:"]",...l.current()});a+=l.move(u+"]["),s();const c=n.stack;n.stack=[],s=n.enter("reference");const f=n.safe(n.associationId(e),{before:a,after:"]",...l.current()});return s(),n.stack=c,o(),i==="full"||!u||u!==f?a+=l.move(f+"]"):i==="shortcut"?a=a.slice(0,-1):a+=l.move("]"),a}function _P(){return"!"}y0.peek=OP;function y0(e,t,n){let r=e.value||"",i="`",o=-1;for(;new RegExp("(^|[^`])"+i+"([^`]|$)").test(r);)i+="`";for(/[^ \r\n]/.test(r)&&(/^[ \r\n]/.test(r)&&/[ \r\n]$/.test(r)||/^`|`$/.test(r))&&(r=" "+r+" ");++o<n.unsafe.length;){const s=n.unsafe[o],l=n.compilePattern(s);let a;if(s.atBreak)for(;a=l.exec(r);){let u=a.index;r.charCodeAt(u)===10&&r.charCodeAt(u-1)===13&&u--,r=r.slice(0,u)+" "+r.slice(a.index+1)}}return i+r+i}function OP(){return"`"}function v0(e,t){const n=Oc(e);return!!(!t.options.resourceLink&&e.url&&!e.title&&e.children&&e.children.length===1&&e.children[0].type==="text"&&(n===e.url||"mailto:"+n===e.url)&&/^[a-z][a-z+.-]+:/i.test(e.url)&&!/[\0- <>\u007F]/.test(e.url))}x0.peek=zP;function x0(e,t,n,r){const i=Hc(n),o=i==='"'?"Quote":"Apostrophe",s=n.createTracker(r);let l,a;if(v0(e,n)){const c=n.stack;n.stack=[],l=n.enter("autolink");let f=s.move("<");return f+=s.move(n.containerPhrasing(e,{before:f,after:">",...s.current()})),f+=s.move(">"),l(),n.stack=c,f}l=n.enter("link"),a=n.enter("label");let u=s.move("[");return u+=s.move(n.containerPhrasing(e,{before:u,after:"](",...s.current()})),u+=s.move("]("),a(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(a=n.enter("destinationLiteral"),u+=s.move("<"),u+=s.move(n.safe(e.url,{before:u,after:">",...s.current()})),u+=s.move(">")):(a=n.enter("destinationRaw"),u+=s.move(n.safe(e.url,{before:u,after:e.title?" ":")",...s.current()}))),a(),e.title&&(a=n.enter(`title${o}`),u+=s.move(" "+i),u+=s.move(n.safe(e.title,{before:u,after:i,...s.current()})),u+=s.move(i),a()),u+=s.move(")"),l(),u}function zP(e,t,n){return v0(e,n)?"<":"["}w0.peek=FP;function w0(e,t,n,r){const i=e.referenceType,o=n.enter("linkReference");let s=n.enter("label");const l=n.createTracker(r);let a=l.move("[");const u=n.containerPhrasing(e,{before:a,after:"]",...l.current()});a+=l.move(u+"]["),s();const c=n.stack;n.stack=[],s=n.enter("reference");const f=n.safe(n.associationId(e),{before:a,after:"]",...l.current()});return s(),n.stack=c,o(),i==="full"||!u||u!==f?a+=l.move(f+"]"):i==="shortcut"?a=a.slice(0,-1):a+=l.move("]"),a}function FP(){return"["}function Wc(e){const t=e.options.bullet||"*";if(t!=="*"&&t!=="+"&&t!=="-")throw new Error("Cannot serialize items with `"+t+"` for `options.bullet`, expected `*`, `+`, or `-`");return t}function NP(e){const t=Wc(e),n=e.options.bulletOther;if(!n)return t==="*"?"-":"*";if(n!=="*"&&n!=="+"&&n!=="-")throw new Error("Cannot serialize items with `"+n+"` for `options.bulletOther`, expected `*`, `+`, or `-`");if(n===t)throw new Error("Expected `bullet` (`"+t+"`) and `bulletOther` (`"+n+"`) to be different");return n}function jP(e){const t=e.options.bulletOrdered||".";if(t!=="."&&t!==")")throw new Error("Cannot serialize items with `"+t+"` for `options.bulletOrdered`, expected `.` or `)`");return t}function k0(e){const t=e.options.rule||"*";if(t!=="*"&&t!=="-"&&t!=="_")throw new Error("Cannot serialize rules with `"+t+"` for `options.rule`, expected `*`, `-`, or `_`");return t}function VP(e,t,n,r){const i=n.enter("list"),o=n.bulletCurrent;let s=e.ordered?jP(n):Wc(n);const l=e.ordered?s==="."?")":".":NP(n);let a=t&&n.bulletLastUsed?s===n.bulletLastUsed:!1;if(!e.ordered){const c=e.children?e.children[0]:void 0;if((s==="*"||s==="-")&&c&&(!c.children||!c.children[0])&&n.stack[n.stack.length-1]==="list"&&n.stack[n.stack.length-2]==="listItem"&&n.stack[n.stack.length-3]==="list"&&n.stack[n.stack.length-4]==="listItem"&&n.indexStack[n.indexStack.length-1]===0&&n.indexStack[n.indexStack.length-2]===0&&n.indexStack[n.indexStack.length-3]===0&&(a=!0),k0(n)===s&&c){let f=-1;for(;++f<e.children.length;){const h=e.children[f];if(h&&h.type==="listItem"&&h.children&&h.children[0]&&h.children[0].type==="thematicBreak"){a=!0;break}}}}a&&(s=l),n.bulletCurrent=s;const u=n.containerFlow(e,r);return n.bulletLastUsed=s,n.bulletCurrent=o,i(),u}function BP(e){const t=e.options.listItemIndent||"one";if(t!=="tab"&&t!=="one"&&t!=="mixed")throw new Error("Cannot serialize items with `"+t+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return t}function UP(e,t,n,r){const i=BP(n);let o=n.bulletCurrent||Wc(n);t&&t.type==="list"&&t.ordered&&(o=(typeof t.start=="number"&&t.start>-1?t.start:1)+(n.options.incrementListMarker===!1?0:t.children.indexOf(e))+o);let s=o.length+1;(i==="tab"||i==="mixed"&&(t&&t.type==="list"&&t.spread||e.spread))&&(s=Math.ceil(s/4)*4);const l=n.createTracker(r);l.move(o+" ".repeat(s-o.length)),l.shift(s);const a=n.enter("listItem"),u=n.indentLines(n.containerFlow(e,l.current()),c);return a(),u;function c(f,h,d){return h?(d?"":" ".repeat(s))+f:(d?o:o+" ".repeat(s-o.length))+f}}function $P(e,t,n,r){const i=n.enter("paragraph"),o=n.enter("phrasing"),s=n.containerPhrasing(e,r);return o(),i(),s}const HP=rl(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"]);function WP(e,t,n,r){return(e.children.some(function(s){return HP(s)})?n.containerPhrasing:n.containerFlow).call(n,e,r)}function KP(e){const t=e.options.strong||"*";if(t!=="*"&&t!=="_")throw new Error("Cannot serialize strong with `"+t+"` for `options.strong`, expected `*`, or `_`");return t}S0.peek=QP;function S0(e,t,n,r){const i=KP(n),o=n.enter("strong"),s=n.createTracker(r);let l=s.move(i+i);return l+=s.move(n.containerPhrasing(e,{before:l,after:i,...s.current()})),l+=s.move(i+i),o(),l}function QP(e,t,n){return n.options.strong||"*"}function GP(e,t,n,r){return n.safe(e.value,r)}function YP(e){const t=e.options.ruleRepetition||3;if(t<3)throw new Error("Cannot serialize rules with repetition `"+t+"` for `options.ruleRepetition`, expected `3` or more");return t}function qP(e,t,n){const r=(k0(n)+(n.options.ruleSpaces?" ":"")).repeat(YP(n));return n.options.ruleSpaces?r.slice(0,-1):r}const C0={blockquote:xP,break:Ud,code:TP,definition:AP,emphasis:d0,hardBreak:Ud,heading:RP,html:p0,image:m0,imageReference:g0,inlineCode:y0,link:x0,linkReference:w0,list:VP,listItem:UP,paragraph:$P,root:WP,strong:S0,text:GP,thematicBreak:qP};function XP(){return{enter:{table:ZP,tableData:$d,tableHeader:$d,tableRow:eA},exit:{codeText:tA,table:JP,tableData:Xl,tableHeader:Xl,tableRow:Xl}}}function ZP(e){const t=e._align;this.enter({type:"table",align:t.map(function(n){return n==="none"?null:n}),children:[]},e),this.data.inTable=!0}function JP(e){this.exit(e),this.data.inTable=void 0}function eA(e){this.enter({type:"tableRow",children:[]},e)}function Xl(e){this.exit(e)}function $d(e){this.enter({type:"tableCell",children:[]},e)}function tA(e){let t=this.resume();this.data.inTable&&(t=t.replace(/\\([\\|])/g,nA));const n=this.stack[this.stack.length-1];n.type,n.value=t,this.exit(e)}function nA(e,t){return t==="|"?t:e}function rA(e){const t=e||{},n=t.tableCellPadding,r=t.tablePipeAlign,i=t.stringLength,o=n?" ":"|";return{unsafe:[{character:"\r",inConstruct:"tableCell"},{character:`
`,inConstruct:"tableCell"},{atBreak:!0,character:"|",after:"[	 :-]"},{character:"|",inConstruct:"tableCell"},{atBreak:!0,character:":",after:"-"},{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{inlineCode:h,table:s,tableCell:a,tableRow:l}};function s(d,g,v,w){return u(c(d,v,w),d.align)}function l(d,g,v,w){const p=f(d,v,w),m=u([p]);return m.slice(0,m.indexOf(`
`))}function a(d,g,v,w){const p=v.enter("tableCell"),m=v.enter("phrasing"),y=v.containerPhrasing(d,{...w,before:o,after:o});return m(),p(),y}function u(d,g){return gP(d,{align:g,alignDelimiters:r,padding:n,stringLength:i})}function c(d,g,v){const w=d.children;let p=-1;const m=[],y=g.enter("table");for(;++p<w.length;)m[p]=f(w[p],g,v);return y(),m}function f(d,g,v){const w=d.children;let p=-1;const m=[],y=g.enter("tableRow");for(;++p<w.length;)m[p]=a(w[p],d,g,v);return y(),m}function h(d,g,v){let w=C0.inlineCode(d,g,v);return v.stack.includes("tableCell")&&(w=w.replace(/\|/g,"\\$&")),w}}function iA(){return{exit:{taskListCheckValueChecked:Hd,taskListCheckValueUnchecked:Hd,paragraph:sA}}}function oA(){return{unsafe:[{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{listItem:lA}}}function Hd(e){const t=this.stack[this.stack.length-2];t.type,t.checked=e.type==="taskListCheckValueChecked"}function sA(e){const t=this.stack[this.stack.length-2];if(t&&t.type==="listItem"&&typeof t.checked=="boolean"){const n=this.stack[this.stack.length-1];n.type;const r=n.children[0];if(r&&r.type==="text"){const i=t.children;let o=-1,s;for(;++o<i.length;){const l=i[o];if(l.type==="paragraph"){s=l;break}}s===n&&(r.value=r.value.slice(1),r.value.length===0?n.children.shift():n.position&&r.position&&typeof r.position.start.offset=="number"&&(r.position.start.column++,r.position.start.offset++,n.position.start=Object.assign({},r.position.start)))}}this.exit(e)}function lA(e,t,n,r){const i=e.children[0],o=typeof e.checked=="boolean"&&i&&i.type==="paragraph",s="["+(e.checked?"x":" ")+"] ",l=n.createTracker(r);o&&l.move(s);let a=C0.listItem(e,t,n,{...r,...l.current()});return o&&(a=a.replace(/^(?:[*+-]|\d+\.)([\r\n]| {1,3})/,u)),a;function u(c){return c+s}}function aA(){return[jT(),XT(),fP(),XP(),iA()]}function uA(e){return{extensions:[VT(),ZT(),hP(),rA(e),oA()]}}const cA={tokenize:gA,partial:!0},E0={tokenize:yA,partial:!0},T0={tokenize:vA,partial:!0},P0={tokenize:xA,partial:!0},fA={tokenize:wA,partial:!0},A0={name:"wwwAutolink",tokenize:pA,previous:L0},D0={name:"protocolAutolink",tokenize:mA,previous:b0},en={name:"emailAutolink",tokenize:dA,previous:R0},Nt={};function hA(){return{text:Nt}}let Rn=48;for(;Rn<123;)Nt[Rn]=en,Rn++,Rn===58?Rn=65:Rn===91&&(Rn=97);Nt[43]=en;Nt[45]=en;Nt[46]=en;Nt[95]=en;Nt[72]=[en,D0];Nt[104]=[en,D0];Nt[87]=[en,A0];Nt[119]=[en,A0];function dA(e,t,n){const r=this;let i,o;return s;function s(f){return!gu(f)||!R0.call(r,r.previous)||Kc(r.events)?n(f):(e.enter("literalAutolink"),e.enter("literalAutolinkEmail"),l(f))}function l(f){return gu(f)?(e.consume(f),l):f===64?(e.consume(f),a):n(f)}function a(f){return f===46?e.check(fA,c,u)(f):f===45||f===95||Oe(f)?(o=!0,e.consume(f),a):c(f)}function u(f){return e.consume(f),i=!0,a}function c(f){return o&&i&&Be(r.previous)?(e.exit("literalAutolinkEmail"),e.exit("literalAutolink"),t(f)):n(f)}}function pA(e,t,n){const r=this;return i;function i(s){return s!==87&&s!==119||!L0.call(r,r.previous)||Kc(r.events)?n(s):(e.enter("literalAutolink"),e.enter("literalAutolinkWww"),e.check(cA,e.attempt(E0,e.attempt(T0,o),n),n)(s))}function o(s){return e.exit("literalAutolinkWww"),e.exit("literalAutolink"),t(s)}}function mA(e,t,n){const r=this;let i="",o=!1;return s;function s(f){return(f===72||f===104)&&b0.call(r,r.previous)&&!Kc(r.events)?(e.enter("literalAutolink"),e.enter("literalAutolinkHttp"),i+=String.fromCodePoint(f),e.consume(f),l):n(f)}function l(f){if(Be(f)&&i.length<5)return i+=String.fromCodePoint(f),e.consume(f),l;if(f===58){const h=i.toLowerCase();if(h==="http"||h==="https")return e.consume(f),a}return n(f)}function a(f){return f===47?(e.consume(f),o?u:(o=!0,a)):n(f)}function u(f){return f===null||Ps(f)||re(f)||qn(f)||el(f)?n(f):e.attempt(E0,e.attempt(T0,c),n)(f)}function c(f){return e.exit("literalAutolinkHttp"),e.exit("literalAutolink"),t(f)}}function gA(e,t,n){let r=0;return i;function i(s){return(s===87||s===119)&&r<3?(r++,e.consume(s),i):s===46&&r===3?(e.consume(s),o):n(s)}function o(s){return s===null?n(s):t(s)}}function yA(e,t,n){let r,i,o;return s;function s(u){return u===46||u===95?e.check(P0,a,l)(u):u===null||re(u)||qn(u)||u!==45&&el(u)?a(u):(o=!0,e.consume(u),s)}function l(u){return u===95?r=!0:(i=r,r=void 0),e.consume(u),s}function a(u){return i||r||!o?n(u):t(u)}}function vA(e,t){let n=0,r=0;return i;function i(s){return s===40?(n++,e.consume(s),i):s===41&&r<n?o(s):s===33||s===34||s===38||s===39||s===41||s===42||s===44||s===46||s===58||s===59||s===60||s===63||s===93||s===95||s===126?e.check(P0,t,o)(s):s===null||re(s)||qn(s)?t(s):(e.consume(s),i)}function o(s){return s===41&&r++,e.consume(s),i}}function xA(e,t,n){return r;function r(l){return l===33||l===34||l===39||l===41||l===42||l===44||l===46||l===58||l===59||l===63||l===95||l===126?(e.consume(l),r):l===38?(e.consume(l),o):l===93?(e.consume(l),i):l===60||l===null||re(l)||qn(l)?t(l):n(l)}function i(l){return l===null||l===40||l===91||re(l)||qn(l)?t(l):r(l)}function o(l){return Be(l)?s(l):n(l)}function s(l){return l===59?(e.consume(l),r):Be(l)?(e.consume(l),s):n(l)}}function wA(e,t,n){return r;function r(o){return e.consume(o),i}function i(o){return Oe(o)?n(o):t(o)}}function L0(e){return e===null||e===40||e===42||e===95||e===91||e===93||e===126||re(e)}function b0(e){return!Be(e)}function R0(e){return!(e===47||gu(e))}function gu(e){return e===43||e===45||e===46||e===95||Oe(e)}function Kc(e){let t=e.length,n=!1;for(;t--;){const r=e[t][1];if((r.type==="labelLink"||r.type==="labelImage")&&!r._balanced){n=!0;break}if(r._gfmAutolinkLiteralWalkedInto){n=!1;break}}return e.length>0&&!n&&(e[e.length-1][1]._gfmAutolinkLiteralWalkedInto=!0),n}const kA={tokenize:LA,partial:!0};function SA(){return{document:{91:{name:"gfmFootnoteDefinition",tokenize:PA,continuation:{tokenize:AA},exit:DA}},text:{91:{name:"gfmFootnoteCall",tokenize:TA},93:{name:"gfmPotentialFootnoteCall",add:"after",tokenize:CA,resolveTo:EA}}}}function CA(e,t,n){const r=this;let i=r.events.length;const o=r.parser.gfmFootnotes||(r.parser.gfmFootnotes=[]);let s;for(;i--;){const a=r.events[i][1];if(a.type==="labelImage"){s=a;break}if(a.type==="gfmFootnoteCall"||a.type==="labelLink"||a.type==="label"||a.type==="image"||a.type==="link")break}return l;function l(a){if(!s||!s._balanced)return n(a);const u=At(r.sliceSerialize({start:s.end,end:r.now()}));return u.codePointAt(0)!==94||!o.includes(u.slice(1))?n(a):(e.enter("gfmFootnoteCallLabelMarker"),e.consume(a),e.exit("gfmFootnoteCallLabelMarker"),t(a))}}function EA(e,t){let n=e.length;for(;n--;)if(e[n][1].type==="labelImage"&&e[n][0]==="enter"){e[n][1];break}e[n+1][1].type="data",e[n+3][1].type="gfmFootnoteCallLabelMarker";const r={type:"gfmFootnoteCall",start:Object.assign({},e[n+3][1].start),end:Object.assign({},e[e.length-1][1].end)},i={type:"gfmFootnoteCallMarker",start:Object.assign({},e[n+3][1].end),end:Object.assign({},e[n+3][1].end)};i.end.column++,i.end.offset++,i.end._bufferIndex++;const o={type:"gfmFootnoteCallString",start:Object.assign({},i.end),end:Object.assign({},e[e.length-1][1].start)},s={type:"chunkString",contentType:"string",start:Object.assign({},o.start),end:Object.assign({},o.end)},l=[e[n+1],e[n+2],["enter",r,t],e[n+3],e[n+4],["enter",i,t],["exit",i,t],["enter",o,t],["enter",s,t],["exit",s,t],["exit",o,t],e[e.length-2],e[e.length-1],["exit",r,t]];return e.splice(n,e.length-n+1,...l),e}function TA(e,t,n){const r=this,i=r.parser.gfmFootnotes||(r.parser.gfmFootnotes=[]);let o=0,s;return l;function l(f){return e.enter("gfmFootnoteCall"),e.enter("gfmFootnoteCallLabelMarker"),e.consume(f),e.exit("gfmFootnoteCallLabelMarker"),a}function a(f){return f!==94?n(f):(e.enter("gfmFootnoteCallMarker"),e.consume(f),e.exit("gfmFootnoteCallMarker"),e.enter("gfmFootnoteCallString"),e.enter("chunkString").contentType="string",u)}function u(f){if(o>999||f===93&&!s||f===null||f===91||re(f))return n(f);if(f===93){e.exit("chunkString");const h=e.exit("gfmFootnoteCallString");return i.includes(At(r.sliceSerialize(h)))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(f),e.exit("gfmFootnoteCallLabelMarker"),e.exit("gfmFootnoteCall"),t):n(f)}return re(f)||(s=!0),o++,e.consume(f),f===92?c:u}function c(f){return f===91||f===92||f===93?(e.consume(f),o++,u):u(f)}}function PA(e,t,n){const r=this,i=r.parser.gfmFootnotes||(r.parser.gfmFootnotes=[]);let o,s=0,l;return a;function a(g){return e.enter("gfmFootnoteDefinition")._container=!0,e.enter("gfmFootnoteDefinitionLabel"),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(g),e.exit("gfmFootnoteDefinitionLabelMarker"),u}function u(g){return g===94?(e.enter("gfmFootnoteDefinitionMarker"),e.consume(g),e.exit("gfmFootnoteDefinitionMarker"),e.enter("gfmFootnoteDefinitionLabelString"),e.enter("chunkString").contentType="string",c):n(g)}function c(g){if(s>999||g===93&&!l||g===null||g===91||re(g))return n(g);if(g===93){e.exit("chunkString");const v=e.exit("gfmFootnoteDefinitionLabelString");return o=At(r.sliceSerialize(v)),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(g),e.exit("gfmFootnoteDefinitionLabelMarker"),e.exit("gfmFootnoteDefinitionLabel"),h}return re(g)||(l=!0),s++,e.consume(g),g===92?f:c}function f(g){return g===91||g===92||g===93?(e.consume(g),s++,c):c(g)}function h(g){return g===58?(e.enter("definitionMarker"),e.consume(g),e.exit("definitionMarker"),i.includes(o)||i.push(o),X(e,d,"gfmFootnoteDefinitionWhitespace")):n(g)}function d(g){return t(g)}}function AA(e,t,n){return e.check(io,t,e.attempt(kA,t,n))}function DA(e){e.exit("gfmFootnoteDefinition")}function LA(e,t,n){const r=this;return X(e,i,"gfmFootnoteDefinitionIndent",5);function i(o){const s=r.events[r.events.length-1];return s&&s[1].type==="gfmFootnoteDefinitionIndent"&&s[2].sliceSerialize(s[1],!0).length===4?t(o):n(o)}}function bA(e){let n=(e||{}).singleTilde;const r={name:"strikethrough",tokenize:o,resolveAll:i};return n==null&&(n=!0),{text:{126:r},insideSpan:{null:[r]},attentionMarkers:{null:[126]}};function i(s,l){let a=-1;for(;++a<s.length;)if(s[a][0]==="enter"&&s[a][1].type==="strikethroughSequenceTemporary"&&s[a][1]._close){let u=a;for(;u--;)if(s[u][0]==="exit"&&s[u][1].type==="strikethroughSequenceTemporary"&&s[u][1]._open&&s[a][1].end.offset-s[a][1].start.offset===s[u][1].end.offset-s[u][1].start.offset){s[a][1].type="strikethroughSequence",s[u][1].type="strikethroughSequence";const c={type:"strikethrough",start:Object.assign({},s[u][1].start),end:Object.assign({},s[a][1].end)},f={type:"strikethroughText",start:Object.assign({},s[u][1].end),end:Object.assign({},s[a][1].start)},h=[["enter",c,l],["enter",s[u][1],l],["exit",s[u][1],l],["enter",f,l]],d=l.parser.constructs.insideSpan.null;d&&pt(h,h.length,0,tl(d,s.slice(u+1,a),l)),pt(h,h.length,0,[["exit",f,l],["enter",s[a][1],l],["exit",s[a][1],l],["exit",c,l]]),pt(s,u-1,a-u+3,h),a=u+h.length-2;break}}for(a=-1;++a<s.length;)s[a][1].type==="strikethroughSequenceTemporary"&&(s[a][1].type="data");return s}function o(s,l,a){const u=this.previous,c=this.events;let f=0;return h;function h(g){return u===126&&c[c.length-1][1].type!=="characterEscape"?a(g):(s.enter("strikethroughSequenceTemporary"),d(g))}function d(g){const v=As(u);if(g===126)return f>1?a(g):(s.consume(g),f++,d);if(f<2&&!n)return a(g);const w=s.exit("strikethroughSequenceTemporary"),p=As(g);return w._open=!p||p===2&&!!v,w._close=!v||v===2&&!!p,l(g)}}}class RA{constructor(){this.map=[]}add(t,n,r){MA(this,t,n,r)}consume(t){if(this.map.sort(function(o,s){return o[0]-s[0]}),this.map.length===0)return;let n=this.map.length;const r=[];for(;n>0;)n-=1,r.push(t.slice(this.map[n][0]+this.map[n][1]),this.map[n][2]),t.length=this.map[n][0];r.push([...t]),t.length=0;let i=r.pop();for(;i;)t.push(...i),i=r.pop();this.map.length=0}}function MA(e,t,n,r){let i=0;if(!(n===0&&r.length===0)){for(;i<e.map.length;){if(e.map[i][0]===t){e.map[i][1]+=n,e.map[i][2].push(...r);return}i+=1}e.map.push([t,n,r])}}function IA(e,t){let n=!1;const r=[];for(;t<e.length;){const i=e[t];if(n){if(i[0]==="enter")i[1].type==="tableContent"&&r.push(e[t+1][1].type==="tableDelimiterMarker"?"left":"none");else if(i[1].type==="tableContent"){if(e[t-1][1].type==="tableDelimiterMarker"){const o=r.length-1;r[o]=r[o]==="left"?"center":"right"}}else if(i[1].type==="tableDelimiterRow")break}else i[0]==="enter"&&i[1].type==="tableDelimiterRow"&&(n=!0);t+=1}return r}function _A(){return{flow:{null:{name:"table",tokenize:OA,resolveAll:zA}}}}function OA(e,t,n){const r=this;let i=0,o=0,s;return l;function l(E){let _=r.events.length-1;for(;_>-1;){const J=r.events[_][1].type;if(J==="lineEnding"||J==="linePrefix")_--;else break}const N=_>-1?r.events[_][1].type:null,Y=N==="tableHead"||N==="tableRow"?k:a;return Y===k&&r.parser.lazy[r.now().line]?n(E):Y(E)}function a(E){return e.enter("tableHead"),e.enter("tableRow"),u(E)}function u(E){return E===124||(s=!0,o+=1),c(E)}function c(E){return E===null?n(E):B(E)?o>1?(o=0,r.interrupt=!0,e.exit("tableRow"),e.enter("lineEnding"),e.consume(E),e.exit("lineEnding"),d):n(E):Q(E)?X(e,c,"whitespace")(E):(o+=1,s&&(s=!1,i+=1),E===124?(e.enter("tableCellDivider"),e.consume(E),e.exit("tableCellDivider"),s=!0,c):(e.enter("data"),f(E)))}function f(E){return E===null||E===124||re(E)?(e.exit("data"),c(E)):(e.consume(E),E===92?h:f)}function h(E){return E===92||E===124?(e.consume(E),f):f(E)}function d(E){return r.interrupt=!1,r.parser.lazy[r.now().line]?n(E):(e.enter("tableDelimiterRow"),s=!1,Q(E)?X(e,g,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(E):g(E))}function g(E){return E===45||E===58?w(E):E===124?(s=!0,e.enter("tableCellDivider"),e.consume(E),e.exit("tableCellDivider"),v):T(E)}function v(E){return Q(E)?X(e,w,"whitespace")(E):w(E)}function w(E){return E===58?(o+=1,s=!0,e.enter("tableDelimiterMarker"),e.consume(E),e.exit("tableDelimiterMarker"),p):E===45?(o+=1,p(E)):E===null||B(E)?C(E):T(E)}function p(E){return E===45?(e.enter("tableDelimiterFiller"),m(E)):T(E)}function m(E){return E===45?(e.consume(E),m):E===58?(s=!0,e.exit("tableDelimiterFiller"),e.enter("tableDelimiterMarker"),e.consume(E),e.exit("tableDelimiterMarker"),y):(e.exit("tableDelimiterFiller"),y(E))}function y(E){return Q(E)?X(e,C,"whitespace")(E):C(E)}function C(E){return E===124?g(E):E===null||B(E)?!s||i!==o?T(E):(e.exit("tableDelimiterRow"),e.exit("tableHead"),t(E)):T(E)}function T(E){return n(E)}function k(E){return e.enter("tableRow"),P(E)}function P(E){return E===124?(e.enter("tableCellDivider"),e.consume(E),e.exit("tableCellDivider"),P):E===null||B(E)?(e.exit("tableRow"),t(E)):Q(E)?X(e,P,"whitespace")(E):(e.enter("data"),A(E))}function A(E){return E===null||E===124||re(E)?(e.exit("data"),P(E)):(e.consume(E),E===92?O:A)}function O(E){return E===92||E===124?(e.consume(E),A):A(E)}}function zA(e,t){let n=-1,r=!0,i=0,o=[0,0,0,0],s=[0,0,0,0],l=!1,a=0,u,c,f;const h=new RA;for(;++n<e.length;){const d=e[n],g=d[1];d[0]==="enter"?g.type==="tableHead"?(l=!1,a!==0&&(Wd(h,t,a,u,c),c=void 0,a=0),u={type:"table",start:Object.assign({},g.start),end:Object.assign({},g.end)},h.add(n,0,[["enter",u,t]])):g.type==="tableRow"||g.type==="tableDelimiterRow"?(r=!0,f=void 0,o=[0,0,0,0],s=[0,n+1,0,0],l&&(l=!1,c={type:"tableBody",start:Object.assign({},g.start),end:Object.assign({},g.end)},h.add(n,0,[["enter",c,t]])),i=g.type==="tableDelimiterRow"?2:c?3:1):i&&(g.type==="data"||g.type==="tableDelimiterMarker"||g.type==="tableDelimiterFiller")?(r=!1,s[2]===0&&(o[1]!==0&&(s[0]=s[1],f=Mo(h,t,o,i,void 0,f),o=[0,0,0,0]),s[2]=n)):g.type==="tableCellDivider"&&(r?r=!1:(o[1]!==0&&(s[0]=s[1],f=Mo(h,t,o,i,void 0,f)),o=s,s=[o[1],n,0,0])):g.type==="tableHead"?(l=!0,a=n):g.type==="tableRow"||g.type==="tableDelimiterRow"?(a=n,o[1]!==0?(s[0]=s[1],f=Mo(h,t,o,i,n,f)):s[1]!==0&&(f=Mo(h,t,s,i,n,f)),i=0):i&&(g.type==="data"||g.type==="tableDelimiterMarker"||g.type==="tableDelimiterFiller")&&(s[3]=n)}for(a!==0&&Wd(h,t,a,u,c),h.consume(t.events),n=-1;++n<t.events.length;){const d=t.events[n];d[0]==="enter"&&d[1].type==="table"&&(d[1]._align=IA(t.events,n))}return e}function Mo(e,t,n,r,i,o){const s=r===1?"tableHeader":r===2?"tableDelimiter":"tableData",l="tableContent";n[0]!==0&&(o.end=Object.assign({},sr(t.events,n[0])),e.add(n[0],0,[["exit",o,t]]));const a=sr(t.events,n[1]);if(o={type:s,start:Object.assign({},a),end:Object.assign({},a)},e.add(n[1],0,[["enter",o,t]]),n[2]!==0){const u=sr(t.events,n[2]),c=sr(t.events,n[3]),f={type:l,start:Object.assign({},u),end:Object.assign({},c)};if(e.add(n[2],0,[["enter",f,t]]),r!==2){const h=t.events[n[2]],d=t.events[n[3]];if(h[1].end=Object.assign({},d[1].end),h[1].type="chunkText",h[1].contentType="text",n[3]>n[2]+1){const g=n[2]+1,v=n[3]-n[2]-1;e.add(g,v,[])}}e.add(n[3]+1,0,[["exit",f,t]])}return i!==void 0&&(o.end=Object.assign({},sr(t.events,i)),e.add(i,0,[["exit",o,t]]),o=void 0),o}function Wd(e,t,n,r,i){const o=[],s=sr(t.events,n);i&&(i.end=Object.assign({},s),o.push(["exit",i,t])),r.end=Object.assign({},s),o.push(["exit",r,t]),e.add(n+1,0,o)}function sr(e,t){const n=e[t],r=n[0]==="enter"?"start":"end";return n[1][r]}const FA={name:"tasklistCheck",tokenize:jA};function NA(){return{text:{91:FA}}}function jA(e,t,n){const r=this;return i;function i(a){return r.previous!==null||!r._gfmTasklistFirstContentOfListItem?n(a):(e.enter("taskListCheck"),e.enter("taskListCheckMarker"),e.consume(a),e.exit("taskListCheckMarker"),o)}function o(a){return re(a)?(e.enter("taskListCheckValueUnchecked"),e.consume(a),e.exit("taskListCheckValueUnchecked"),s):a===88||a===120?(e.enter("taskListCheckValueChecked"),e.consume(a),e.exit("taskListCheckValueChecked"),s):n(a)}function s(a){return a===93?(e.enter("taskListCheckMarker"),e.consume(a),e.exit("taskListCheckMarker"),e.exit("taskListCheck"),l):n(a)}function l(a){return B(a)?t(a):Q(a)?e.check({tokenize:VA},t,n)(a):n(a)}}function VA(e,t,n){return X(e,r,"whitespace");function r(i){return i===null?n(i):t(i)}}function BA(e){return Uy([hA(),SA(),bA(e),_A(),NA()])}const UA={};function Kd(e){const t=this,n=e||UA,r=t.data(),i=r.micromarkExtensions||(r.micromarkExtensions=[]),o=r.fromMarkdownExtensions||(r.fromMarkdownExtensions=[]),s=r.toMarkdownExtensions||(r.toMarkdownExtensions=[]);i.push(BA(n)),o.push(aA()),s.push(uA(n))}const $A=({messages:e,partialMessage:t,isStreaming:n,waitingResponse:r})=>{const i=b.useRef(null),o=b.useRef(null),s="/Customizing/global/plugins/Services/Repository/RepositoryObject/AIChat/templates/images/aichat.png";return b.useEffect(()=>{o.current&&o.current.scrollIntoView({behavior:"smooth"})},[e,t]),V.jsx("div",{children:V.jsxs("div",{className:"messages",ref:i,children:[e.map((l,a)=>V.jsxs("div",{className:`message ${l.role==="user"?"user":"assistant"}`,children:[l.role==="assistant"&&V.jsx("div",{className:"avatar shrink-0",children:V.jsx("img",{src:s,alt:"Assistant"})}),V.jsx("div",{className:"message-content",children:V.jsx(Nd,{remarkPlugins:[Kd],children:l.content})})]},a)),n&&V.jsxs("div",{className:"message assistant",children:[V.jsx("div",{className:"avatar shrink-0",children:V.jsx("img",{src:s,alt:"Assistant"})}),V.jsx("div",{className:"message-content",children:V.jsx(Nd,{remarkPlugins:[Kd],children:t})})]}),r&&V.jsxs("div",{className:"message assistant",children:[V.jsx("div",{className:"avatar shrink-0",children:V.jsx("img",{src:s,alt:"Assistant"})}),V.jsx("div",{className:"flex items-center",children:V.jsxs("span",{className:"relative flex h-5 w-5",children:[V.jsx("span",{className:"animate-ping absolute inline-flex h-full w-full rounded-full bg-[#4c6586] opacity-75"}),V.jsx("span",{className:"relative inline-flex rounded-full h-5 w-5 bg-[#4c6586]"})]})})]}),V.jsx("div",{ref:o})]})})};function HA(e){return Dc({tag:"svg",attr:{viewBox:"0 0 256 256",fill:"currentColor"},child:[{tag:"path",attr:{d:"M136,32V64a8,8,0,0,1-16,0V32a8,8,0,0,1,16,0Zm88,88H192a8,8,0,0,0,0,16h32a8,8,0,0,0,0-16Zm-45.09,47.6a8,8,0,0,0-11.31,11.31l22.62,22.63a8,8,0,0,0,11.32-11.32ZM128,184a8,8,0,0,0-8,8v32a8,8,0,0,0,16,0V192A8,8,0,0,0,128,184ZM77.09,167.6,54.46,190.22a8,8,0,0,0,11.32,11.32L88.4,178.91A8,8,0,0,0,77.09,167.6ZM72,128a8,8,0,0,0-8-8H32a8,8,0,0,0,0,16H64A8,8,0,0,0,72,128ZM65.78,54.46A8,8,0,0,0,54.46,65.78L77.09,88.4A8,8,0,0,0,88.4,77.09Z"},child:[]}]})(e)}const WA=()=>V.jsx("div",{className:"loading",children:V.jsx("div",{className:"loading-spinner animate-spin",children:V.jsx(HA,{size:46,className:"text-neutral-400"})})}),KA=(e,t,n,r,i,o,s,l)=>(console.log("sendmessagetoapi"),o.streaming_enabled?(console.log("streaming"),QA(s,l,e,t,n,r,i)):(console.log("CHAT ACTIVO:",l),GA(s,l,e,i))),QA=async(e,t,n,r,i,o,s)=>{try{console.log("llega al fetch"),console.log(e);const l=await fetch(e,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({action:"add_message",chat_ui_id:t.toString(),message:n})});if(console.log("salio del fetch"),!l.body)throw new Error("ReadableStream not yet supported in this browser.");s(!1),o(!0);const a=l.body.getReader(),u=new TextDecoder;let c="",f="";const h=async({done:d,value:g})=>{if(d)return o(!1),{id:0,chat_ui_id:t,role:"assistant",content:c};f+=u.decode(g,{stream:!0});let v=f.indexOf(`
`);for(;v!==-1;){const w=f.slice(0,v);if(f=f.slice(v+1),w.trim().length>0&&w.startsWith("data: ")){const p=w.substring(6).trim();if(p==="[DONE]")return o(!1),{id:0,chat_ui_id:t,role:"assistant",content:c};try{const m=JSON.parse(p);m.choices&&m.choices[0].delta&&m.choices[0].delta.content&&(c+=m.choices[0].delta.content,i.current=c,r(c),await new Promise(y=>setTimeout(y,50)))}catch(m){console.error("Error parsing stream data:",m)}}v=f.indexOf(`
`)}return a.read().then(h)};return a.read().then(h)}catch(l){return console.error("Error sending chat message:",l),{title:"Error",message:l.message,code:404}}},Qd=async(e,t)=>{try{const n=await fetch(`${e}&action=chat&chat_ui_id=${t}`),r=await n.json();if(!n.ok){if(r.error)return{title:"Error",message:r.error,code:n.status};throw new Error("Network response was not ok")}return console.log("MENSAJES:",r.messages),r.messages}catch(n){return console.log("Error fetching messages:",n),{title:"Error",message:n,code:404}}},GA=async(e,t,n,r)=>{try{console.log("MANDO MENSAJE:",{action:"add_message",chat_ui_id:t.toString(),message:n});const i=await fetch(e,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({action:"add_message",chat_ui_id:t.toString(),message:n})});console.log("RESPUESTA:",i);const o=await i.json();if(!i.ok){if(o.error)return{title:"Error",message:o.error,code:i.status};throw new Error("Network response was not ok")}return console.log("RESPUESTA EN API:",o),r(!1),o}catch(i){return console.error("Error sending chat message:",i),{title:"Error",message:i,code:404}}},YA=async e=>{try{const t=await fetch(`${e}`,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({action:"new_chat"})}),n=await t.json();if(!t.ok){if(n.error)return{title:"Error",message:n.error,code:t.status};throw new Error("Network response was not ok")}return console.log("DATA New chat:",n),n}catch(t){return console.error("Error fetching new chat:",t),{title:"Error",message:t.error,code:t.code}}},qA=async(e,t)=>{try{const n=await fetch(`${e}`,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({action:"delete_chat",chat_ui_id:t.toString()})}),r=await n.json();if(!n.ok){if(r.error)return{title:"Error",message:r.error,code:n.status};throw new Error("Network response was not ok")}return console.log("DATA Remove chat:",r),r}catch(n){return console.error("Error fetching remove chat:",n),{title:"Error",message:n,code:404}}},XA={characters_limit:100,n_memory_messages:500,streaming_enabled:!1,disclaimer:"",translations:{front_new_chat_button:"New chat",front_input_placeholder:"Type a message..."}},M0=b.createContext(void 0),ZA=()=>{const e=b.useContext(M0);if(!e)throw new Error("useChatConfig must be used within a ChatConfigProvider");return e},JA=({apiUrl:e,children:t})=>{const[n,r]=b.useState(void 0);return b.useEffect(()=>{(async()=>{try{const o=await fetch(`${e}&action=config`);if(!o.ok)throw new Error("Network response was not ok");const s=await o.json(),l={n_memory_messages:s.n_memory_messages,characters_limit:s.characters_limit,streaming_enabled:s.streaming_enabled,disclaimer:s.disclaimer,translations:{front_new_chat_button:s.translations.front_new_chat_button,front_input_placeholder:s.translations.front_input_placeholder}};console.log("BRUTO CONFIG:",s),r(l)}catch(o){console.error("Error fetching chat config:",o),r(XA)}})()},[e]),n?V.jsx(M0.Provider,{value:n,children:t}):V.jsx("div",{children:"Loading configuration..."})},e2=e=>{const t=ZA(),[n,r]=b.useState([]),[i,o]=b.useState([]),[s,l]=b.useState(""),[a,u]=b.useState(!0),[c,f]=b.useState(!1),[h,d]=b.useState(0),[g,v]=b.useState(!1),[w,p]=b.useState(!1),[m,y]=b.useState(!1),[C,T]=b.useState(!1),[k,P]=b.useState(null),A=b.useRef(""),[O,E]=b.useState("");b.useEffect(()=>{_()},[e]);const _=async()=>{v(!0);const F=await Qd(e,0);if(F.code){T(!0),P(F),v(!1);return}o(F),v(!1)},N=async F=>{d(F),v(!0),p(!1);const L=await Qd(e,F);if(L.code){T(!0),P(L),v(!1);return}o(L),v(!1)};return{chats:n,messages:i,inputValue:s,canWrite:a,canSend:c,activeChat:h,loadingChat:g,partialMessage:O,isStreaming:w,waitingResponse:m,displayError:C,error:k,setInputValue:l,setCanSend:f,setMessages:o,setCanWrite:u,setIsStreaming:p,handleSetActiveChat:N,handleOnChangeInput:F=>{F.length>t.characters_limit||(l(F),f(F.length>0))},handleNewChat:async()=>{v(!0);const F=await YA(e);if(F.code){T(!0),P(F),v(!1);return}const L={id:parseInt(F.id),created_at:F.created_at,title:F.title,user_id:parseInt(F.user_id),obj_id:parseInt(F.obj_id),last_update:F.last_update};r(z=>[L,...z]),N(parseInt(F.id))},handleSendMessage:async F=>{if(a&&c&&F.trim()){l(""),f(!1),u(!1),A.current="",E(""),o(z=>{const x={id:0,chat_id:h,role:"user",content:F};return[...z,x]}),y(!0),console.log("el mensaje a enviar: "+F);const L=await KA(F,E,A,p,y,t,e,h);if(L.code){T(!0),P(L);return}if(i.length===0&&r(z=>z.map(W=>W.id===h?{...W,title:F}:W)),L.llmresponse){const z=L,x=z.message.id,W=z.llmresponse;o(ee=>[...ee.map(fe=>fe.id===x?{...fe,id:W.id}:fe),W])}else{const z=L;o(x=>[...x,z])}u(!0)}},handleRemoveChat:async F=>{await qA(e,F)!=="error"&&(o([]),_(),r(z=>z.filter(x=>x.id!==F)))},config:t}};function t2(e){return Dc({tag:"svg",attr:{viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M5 2c-1.103 0-2 .897-2 2v12c0 1.103.897 2 2 2h3.586L12 21.414 15.414 18H19c1.103 0 2-.897 2-2V4c0-1.103-.897-2-2-2H5zm14 14h-4.414L12 18.586 9.414 16H5V4h14v12z"},child:[]},{tag:"path",attr:{d:"M11 6h2v6h-2zm0 7h2v2h-2z"},child:[]}]})(e)}const Qc=b.createContext({});function Gc(e){const t=b.useRef(null);return t.current===null&&(t.current=e()),t.current}const ol=b.createContext(null),Yc=b.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"});class n2 extends b.Component{getSnapshotBeforeUpdate(t){const n=this.props.childRef.current;if(n&&t.isPresent&&!this.props.isPresent){const r=this.props.sizeRef.current;r.height=n.offsetHeight||0,r.width=n.offsetWidth||0,r.top=n.offsetTop,r.left=n.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function r2({children:e,isPresent:t}){const n=b.useId(),r=b.useRef(null),i=b.useRef({width:0,height:0,top:0,left:0}),{nonce:o}=b.useContext(Yc);return b.useInsertionEffect(()=>{const{width:s,height:l,top:a,left:u}=i.current;if(t||!r.current||!s||!l)return;r.current.dataset.motionPopId=n;const c=document.createElement("style");return o&&(c.nonce=o),document.head.appendChild(c),c.sheet&&c.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${s}px !important;
            height: ${l}px !important;
            top: ${a}px !important;
            left: ${u}px !important;
          }
        `),()=>{document.head.removeChild(c)}},[t]),V.jsx(n2,{isPresent:t,childRef:r,sizeRef:i,children:b.cloneElement(e,{ref:r})})}const i2=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:i,presenceAffectsLayout:o,mode:s})=>{const l=Gc(o2),a=b.useId(),u=b.useCallback(f=>{l.set(f,!0);for(const h of l.values())if(!h)return;r&&r()},[l,r]),c=b.useMemo(()=>({id:a,initial:t,isPresent:n,custom:i,onExitComplete:u,register:f=>(l.set(f,!1),()=>l.delete(f))}),o?[Math.random(),u]:[n,u]);return b.useMemo(()=>{l.forEach((f,h)=>l.set(h,!1))},[n]),b.useEffect(()=>{!n&&!l.size&&r&&r()},[n]),s==="popLayout"&&(e=V.jsx(r2,{isPresent:n,children:e})),V.jsx(ol.Provider,{value:c,children:e})};function o2(){return new Map}function I0(e=!0){const t=b.useContext(ol);if(t===null)return[!0,null];const{isPresent:n,onExitComplete:r,register:i}=t,o=b.useId();b.useEffect(()=>{e&&i(o)},[e]);const s=b.useCallback(()=>e&&r&&r(o),[o,r,e]);return!n&&r?[!1,s]:[!0]}const Io=e=>e.key||"";function Gd(e){const t=[];return b.Children.forEach(e,n=>{b.isValidElement(n)&&t.push(n)}),t}const qc=typeof window<"u",_0=qc?b.useLayoutEffect:b.useEffect,s2=({children:e,custom:t,initial:n=!0,onExitComplete:r,presenceAffectsLayout:i=!0,mode:o="sync",propagate:s=!1})=>{const[l,a]=I0(s),u=b.useMemo(()=>Gd(e),[e]),c=s&&!l?[]:u.map(Io),f=b.useRef(!0),h=b.useRef(u),d=Gc(()=>new Map),[g,v]=b.useState(u),[w,p]=b.useState(u);_0(()=>{f.current=!1,h.current=u;for(let C=0;C<w.length;C++){const T=Io(w[C]);c.includes(T)?d.delete(T):d.get(T)!==!0&&d.set(T,!1)}},[w,c.length,c.join("-")]);const m=[];if(u!==g){let C=[...u];for(let T=0;T<w.length;T++){const k=w[T],P=Io(k);c.includes(P)||(C.splice(T,0,k),m.push(k))}o==="wait"&&m.length&&(C=m),p(Gd(C)),v(u);return}const{forceRender:y}=b.useContext(Qc);return V.jsx(V.Fragment,{children:w.map(C=>{const T=Io(C),k=s&&!l?!1:u===w||c.includes(T),P=()=>{if(d.has(T))d.set(T,!0);else return;let A=!0;d.forEach(O=>{O||(A=!1)}),A&&(y==null||y(),p(h.current),s&&(a==null||a()),r&&r())};return V.jsx(i2,{isPresent:k,initial:!f.current||n?void 0:!1,custom:k?void 0:t,presenceAffectsLayout:i,mode:o,onExitComplete:k?void 0:P,children:C},T)})})},nt=e=>e;let yu=nt;function Xc(e){let t;return()=>(t===void 0&&(t=e()),t)}const Nr=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},Kt=e=>e*1e3,Qt=e=>e/1e3,l2={skipAnimations:!1,useManualTiming:!1};function a2(e){let t=new Set,n=new Set,r=!1,i=!1;const o=new WeakSet;let s={delta:0,timestamp:0,isProcessing:!1};function l(u){o.has(u)&&(a.schedule(u),e()),u(s)}const a={schedule:(u,c=!1,f=!1)=>{const d=f&&r?t:n;return c&&o.add(u),d.has(u)||d.add(u),u},cancel:u=>{n.delete(u),o.delete(u)},process:u=>{if(s=u,r){i=!0;return}r=!0,[t,n]=[n,t],t.forEach(l),t.clear(),r=!1,i&&(i=!1,a.process(u))}};return a}const _o=["read","resolveKeyframes","update","preRender","render","postRender"],u2=40;function O0(e,t){let n=!1,r=!0;const i={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,s=_o.reduce((p,m)=>(p[m]=a2(o),p),{}),{read:l,resolveKeyframes:a,update:u,preRender:c,render:f,postRender:h}=s,d=()=>{const p=performance.now();n=!1,i.delta=r?1e3/60:Math.max(Math.min(p-i.timestamp,u2),1),i.timestamp=p,i.isProcessing=!0,l.process(i),a.process(i),u.process(i),c.process(i),f.process(i),h.process(i),i.isProcessing=!1,n&&t&&(r=!1,e(d))},g=()=>{n=!0,r=!0,i.isProcessing||e(d)};return{schedule:_o.reduce((p,m)=>{const y=s[m];return p[m]=(C,T=!1,k=!1)=>(n||g(),y.schedule(C,T,k)),p},{}),cancel:p=>{for(let m=0;m<_o.length;m++)s[_o[m]].cancel(p)},state:i,steps:s}}const{schedule:ue,cancel:Cn,state:De,steps:Zl}=O0(typeof requestAnimationFrame<"u"?requestAnimationFrame:nt,!0),z0=b.createContext({strict:!1}),Yd={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},jr={};for(const e in Yd)jr[e]={isEnabled:t=>Yd[e].some(n=>!!t[n])};function c2(e){for(const t in e)jr[t]={...jr[t],...e[t]}}const f2=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Rs(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||f2.has(e)}let F0=e=>!Rs(e);function h2(e){e&&(F0=t=>t.startsWith("on")?!Rs(t):e(t))}try{h2(require("@emotion/is-prop-valid").default)}catch{}function d2(e,t,n){const r={};for(const i in e)i==="values"&&typeof e.values=="object"||(F0(i)||n===!0&&Rs(i)||!t&&!Rs(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}function p2(e){if(typeof Proxy>"u")return e;const t=new Map,n=(...r)=>e(...r);return new Proxy(n,{get:(r,i)=>i==="create"?e:(t.has(i)||t.set(i,e(i)),t.get(i))})}const sl=b.createContext({});function Qi(e){return typeof e=="string"||Array.isArray(e)}function ll(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const Zc=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Jc=["initial",...Zc];function al(e){return ll(e.animate)||Jc.some(t=>Qi(e[t]))}function N0(e){return!!(al(e)||e.variants)}function m2(e,t){if(al(e)){const{initial:n,animate:r}=e;return{initial:n===!1||Qi(n)?n:void 0,animate:Qi(r)?r:void 0}}return e.inherit!==!1?t:{}}function g2(e){const{initial:t,animate:n}=m2(e,b.useContext(sl));return b.useMemo(()=>({initial:t,animate:n}),[qd(t),qd(n)])}function qd(e){return Array.isArray(e)?e.join(" "):e}const y2=Symbol.for("motionComponentSymbol");function xr(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function v2(e,t,n){return b.useCallback(r=>{r&&e.onMount&&e.onMount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):xr(n)&&(n.current=r))},[t])}const ef=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),x2="framerAppearId",j0="data-"+ef(x2),{schedule:tf,cancel:RR}=O0(queueMicrotask,!1),V0=b.createContext({});function w2(e,t,n,r,i){var o,s;const{visualElement:l}=b.useContext(sl),a=b.useContext(z0),u=b.useContext(ol),c=b.useContext(Yc).reducedMotion,f=b.useRef(null);r=r||a.renderer,!f.current&&r&&(f.current=r(e,{visualState:t,parent:l,props:n,presenceContext:u,blockInitialAnimation:u?u.initial===!1:!1,reducedMotionConfig:c}));const h=f.current,d=b.useContext(V0);h&&!h.projection&&i&&(h.type==="html"||h.type==="svg")&&k2(f.current,n,i,d);const g=b.useRef(!1);b.useInsertionEffect(()=>{h&&g.current&&h.update(n,u)});const v=n[j0],w=b.useRef(!!v&&!(!((o=window.MotionHandoffIsComplete)===null||o===void 0)&&o.call(window,v))&&((s=window.MotionHasOptimisedAnimation)===null||s===void 0?void 0:s.call(window,v)));return _0(()=>{h&&(g.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),tf.render(h.render),w.current&&h.animationState&&h.animationState.animateChanges())}),b.useEffect(()=>{h&&(!w.current&&h.animationState&&h.animationState.animateChanges(),w.current&&(queueMicrotask(()=>{var p;(p=window.MotionHandoffMarkAsComplete)===null||p===void 0||p.call(window,v)}),w.current=!1))}),h}function k2(e,t,n,r){const{layoutId:i,layout:o,drag:s,dragConstraints:l,layoutScroll:a,layoutRoot:u}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:B0(e.parent)),e.projection.setOptions({layoutId:i,layout:o,alwaysMeasureLayout:!!s||l&&xr(l),visualElement:e,animationType:typeof o=="string"?o:"both",initialPromotionConfig:r,layoutScroll:a,layoutRoot:u})}function B0(e){if(e)return e.options.allowProjection!==!1?e.projection:B0(e.parent)}function S2({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:i}){var o,s;e&&c2(e);function l(u,c){let f;const h={...b.useContext(Yc),...u,layoutId:C2(u)},{isStatic:d}=h,g=g2(u),v=r(u,d);if(!d&&qc){E2();const w=T2(h);f=w.MeasureLayout,g.visualElement=w2(i,v,h,t,w.ProjectionNode)}return V.jsxs(sl.Provider,{value:g,children:[f&&g.visualElement?V.jsx(f,{visualElement:g.visualElement,...h}):null,n(i,u,v2(v,g.visualElement,c),v,d,g.visualElement)]})}l.displayName=`motion.${typeof i=="string"?i:`create(${(s=(o=i.displayName)!==null&&o!==void 0?o:i.name)!==null&&s!==void 0?s:""})`}`;const a=b.forwardRef(l);return a[y2]=i,a}function C2({layoutId:e}){const t=b.useContext(Qc).id;return t&&e!==void 0?t+"-"+e:e}function E2(e,t){b.useContext(z0).strict}function T2(e){const{drag:t,layout:n}=jr;if(!t&&!n)return{};const r={...t,...n};return{MeasureLayout:t!=null&&t.isEnabled(e)||n!=null&&n.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}const P2=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function nf(e){return typeof e!="string"||e.includes("-")?!1:!!(P2.indexOf(e)>-1||/[A-Z]/u.test(e))}function Xd(e){const t=[{},{}];return e==null||e.values.forEach((n,r)=>{t[0][r]=n.get(),t[1][r]=n.getVelocity()}),t}function rf(e,t,n,r){if(typeof t=="function"){const[i,o]=Xd(r);t=t(n!==void 0?n:e.custom,i,o)}if(typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"){const[i,o]=Xd(r);t=t(n!==void 0?n:e.custom,i,o)}return t}const vu=e=>Array.isArray(e),A2=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),D2=e=>vu(e)?e[e.length-1]||0:e,ze=e=>!!(e&&e.getVelocity);function Xo(e){const t=ze(e)?e.get():e;return A2(t)?t.toValue():t}function L2({scrapeMotionValuesFromProps:e,createRenderState:t,onUpdate:n},r,i,o){const s={latestValues:b2(r,i,o,e),renderState:t()};return n&&(s.onMount=l=>n({props:r,current:l,...s}),s.onUpdate=l=>n(l)),s}const U0=e=>(t,n)=>{const r=b.useContext(sl),i=b.useContext(ol),o=()=>L2(e,t,r,i);return n?o():Gc(o)};function b2(e,t,n,r){const i={},o=r(e,{});for(const h in o)i[h]=Xo(o[h]);let{initial:s,animate:l}=e;const a=al(e),u=N0(e);t&&u&&!a&&e.inherit!==!1&&(s===void 0&&(s=t.initial),l===void 0&&(l=t.animate));let c=n?n.initial===!1:!1;c=c||s===!1;const f=c?l:s;if(f&&typeof f!="boolean"&&!ll(f)){const h=Array.isArray(f)?f:[f];for(let d=0;d<h.length;d++){const g=rf(e,h[d]);if(g){const{transitionEnd:v,transition:w,...p}=g;for(const m in p){let y=p[m];if(Array.isArray(y)){const C=c?y.length-1:0;y=y[C]}y!==null&&(i[m]=y)}for(const m in v)i[m]=v[m]}}}return i}const Kr=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],er=new Set(Kr),$0=e=>t=>typeof t=="string"&&t.startsWith(e),H0=$0("--"),R2=$0("var(--"),of=e=>R2(e)?M2.test(e.split("/*")[0].trim()):!1,M2=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,W0=(e,t)=>t&&typeof e=="number"?t.transform(e):e,Zt=(e,t,n)=>n>t?t:n<e?e:n,Qr={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},Gi={...Qr,transform:e=>Zt(0,1,e)},Oo={...Qr,default:1},so=e=>({test:t=>typeof t=="string"&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),on=so("deg"),zt=so("%"),U=so("px"),I2=so("vh"),_2=so("vw"),Zd={...zt,parse:e=>zt.parse(e)/100,transform:e=>zt.transform(e*100)},O2={borderWidth:U,borderTopWidth:U,borderRightWidth:U,borderBottomWidth:U,borderLeftWidth:U,borderRadius:U,radius:U,borderTopLeftRadius:U,borderTopRightRadius:U,borderBottomRightRadius:U,borderBottomLeftRadius:U,width:U,maxWidth:U,height:U,maxHeight:U,top:U,right:U,bottom:U,left:U,padding:U,paddingTop:U,paddingRight:U,paddingBottom:U,paddingLeft:U,margin:U,marginTop:U,marginRight:U,marginBottom:U,marginLeft:U,backgroundPositionX:U,backgroundPositionY:U},z2={rotate:on,rotateX:on,rotateY:on,rotateZ:on,scale:Oo,scaleX:Oo,scaleY:Oo,scaleZ:Oo,skew:on,skewX:on,skewY:on,distance:U,translateX:U,translateY:U,translateZ:U,x:U,y:U,z:U,perspective:U,transformPerspective:U,opacity:Gi,originX:Zd,originY:Zd,originZ:U},Jd={...Qr,transform:Math.round},sf={...O2,...z2,zIndex:Jd,size:U,fillOpacity:Gi,strokeOpacity:Gi,numOctaves:Jd},F2={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},N2=Kr.length;function j2(e,t,n){let r="",i=!0;for(let o=0;o<N2;o++){const s=Kr[o],l=e[s];if(l===void 0)continue;let a=!0;if(typeof l=="number"?a=l===(s.startsWith("scale")?1:0):a=parseFloat(l)===0,!a||n){const u=W0(l,sf[s]);if(!a){i=!1;const c=F2[s]||s;r+=`${c}(${u}) `}n&&(t[s]=u)}}return r=r.trim(),n?r=n(t,i?"":r):i&&(r="none"),r}function lf(e,t,n){const{style:r,vars:i,transformOrigin:o}=e;let s=!1,l=!1;for(const a in t){const u=t[a];if(er.has(a)){s=!0;continue}else if(H0(a)){i[a]=u;continue}else{const c=W0(u,sf[a]);a.startsWith("origin")?(l=!0,o[a]=c):r[a]=c}}if(t.transform||(s||n?r.transform=j2(t,e.transform,n):r.transform&&(r.transform="none")),l){const{originX:a="50%",originY:u="50%",originZ:c=0}=o;r.transformOrigin=`${a} ${u} ${c}`}}const V2={offset:"stroke-dashoffset",array:"stroke-dasharray"},B2={offset:"strokeDashoffset",array:"strokeDasharray"};function U2(e,t,n=1,r=0,i=!0){e.pathLength=1;const o=i?V2:B2;e[o.offset]=U.transform(-r);const s=U.transform(t),l=U.transform(n);e[o.array]=`${s} ${l}`}function ep(e,t,n){return typeof e=="string"?e:U.transform(t+n*e)}function $2(e,t,n){const r=ep(t,e.x,e.width),i=ep(n,e.y,e.height);return`${r} ${i}`}function af(e,{attrX:t,attrY:n,attrScale:r,originX:i,originY:o,pathLength:s,pathSpacing:l=1,pathOffset:a=0,...u},c,f){if(lf(e,u,f),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:h,style:d,dimensions:g}=e;h.transform&&(g&&(d.transform=h.transform),delete h.transform),g&&(i!==void 0||o!==void 0||d.transform)&&(d.transformOrigin=$2(g,i!==void 0?i:.5,o!==void 0?o:.5)),t!==void 0&&(h.x=t),n!==void 0&&(h.y=n),r!==void 0&&(h.scale=r),s!==void 0&&U2(h,s,l,a,!1)}const uf=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),K0=()=>({...uf(),attrs:{}}),cf=e=>typeof e=="string"&&e.toLowerCase()==="svg";function Q0(e,{style:t,vars:n},r,i){Object.assign(e.style,t,i&&i.getProjectionStyles(r));for(const o in n)e.style.setProperty(o,n[o])}const G0=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Y0(e,t,n,r){Q0(e,t,void 0,r);for(const i in t.attrs)e.setAttribute(G0.has(i)?i:ef(i),t.attrs[i])}const Ms={};function H2(e){Object.assign(Ms,e)}function q0(e,{layout:t,layoutId:n}){return er.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!Ms[e]||e==="opacity")}function ff(e,t,n){var r;const{style:i}=e,o={};for(const s in i)(ze(i[s])||t.style&&ze(t.style[s])||q0(s,e)||((r=n==null?void 0:n.getValue(s))===null||r===void 0?void 0:r.liveStyle)!==void 0)&&(o[s]=i[s]);return o}function X0(e,t,n){const r=ff(e,t,n);for(const i in e)if(ze(e[i])||ze(t[i])){const o=Kr.indexOf(i)!==-1?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i;r[o]=e[i]}return r}function W2(e,t){try{t.dimensions=typeof e.getBBox=="function"?e.getBBox():e.getBoundingClientRect()}catch{t.dimensions={x:0,y:0,width:0,height:0}}}const tp=["x","y","width","height","cx","cy","r"],K2={useVisualState:U0({scrapeMotionValuesFromProps:X0,createRenderState:K0,onUpdate:({props:e,prevProps:t,current:n,renderState:r,latestValues:i})=>{if(!n)return;let o=!!e.drag;if(!o){for(const l in i)if(er.has(l)){o=!0;break}}if(!o)return;let s=!t;if(t)for(let l=0;l<tp.length;l++){const a=tp[l];e[a]!==t[a]&&(s=!0)}s&&ue.read(()=>{W2(n,r),ue.render(()=>{af(r,i,cf(n.tagName),e.transformTemplate),Y0(n,r)})})}})},Q2={useVisualState:U0({scrapeMotionValuesFromProps:ff,createRenderState:uf})};function Z0(e,t,n){for(const r in t)!ze(t[r])&&!q0(r,n)&&(e[r]=t[r])}function G2({transformTemplate:e},t){return b.useMemo(()=>{const n=uf();return lf(n,t,e),Object.assign({},n.vars,n.style)},[t])}function Y2(e,t){const n=e.style||{},r={};return Z0(r,n,e),Object.assign(r,G2(e,t)),r}function q2(e,t){const n={},r=Y2(e,t);return e.drag&&e.dragListener!==!1&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n}function X2(e,t,n,r){const i=b.useMemo(()=>{const o=K0();return af(o,t,cf(r),e.transformTemplate),{...o.attrs,style:{...o.style}}},[t]);if(e.style){const o={};Z0(o,e.style,e),i.style={...o,...i.style}}return i}function Z2(e=!1){return(n,r,i,{latestValues:o},s)=>{const a=(nf(n)?X2:q2)(r,o,s,n),u=d2(r,typeof n=="string",e),c=n!==b.Fragment?{...u,...a,ref:i}:{},{children:f}=r,h=b.useMemo(()=>ze(f)?f.get():f,[f]);return b.createElement(n,{...c,children:h})}}function J2(e,t){return function(r,{forwardMotionProps:i}={forwardMotionProps:!1}){const s={...nf(r)?K2:Q2,preloadedFeatures:e,useRender:Z2(i),createVisualElement:t,Component:r};return S2(s)}}function J0(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function ul(e,t,n){const r=e.getProps();return rf(r,t,n!==void 0?n:r.custom,e)}const eD=Xc(()=>window.ScrollTimeline!==void 0);class tD{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>"finished"in t?t.finished:t))}getAll(t){return this.animations[0][t]}setAll(t,n){for(let r=0;r<this.animations.length;r++)this.animations[r][t]=n}attachTimeline(t,n){const r=this.animations.map(i=>{if(eD()&&i.attachTimeline)return i.attachTimeline(t);if(typeof n=="function")return n(i)});return()=>{r.forEach((i,o)=>{i&&i(),this.animations[o].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let n=0;n<this.animations.length;n++)t=Math.max(t,this.animations[n].duration);return t}runAll(t){this.animations.forEach(n=>n[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class nD extends tD{then(t,n){return Promise.all(this.animations).then(t).catch(n)}}function hf(e,t){return e?e[t]||e.default||e:void 0}const xu=2e4;function ev(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<xu;)t+=n,r=e.next(t);return t>=xu?1/0:t}function df(e){return typeof e=="function"}function np(e,t){e.timeline=t,e.onfinish=null}const pf=e=>Array.isArray(e)&&typeof e[0]=="number",rD={linearEasing:void 0};function iD(e,t){const n=Xc(e);return()=>{var r;return(r=rD[t])!==null&&r!==void 0?r:n()}}const Is=iD(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),tv=(e,t,n=10)=>{let r="";const i=Math.max(Math.round(t/n),2);for(let o=0;o<i;o++)r+=e(Nr(0,i-1,o))+", ";return`linear(${r.substring(0,r.length-2)})`};function nv(e){return!!(typeof e=="function"&&Is()||!e||typeof e=="string"&&(e in wu||Is())||pf(e)||Array.isArray(e)&&e.every(nv))}const fi=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,wu={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:fi([0,.65,.55,1]),circOut:fi([.55,0,1,.45]),backIn:fi([.31,.01,.66,-.59]),backOut:fi([.33,1.53,.69,.99])};function rv(e,t){if(e)return typeof e=="function"&&Is()?tv(e,t):pf(e)?fi(e):Array.isArray(e)?e.map(n=>rv(n,t)||wu.easeOut):wu[e]}const kt={x:!1,y:!1};function iv(){return kt.x||kt.y}function oD(e,t,n){var r;if(e instanceof Element)return[e];if(typeof e=="string"){let i=document;const o=(r=void 0)!==null&&r!==void 0?r:i.querySelectorAll(e);return o?Array.from(o):[]}return Array.from(e)}function ov(e,t){const n=oD(e),r=new AbortController,i={passive:!0,...t,signal:r.signal};return[n,i,()=>r.abort()]}function rp(e){return t=>{t.pointerType==="touch"||iv()||e(t)}}function sD(e,t,n={}){const[r,i,o]=ov(e,n),s=rp(l=>{const{target:a}=l,u=t(l);if(typeof u!="function"||!a)return;const c=rp(f=>{u(f),a.removeEventListener("pointerleave",c)});a.addEventListener("pointerleave",c,i)});return r.forEach(l=>{l.addEventListener("pointerenter",s,i)}),o}const sv=(e,t)=>t?e===t?!0:sv(e,t.parentElement):!1,mf=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1,lD=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function aD(e){return lD.has(e.tagName)||e.tabIndex!==-1}const hi=new WeakSet;function ip(e){return t=>{t.key==="Enter"&&e(t)}}function Jl(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}const uD=(e,t)=>{const n=e.currentTarget;if(!n)return;const r=ip(()=>{if(hi.has(n))return;Jl(n,"down");const i=ip(()=>{Jl(n,"up")}),o=()=>Jl(n,"cancel");n.addEventListener("keyup",i,t),n.addEventListener("blur",o,t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)};function op(e){return mf(e)&&!iv()}function cD(e,t,n={}){const[r,i,o]=ov(e,n),s=l=>{const a=l.currentTarget;if(!op(l)||hi.has(a))return;hi.add(a);const u=t(l),c=(d,g)=>{window.removeEventListener("pointerup",f),window.removeEventListener("pointercancel",h),!(!op(d)||!hi.has(a))&&(hi.delete(a),typeof u=="function"&&u(d,{success:g}))},f=d=>{c(d,n.useGlobalTarget||sv(a,d.target))},h=d=>{c(d,!1)};window.addEventListener("pointerup",f,i),window.addEventListener("pointercancel",h,i)};return r.forEach(l=>{!aD(l)&&l.getAttribute("tabindex")===null&&(l.tabIndex=0),(n.useGlobalTarget?window:l).addEventListener("pointerdown",s,i),l.addEventListener("focus",u=>uD(u,i),i)}),o}function fD(e){return e==="x"||e==="y"?kt[e]?null:(kt[e]=!0,()=>{kt[e]=!1}):kt.x||kt.y?null:(kt.x=kt.y=!0,()=>{kt.x=kt.y=!1})}const lv=new Set(["width","height","top","left","right","bottom",...Kr]);let Zo;function hD(){Zo=void 0}const Ft={now:()=>(Zo===void 0&&Ft.set(De.isProcessing||l2.useManualTiming?De.timestamp:performance.now()),Zo),set:e=>{Zo=e,queueMicrotask(hD)}};function gf(e,t){e.indexOf(t)===-1&&e.push(t)}function yf(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class vf{constructor(){this.subscriptions=[]}add(t){return gf(this.subscriptions,t),()=>yf(this.subscriptions,t)}notify(t,n,r){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](t,n,r);else for(let o=0;o<i;o++){const s=this.subscriptions[o];s&&s(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function av(e,t){return t?e*(1e3/t):0}const sp=30,dD=e=>!isNaN(parseFloat(e));class pD{constructor(t,n={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(r,i=!0)=>{const o=Ft.now();this.updatedAt!==o&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(r),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=n.owner}setCurrent(t){this.current=t,this.updatedAt=Ft.now(),this.canTrackVelocity===null&&t!==void 0&&(this.canTrackVelocity=dD(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new vf);const r=this.events[t].add(n);return t==="change"?()=>{r(),ue.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-r}jump(t,n=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=Ft.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||t-this.updatedAt>sp)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,sp);return av(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Yi(e,t){return new pD(e,t)}function mD(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,Yi(n))}function gD(e,t){const n=ul(e,t);let{transitionEnd:r={},transition:i={},...o}=n||{};o={...o,...r};for(const s in o){const l=D2(o[s]);mD(e,s,l)}}function yD(e){return!!(ze(e)&&e.add)}function ku(e,t){const n=e.getValue("willChange");if(yD(n))return n.add(t)}function uv(e){return e.props[j0]}const cv=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,vD=1e-7,xD=12;function wD(e,t,n,r,i){let o,s,l=0;do s=t+(n-t)/2,o=cv(s,r,i)-e,o>0?n=s:t=s;while(Math.abs(o)>vD&&++l<xD);return s}function lo(e,t,n,r){if(e===t&&n===r)return nt;const i=o=>wD(o,0,1,e,n);return o=>o===0||o===1?o:cv(i(o),t,r)}const fv=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,hv=e=>t=>1-e(1-t),dv=lo(.33,1.53,.69,.99),xf=hv(dv),pv=fv(xf),mv=e=>(e*=2)<1?.5*xf(e):.5*(2-Math.pow(2,-10*(e-1))),wf=e=>1-Math.sin(Math.acos(e)),gv=hv(wf),yv=fv(wf),vv=e=>/^0[^.\s]+$/u.test(e);function kD(e){return typeof e=="number"?e===0:e!==null?e==="none"||e==="0"||vv(e):!0}const Ti=e=>Math.round(e*1e5)/1e5,kf=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function SD(e){return e==null}const CD=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Sf=(e,t)=>n=>!!(typeof n=="string"&&CD.test(n)&&n.startsWith(e)||t&&!SD(n)&&Object.prototype.hasOwnProperty.call(n,t)),xv=(e,t,n)=>r=>{if(typeof r!="string")return r;const[i,o,s,l]=r.match(kf);return{[e]:parseFloat(i),[t]:parseFloat(o),[n]:parseFloat(s),alpha:l!==void 0?parseFloat(l):1}},ED=e=>Zt(0,255,e),ea={...Qr,transform:e=>Math.round(ED(e))},Bn={test:Sf("rgb","red"),parse:xv("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+ea.transform(e)+", "+ea.transform(t)+", "+ea.transform(n)+", "+Ti(Gi.transform(r))+")"};function TD(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}}const Su={test:Sf("#"),parse:TD,transform:Bn.transform},wr={test:Sf("hsl","hue"),parse:xv("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+zt.transform(Ti(t))+", "+zt.transform(Ti(n))+", "+Ti(Gi.transform(r))+")"},Ie={test:e=>Bn.test(e)||Su.test(e)||wr.test(e),parse:e=>Bn.test(e)?Bn.parse(e):wr.test(e)?wr.parse(e):Su.parse(e),transform:e=>typeof e=="string"?e:e.hasOwnProperty("red")?Bn.transform(e):wr.transform(e)},PD=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function AD(e){var t,n;return isNaN(e)&&typeof e=="string"&&(((t=e.match(kf))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(PD))===null||n===void 0?void 0:n.length)||0)>0}const wv="number",kv="color",DD="var",LD="var(",lp="${}",bD=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function qi(e){const t=e.toString(),n=[],r={color:[],number:[],var:[]},i=[];let o=0;const l=t.replace(bD,a=>(Ie.test(a)?(r.color.push(o),i.push(kv),n.push(Ie.parse(a))):a.startsWith(LD)?(r.var.push(o),i.push(DD),n.push(a)):(r.number.push(o),i.push(wv),n.push(parseFloat(a))),++o,lp)).split(lp);return{values:n,split:l,indexes:r,types:i}}function Sv(e){return qi(e).values}function Cv(e){const{split:t,types:n}=qi(e),r=t.length;return i=>{let o="";for(let s=0;s<r;s++)if(o+=t[s],i[s]!==void 0){const l=n[s];l===wv?o+=Ti(i[s]):l===kv?o+=Ie.transform(i[s]):o+=i[s]}return o}}const RD=e=>typeof e=="number"?0:e;function MD(e){const t=Sv(e);return Cv(e)(t.map(RD))}const En={test:AD,parse:Sv,createTransformer:Cv,getAnimatableNone:MD},ID=new Set(["brightness","contrast","saturate","opacity"]);function _D(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(kf)||[];if(!r)return e;const i=n.replace(r,"");let o=ID.has(t)?1:0;return r!==n&&(o*=100),t+"("+o+i+")"}const OD=/\b([a-z-]*)\(.*?\)/gu,Cu={...En,getAnimatableNone:e=>{const t=e.match(OD);return t?t.map(_D).join(" "):e}},zD={...sf,color:Ie,backgroundColor:Ie,outlineColor:Ie,fill:Ie,stroke:Ie,borderColor:Ie,borderTopColor:Ie,borderRightColor:Ie,borderBottomColor:Ie,borderLeftColor:Ie,filter:Cu,WebkitFilter:Cu},Cf=e=>zD[e];function Ev(e,t){let n=Cf(e);return n!==Cu&&(n=En),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const FD=new Set(["auto","none","0"]);function ND(e,t,n){let r=0,i;for(;r<e.length&&!i;){const o=e[r];typeof o=="string"&&!FD.has(o)&&qi(o).values.length&&(i=e[r]),r++}if(i&&n)for(const o of t)e[o]=Ev(n,i)}const ap=e=>e===Qr||e===U,up=(e,t)=>parseFloat(e.split(", ")[t]),cp=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const i=r.match(/^matrix3d\((.+)\)$/u);if(i)return up(i[1],t);{const o=r.match(/^matrix\((.+)\)$/u);return o?up(o[1],e):0}},jD=new Set(["x","y","z"]),VD=Kr.filter(e=>!jD.has(e));function BD(e){const t=[];return VD.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t}const Vr={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:cp(4,13),y:cp(5,14)};Vr.translateX=Vr.x;Vr.translateY=Vr.y;const Hn=new Set;let Eu=!1,Tu=!1;function Tv(){if(Tu){const e=Array.from(Hn).filter(r=>r.needsMeasurement),t=new Set(e.map(r=>r.element)),n=new Map;t.forEach(r=>{const i=BD(r);i.length&&(n.set(r,i),r.render())}),e.forEach(r=>r.measureInitialState()),t.forEach(r=>{r.render();const i=n.get(r);i&&i.forEach(([o,s])=>{var l;(l=r.getValue(o))===null||l===void 0||l.set(s)})}),e.forEach(r=>r.measureEndState()),e.forEach(r=>{r.suspendedScrollY!==void 0&&window.scrollTo(0,r.suspendedScrollY)})}Tu=!1,Eu=!1,Hn.forEach(e=>e.complete()),Hn.clear()}function Pv(){Hn.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(Tu=!0)})}function UD(){Pv(),Tv()}class Ef{constructor(t,n,r,i,o,s=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=n,this.name=r,this.motionValue=i,this.element=o,this.isAsync=s}scheduleResolve(){this.isScheduled=!0,this.isAsync?(Hn.add(this),Eu||(Eu=!0,ue.read(Pv),ue.resolveKeyframes(Tv))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:n,element:r,motionValue:i}=this;for(let o=0;o<t.length;o++)if(t[o]===null)if(o===0){const s=i==null?void 0:i.get(),l=t[t.length-1];if(s!==void 0)t[0]=s;else if(r&&n){const a=r.readValue(n,l);a!=null&&(t[0]=a)}t[0]===void 0&&(t[0]=l),i&&s===void 0&&i.set(t[0])}else t[o]=t[o-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),Hn.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,Hn.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const Av=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),$D=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function HD(e){const t=$D.exec(e);if(!t)return[,];const[,n,r,i]=t;return[`--${n??r}`,i]}function Dv(e,t,n=1){const[r,i]=HD(e);if(!r)return;const o=window.getComputedStyle(t).getPropertyValue(r);if(o){const s=o.trim();return Av(s)?parseFloat(s):s}return of(i)?Dv(i,t,n+1):i}const Lv=e=>t=>t.test(e),WD={test:e=>e==="auto",parse:e=>e},bv=[Qr,U,zt,on,_2,I2,WD],fp=e=>bv.find(Lv(e));class Rv extends Ef{constructor(t,n,r,i,o){super(t,n,r,i,o,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:n,name:r}=this;if(!n||!n.current)return;super.readKeyframes();for(let a=0;a<t.length;a++){let u=t[a];if(typeof u=="string"&&(u=u.trim(),of(u))){const c=Dv(u,n.current);c!==void 0&&(t[a]=c),a===t.length-1&&(this.finalKeyframe=u)}}if(this.resolveNoneKeyframes(),!lv.has(r)||t.length!==2)return;const[i,o]=t,s=fp(i),l=fp(o);if(s!==l)if(ap(s)&&ap(l))for(let a=0;a<t.length;a++){const u=t[a];typeof u=="string"&&(t[a]=parseFloat(u))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:n}=this,r=[];for(let i=0;i<t.length;i++)kD(t[i])&&r.push(i);r.length&&ND(t,r,n)}measureInitialState(){const{element:t,unresolvedKeyframes:n,name:r}=this;if(!t||!t.current)return;r==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=Vr[r](t.measureViewportBox(),window.getComputedStyle(t.current)),n[0]=this.measuredOrigin;const i=n[n.length-1];i!==void 0&&t.getValue(r,i).jump(i,!1)}measureEndState(){var t;const{element:n,name:r,unresolvedKeyframes:i}=this;if(!n||!n.current)return;const o=n.getValue(r);o&&o.jump(this.measuredOrigin,!1);const s=i.length-1,l=i[s];i[s]=Vr[r](n.measureViewportBox(),window.getComputedStyle(n.current)),l!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=l),!((t=this.removedTransforms)===null||t===void 0)&&t.length&&this.removedTransforms.forEach(([a,u])=>{n.getValue(a).set(u)}),this.resolveNoneKeyframes()}}const hp=(e,t)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&(En.test(e)||e==="0")&&!e.startsWith("url("));function KD(e){const t=e[0];if(e.length===1)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}function QD(e,t,n,r){const i=e[0];if(i===null)return!1;if(t==="display"||t==="visibility")return!0;const o=e[e.length-1],s=hp(i,t),l=hp(o,t);return!s||!l?!1:KD(e)||(n==="spring"||df(n))&&r}const GD=e=>e!==null;function cl(e,{repeat:t,repeatType:n="loop"},r){const i=e.filter(GD),o=t&&n!=="loop"&&t%2===1?0:i.length-1;return!o||r===void 0?i[o]:r}const YD=40;class Mv{constructor({autoplay:t=!0,delay:n=0,type:r="keyframes",repeat:i=0,repeatDelay:o=0,repeatType:s="loop",...l}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=Ft.now(),this.options={autoplay:t,delay:n,type:r,repeat:i,repeatDelay:o,repeatType:s,...l},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt?this.resolvedAt-this.createdAt>YD?this.resolvedAt:this.createdAt:this.createdAt}get resolved(){return!this._resolved&&!this.hasAttemptedResolve&&UD(),this._resolved}onKeyframesResolved(t,n){this.resolvedAt=Ft.now(),this.hasAttemptedResolve=!0;const{name:r,type:i,velocity:o,delay:s,onComplete:l,onUpdate:a,isGenerator:u}=this.options;if(!u&&!QD(t,r,i,o))if(s)this.options.duration=0;else{a&&a(cl(t,this.options,n)),l&&l(),this.resolveFinishedPromise();return}const c=this.initPlayback(t,n);c!==!1&&(this._resolved={keyframes:t,finalKeyframe:n,...c},this.onPostResolved())}onPostResolved(){}then(t,n){return this.currentFinishedPromise.then(t,n)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}const de=(e,t,n)=>e+(t-e)*n;function ta(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function qD({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let i=0,o=0,s=0;if(!t)i=o=s=n;else{const l=n<.5?n*(1+t):n+t-n*t,a=2*n-l;i=ta(a,l,e+1/3),o=ta(a,l,e),s=ta(a,l,e-1/3)}return{red:Math.round(i*255),green:Math.round(o*255),blue:Math.round(s*255),alpha:r}}function _s(e,t){return n=>n>0?t:e}const na=(e,t,n)=>{const r=e*e,i=n*(t*t-r)+r;return i<0?0:Math.sqrt(i)},XD=[Su,Bn,wr],ZD=e=>XD.find(t=>t.test(e));function dp(e){const t=ZD(e);if(!t)return!1;let n=t.parse(e);return t===wr&&(n=qD(n)),n}const pp=(e,t)=>{const n=dp(e),r=dp(t);if(!n||!r)return _s(e,t);const i={...n};return o=>(i.red=na(n.red,r.red,o),i.green=na(n.green,r.green,o),i.blue=na(n.blue,r.blue,o),i.alpha=de(n.alpha,r.alpha,o),Bn.transform(i))},JD=(e,t)=>n=>t(e(n)),ao=(...e)=>e.reduce(JD),Pu=new Set(["none","hidden"]);function eL(e,t){return Pu.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}function tL(e,t){return n=>de(e,t,n)}function Tf(e){return typeof e=="number"?tL:typeof e=="string"?of(e)?_s:Ie.test(e)?pp:iL:Array.isArray(e)?Iv:typeof e=="object"?Ie.test(e)?pp:nL:_s}function Iv(e,t){const n=[...e],r=n.length,i=e.map((o,s)=>Tf(o)(o,t[s]));return o=>{for(let s=0;s<r;s++)n[s]=i[s](o);return n}}function nL(e,t){const n={...e,...t},r={};for(const i in n)e[i]!==void 0&&t[i]!==void 0&&(r[i]=Tf(e[i])(e[i],t[i]));return i=>{for(const o in r)n[o]=r[o](i);return n}}function rL(e,t){var n;const r=[],i={color:0,var:0,number:0};for(let o=0;o<t.values.length;o++){const s=t.types[o],l=e.indexes[s][i[s]],a=(n=e.values[l])!==null&&n!==void 0?n:0;r[o]=a,i[s]++}return r}const iL=(e,t)=>{const n=En.createTransformer(t),r=qi(e),i=qi(t);return r.indexes.var.length===i.indexes.var.length&&r.indexes.color.length===i.indexes.color.length&&r.indexes.number.length>=i.indexes.number.length?Pu.has(e)&&!i.values.length||Pu.has(t)&&!r.values.length?eL(e,t):ao(Iv(rL(r,i),i.values),n):_s(e,t)};function _v(e,t,n){return typeof e=="number"&&typeof t=="number"&&typeof n=="number"?de(e,t,n):Tf(e)(e,t)}const oL=5;function Ov(e,t,n){const r=Math.max(t-oL,0);return av(n-e(r),t-r)}const ye={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},ra=.001;function sL({duration:e=ye.duration,bounce:t=ye.bounce,velocity:n=ye.velocity,mass:r=ye.mass}){let i,o,s=1-t;s=Zt(ye.minDamping,ye.maxDamping,s),e=Zt(ye.minDuration,ye.maxDuration,Qt(e)),s<1?(i=u=>{const c=u*s,f=c*e,h=c-n,d=Au(u,s),g=Math.exp(-f);return ra-h/d*g},o=u=>{const f=u*s*e,h=f*n+n,d=Math.pow(s,2)*Math.pow(u,2)*e,g=Math.exp(-f),v=Au(Math.pow(u,2),s);return(-i(u)+ra>0?-1:1)*((h-d)*g)/v}):(i=u=>{const c=Math.exp(-u*e),f=(u-n)*e+1;return-ra+c*f},o=u=>{const c=Math.exp(-u*e),f=(n-u)*(e*e);return c*f});const l=5/e,a=aL(i,o,l);if(e=Kt(e),isNaN(a))return{stiffness:ye.stiffness,damping:ye.damping,duration:e};{const u=Math.pow(a,2)*r;return{stiffness:u,damping:s*2*Math.sqrt(r*u),duration:e}}}const lL=12;function aL(e,t,n){let r=n;for(let i=1;i<lL;i++)r=r-e(r)/t(r);return r}function Au(e,t){return e*Math.sqrt(1-t*t)}const uL=["duration","bounce"],cL=["stiffness","damping","mass"];function mp(e,t){return t.some(n=>e[n]!==void 0)}function fL(e){let t={velocity:ye.velocity,stiffness:ye.stiffness,damping:ye.damping,mass:ye.mass,isResolvedFromDuration:!1,...e};if(!mp(e,cL)&&mp(e,uL))if(e.visualDuration){const n=e.visualDuration,r=2*Math.PI/(n*1.2),i=r*r,o=2*Zt(.05,1,1-(e.bounce||0))*Math.sqrt(i);t={...t,mass:ye.mass,stiffness:i,damping:o}}else{const n=sL(e);t={...t,...n,mass:ye.mass},t.isResolvedFromDuration=!0}return t}function zv(e=ye.visualDuration,t=ye.bounce){const n=typeof e!="object"?{visualDuration:e,keyframes:[0,1],bounce:t}:e;let{restSpeed:r,restDelta:i}=n;const o=n.keyframes[0],s=n.keyframes[n.keyframes.length-1],l={done:!1,value:o},{stiffness:a,damping:u,mass:c,duration:f,velocity:h,isResolvedFromDuration:d}=fL({...n,velocity:-Qt(n.velocity||0)}),g=h||0,v=u/(2*Math.sqrt(a*c)),w=s-o,p=Qt(Math.sqrt(a/c)),m=Math.abs(w)<5;r||(r=m?ye.restSpeed.granular:ye.restSpeed.default),i||(i=m?ye.restDelta.granular:ye.restDelta.default);let y;if(v<1){const T=Au(p,v);y=k=>{const P=Math.exp(-v*p*k);return s-P*((g+v*p*w)/T*Math.sin(T*k)+w*Math.cos(T*k))}}else if(v===1)y=T=>s-Math.exp(-p*T)*(w+(g+p*w)*T);else{const T=p*Math.sqrt(v*v-1);y=k=>{const P=Math.exp(-v*p*k),A=Math.min(T*k,300);return s-P*((g+v*p*w)*Math.sinh(A)+T*w*Math.cosh(A))/T}}const C={calculatedDuration:d&&f||null,next:T=>{const k=y(T);if(d)l.done=T>=f;else{let P=0;v<1&&(P=T===0?Kt(g):Ov(y,T,k));const A=Math.abs(P)<=r,O=Math.abs(s-k)<=i;l.done=A&&O}return l.value=l.done?s:k,l},toString:()=>{const T=Math.min(ev(C),xu),k=tv(P=>C.next(T*P).value,T,30);return T+"ms "+k}};return C}function gp({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:s,min:l,max:a,restDelta:u=.5,restSpeed:c}){const f=e[0],h={done:!1,value:f},d=A=>l!==void 0&&A<l||a!==void 0&&A>a,g=A=>l===void 0?a:a===void 0||Math.abs(l-A)<Math.abs(a-A)?l:a;let v=n*t;const w=f+v,p=s===void 0?w:s(w);p!==w&&(v=p-f);const m=A=>-v*Math.exp(-A/r),y=A=>p+m(A),C=A=>{const O=m(A),E=y(A);h.done=Math.abs(O)<=u,h.value=h.done?p:E};let T,k;const P=A=>{d(h.value)&&(T=A,k=zv({keyframes:[h.value,g(h.value)],velocity:Ov(y,A,h.value),damping:i,stiffness:o,restDelta:u,restSpeed:c}))};return P(0),{calculatedDuration:null,next:A=>{let O=!1;return!k&&T===void 0&&(O=!0,C(A),P(A)),T!==void 0&&A>=T?k.next(A-T):(!O&&C(A),h)}}}const hL=lo(.42,0,1,1),dL=lo(0,0,.58,1),Fv=lo(.42,0,.58,1),pL=e=>Array.isArray(e)&&typeof e[0]!="number",yp={linear:nt,easeIn:hL,easeInOut:Fv,easeOut:dL,circIn:wf,circInOut:yv,circOut:gv,backIn:xf,backInOut:pv,backOut:dv,anticipate:mv},vp=e=>{if(pf(e)){yu(e.length===4);const[t,n,r,i]=e;return lo(t,n,r,i)}else if(typeof e=="string")return yu(yp[e]!==void 0),yp[e];return e};function mL(e,t,n){const r=[],i=n||_v,o=e.length-1;for(let s=0;s<o;s++){let l=i(e[s],e[s+1]);if(t){const a=Array.isArray(t)?t[s]||nt:t;l=ao(a,l)}r.push(l)}return r}function gL(e,t,{clamp:n=!0,ease:r,mixer:i}={}){const o=e.length;if(yu(o===t.length),o===1)return()=>t[0];if(o===2&&t[0]===t[1])return()=>t[1];const s=e[0]===e[1];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());const l=mL(t,r,i),a=l.length,u=c=>{if(s&&c<e[0])return t[0];let f=0;if(a>1)for(;f<e.length-2&&!(c<e[f+1]);f++);const h=Nr(e[f],e[f+1],c);return l[f](h)};return n?c=>u(Zt(e[0],e[o-1],c)):u}function yL(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const i=Nr(0,t,r);e.push(de(n,1,i))}}function vL(e){const t=[0];return yL(t,e.length-1),t}function xL(e,t){return e.map(n=>n*t)}function wL(e,t){return e.map(()=>t||Fv).splice(0,e.length-1)}function Os({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const i=pL(r)?r.map(vp):vp(r),o={done:!1,value:t[0]},s=xL(n&&n.length===t.length?n:vL(t),e),l=gL(s,t,{ease:Array.isArray(i)?i:wL(t,i)});return{calculatedDuration:e,next:a=>(o.value=l(a),o.done=a>=e,o)}}const kL=e=>{const t=({timestamp:n})=>e(n);return{start:()=>ue.update(t,!0),stop:()=>Cn(t),now:()=>De.isProcessing?De.timestamp:Ft.now()}},SL={decay:gp,inertia:gp,tween:Os,keyframes:Os,spring:zv},CL=e=>e/100;class Pf extends Mv{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.teardown();const{onStop:a}=this.options;a&&a()};const{name:n,motionValue:r,element:i,keyframes:o}=this.options,s=(i==null?void 0:i.KeyframeResolver)||Ef,l=(a,u)=>this.onKeyframesResolved(a,u);this.resolver=new s(o,l,n,r,i),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){const{type:n="keyframes",repeat:r=0,repeatDelay:i=0,repeatType:o,velocity:s=0}=this.options,l=df(n)?n:SL[n]||Os;let a,u;l!==Os&&typeof t[0]!="number"&&(a=ao(CL,_v(t[0],t[1])),t=[0,100]);const c=l({...this.options,keyframes:t});o==="mirror"&&(u=l({...this.options,keyframes:[...t].reverse(),velocity:-s})),c.calculatedDuration===null&&(c.calculatedDuration=ev(c));const{calculatedDuration:f}=c,h=f+i,d=h*(r+1)-i;return{generator:c,mirroredGenerator:u,mapPercentToKeyframes:a,calculatedDuration:f,resolvedDuration:h,totalDuration:d}}onPostResolved(){const{autoplay:t=!0}=this.options;this.play(),this.pendingPlayState==="paused"||!t?this.pause():this.state=this.pendingPlayState}tick(t,n=!1){const{resolved:r}=this;if(!r){const{keyframes:A}=this.options;return{done:!0,value:A[A.length-1]}}const{finalKeyframe:i,generator:o,mirroredGenerator:s,mapPercentToKeyframes:l,keyframes:a,calculatedDuration:u,totalDuration:c,resolvedDuration:f}=r;if(this.startTime===null)return o.next(0);const{delay:h,repeat:d,repeatType:g,repeatDelay:v,onUpdate:w}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-c/this.speed,this.startTime)),n?this.currentTime=t:this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;const p=this.currentTime-h*(this.speed>=0?1:-1),m=this.speed>=0?p<0:p>c;this.currentTime=Math.max(p,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=c);let y=this.currentTime,C=o;if(d){const A=Math.min(this.currentTime,c)/f;let O=Math.floor(A),E=A%1;!E&&A>=1&&(E=1),E===1&&O--,O=Math.min(O,d+1),!!(O%2)&&(g==="reverse"?(E=1-E,v&&(E-=v/f)):g==="mirror"&&(C=s)),y=Zt(0,1,E)*f}const T=m?{done:!1,value:a[0]}:C.next(y);l&&(T.value=l(T.value));let{done:k}=T;!m&&u!==null&&(k=this.speed>=0?this.currentTime>=c:this.currentTime<=0);const P=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&k);return P&&i!==void 0&&(T.value=cl(a,this.options,i)),w&&w(T.value),P&&this.finish(),T}get duration(){const{resolved:t}=this;return t?Qt(t.calculatedDuration):0}get time(){return Qt(this.currentTime)}set time(t){t=Kt(t),this.currentTime=t,this.holdTime!==null||this.speed===0?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){const n=this.playbackSpeed!==t;this.playbackSpeed=t,n&&(this.time=Qt(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;const{driver:t=kL,onPlay:n,startTime:r}=this.options;this.driver||(this.driver=t(o=>this.tick(o))),n&&n();const i=this.driver.now();this.holdTime!==null?this.startTime=i-this.holdTime:this.startTime?this.state==="finished"&&(this.startTime=i):this.startTime=r??this.calcStartTime(),this.state==="finished"&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=(t=this.currentTime)!==null&&t!==void 0?t:0}complete(){this.state!=="running"&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){this.cancelTime!==null&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}const EL=new Set(["opacity","clipPath","filter","transform"]);function TL(e,t,n,{delay:r=0,duration:i=300,repeat:o=0,repeatType:s="loop",ease:l="easeInOut",times:a}={}){const u={[t]:n};a&&(u.offset=a);const c=rv(l,i);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:r,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:s==="reverse"?"alternate":"normal"})}const PL=Xc(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),zs=10,AL=2e4;function DL(e){return df(e.type)||e.type==="spring"||!nv(e.ease)}function LL(e,t){const n=new Pf({...t,keyframes:e,repeat:0,delay:0,isGenerator:!0});let r={done:!1,value:e[0]};const i=[];let o=0;for(;!r.done&&o<AL;)r=n.sample(o),i.push(r.value),o+=zs;return{times:void 0,keyframes:i,duration:o-zs,ease:"linear"}}const Nv={anticipate:mv,backInOut:pv,circInOut:yv};function bL(e){return e in Nv}class xp extends Mv{constructor(t){super(t);const{name:n,motionValue:r,element:i,keyframes:o}=this.options;this.resolver=new Rv(o,(s,l)=>this.onKeyframesResolved(s,l),n,r,i),this.resolver.scheduleResolve()}initPlayback(t,n){let{duration:r=300,times:i,ease:o,type:s,motionValue:l,name:a,startTime:u}=this.options;if(!l.owner||!l.owner.current)return!1;if(typeof o=="string"&&Is()&&bL(o)&&(o=Nv[o]),DL(this.options)){const{onComplete:f,onUpdate:h,motionValue:d,element:g,...v}=this.options,w=LL(t,v);t=w.keyframes,t.length===1&&(t[1]=t[0]),r=w.duration,i=w.times,o=w.ease,s="keyframes"}const c=TL(l.owner.current,a,t,{...this.options,duration:r,times:i,ease:o});return c.startTime=u??this.calcStartTime(),this.pendingTimeline?(np(c,this.pendingTimeline),this.pendingTimeline=void 0):c.onfinish=()=>{const{onComplete:f}=this.options;l.set(cl(t,this.options,n)),f&&f(),this.cancel(),this.resolveFinishedPromise()},{animation:c,duration:r,times:i,type:s,ease:o,keyframes:t}}get duration(){const{resolved:t}=this;if(!t)return 0;const{duration:n}=t;return Qt(n)}get time(){const{resolved:t}=this;if(!t)return 0;const{animation:n}=t;return Qt(n.currentTime||0)}set time(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.currentTime=Kt(t)}get speed(){const{resolved:t}=this;if(!t)return 1;const{animation:n}=t;return n.playbackRate}set speed(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.playbackRate=t}get state(){const{resolved:t}=this;if(!t)return"idle";const{animation:n}=t;return n.playState}get startTime(){const{resolved:t}=this;if(!t)return null;const{animation:n}=t;return n.startTime}attachTimeline(t){if(!this._resolved)this.pendingTimeline=t;else{const{resolved:n}=this;if(!n)return nt;const{animation:r}=n;np(r,t)}return nt}play(){if(this.isStopped)return;const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.playState==="finished"&&this.updateFinishedPromise(),n.play()}pause(){const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:t}=this;if(!t)return;const{animation:n,keyframes:r,duration:i,type:o,ease:s,times:l}=t;if(n.playState==="idle"||n.playState==="finished")return;if(this.time){const{motionValue:u,onUpdate:c,onComplete:f,element:h,...d}=this.options,g=new Pf({...d,keyframes:r,duration:i,type:o,ease:s,times:l,isGenerator:!0}),v=Kt(this.time);u.setWithVelocity(g.sample(v-zs).value,g.sample(v).value,zs)}const{onStop:a}=this.options;a&&a(),this.cancel()}complete(){const{resolved:t}=this;t&&t.animation.finish()}cancel(){const{resolved:t}=this;t&&t.animation.cancel()}static supports(t){const{motionValue:n,name:r,repeatDelay:i,repeatType:o,damping:s,type:l}=t;if(!n||!n.owner||!(n.owner.current instanceof HTMLElement))return!1;const{onUpdate:a,transformTemplate:u}=n.owner.getProps();return PL()&&r&&EL.has(r)&&!a&&!u&&!i&&o!=="mirror"&&s!==0&&l!=="inertia"}}const RL={type:"spring",stiffness:500,damping:25,restSpeed:10},ML=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),IL={type:"keyframes",duration:.8},_L={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},OL=(e,{keyframes:t})=>t.length>2?IL:er.has(e)?e.startsWith("scale")?ML(t[1]):RL:_L;function zL({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:o,repeatType:s,repeatDelay:l,from:a,elapsed:u,...c}){return!!Object.keys(c).length}const Af=(e,t,n,r={},i,o)=>s=>{const l=hf(r,e)||{},a=l.delay||r.delay||0;let{elapsed:u=0}=r;u=u-Kt(a);let c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...l,delay:-u,onUpdate:h=>{t.set(h),l.onUpdate&&l.onUpdate(h)},onComplete:()=>{s(),l.onComplete&&l.onComplete()},name:e,motionValue:t,element:o?void 0:i};zL(l)||(c={...c,...OL(e,c)}),c.duration&&(c.duration=Kt(c.duration)),c.repeatDelay&&(c.repeatDelay=Kt(c.repeatDelay)),c.from!==void 0&&(c.keyframes[0]=c.from);let f=!1;if((c.type===!1||c.duration===0&&!c.repeatDelay)&&(c.duration=0,c.delay===0&&(f=!0)),f&&!o&&t.get()!==void 0){const h=cl(c.keyframes,l);if(h!==void 0)return ue.update(()=>{c.onUpdate(h),c.onComplete()}),new nD([])}return!o&&xp.supports(c)?new xp(c):new Pf(c)};function FL({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function jv(e,t,{delay:n=0,transitionOverride:r,type:i}={}){var o;let{transition:s=e.getDefaultTransition(),transitionEnd:l,...a}=t;r&&(s=r);const u=[],c=i&&e.animationState&&e.animationState.getState()[i];for(const f in a){const h=e.getValue(f,(o=e.latestValues[f])!==null&&o!==void 0?o:null),d=a[f];if(d===void 0||c&&FL(c,f))continue;const g={delay:n,...hf(s||{},f)};let v=!1;if(window.MotionHandoffAnimation){const p=uv(e);if(p){const m=window.MotionHandoffAnimation(p,f,ue);m!==null&&(g.startTime=m,v=!0)}}ku(e,f),h.start(Af(f,h,d,e.shouldReduceMotion&&lv.has(f)?{type:!1}:g,e,v));const w=h.animation;w&&u.push(w)}return l&&Promise.all(u).then(()=>{ue.update(()=>{l&&gD(e,l)})}),u}function Du(e,t,n={}){var r;const i=ul(e,t,n.type==="exit"?(r=e.presenceContext)===null||r===void 0?void 0:r.custom:void 0);let{transition:o=e.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(o=n.transitionOverride);const s=i?()=>Promise.all(jv(e,i,n)):()=>Promise.resolve(),l=e.variantChildren&&e.variantChildren.size?(u=0)=>{const{delayChildren:c=0,staggerChildren:f,staggerDirection:h}=o;return NL(e,t,c+u,f,h,n)}:()=>Promise.resolve(),{when:a}=o;if(a){const[u,c]=a==="beforeChildren"?[s,l]:[l,s];return u().then(()=>c())}else return Promise.all([s(),l(n.delay)])}function NL(e,t,n=0,r=0,i=1,o){const s=[],l=(e.variantChildren.size-1)*r,a=i===1?(u=0)=>u*r:(u=0)=>l-u*r;return Array.from(e.variantChildren).sort(jL).forEach((u,c)=>{u.notify("AnimationStart",t),s.push(Du(u,t,{...o,delay:n+a(c)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(s)}function jL(e,t){return e.sortNodePosition(t)}function VL(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const i=t.map(o=>Du(e,o,n));r=Promise.all(i)}else if(typeof t=="string")r=Du(e,t,n);else{const i=typeof t=="function"?ul(e,t,n.custom):t;r=Promise.all(jv(e,i,n))}return r.then(()=>{e.notify("AnimationComplete",t)})}const BL=Jc.length;function Vv(e){if(!e)return;if(!e.isControllingVariants){const n=e.parent?Vv(e.parent)||{}:{};return e.props.initial!==void 0&&(n.initial=e.props.initial),n}const t={};for(let n=0;n<BL;n++){const r=Jc[n],i=e.props[r];(Qi(i)||i===!1)&&(t[r]=i)}return t}const UL=[...Zc].reverse(),$L=Zc.length;function HL(e){return t=>Promise.all(t.map(({animation:n,options:r})=>VL(e,n,r)))}function WL(e){let t=HL(e),n=wp(),r=!0;const i=a=>(u,c)=>{var f;const h=ul(e,c,a==="exit"?(f=e.presenceContext)===null||f===void 0?void 0:f.custom:void 0);if(h){const{transition:d,transitionEnd:g,...v}=h;u={...u,...v,...g}}return u};function o(a){t=a(e)}function s(a){const{props:u}=e,c=Vv(e.parent)||{},f=[],h=new Set;let d={},g=1/0;for(let w=0;w<$L;w++){const p=UL[w],m=n[p],y=u[p]!==void 0?u[p]:c[p],C=Qi(y),T=p===a?m.isActive:null;T===!1&&(g=w);let k=y===c[p]&&y!==u[p]&&C;if(k&&r&&e.manuallyAnimateOnMount&&(k=!1),m.protectedKeys={...d},!m.isActive&&T===null||!y&&!m.prevProp||ll(y)||typeof y=="boolean")continue;const P=KL(m.prevProp,y);let A=P||p===a&&m.isActive&&!k&&C||w>g&&C,O=!1;const E=Array.isArray(y)?y:[y];let _=E.reduce(i(p),{});T===!1&&(_={});const{prevResolvedValues:N={}}=m,Y={...N,..._},J=F=>{A=!0,h.has(F)&&(O=!0,h.delete(F)),m.needsAnimating[F]=!0;const L=e.getValue(F);L&&(L.liveStyle=!1)};for(const F in Y){const L=_[F],z=N[F];if(d.hasOwnProperty(F))continue;let x=!1;vu(L)&&vu(z)?x=!J0(L,z):x=L!==z,x?L!=null?J(F):h.add(F):L!==void 0&&h.has(F)?J(F):m.protectedKeys[F]=!0}m.prevProp=y,m.prevResolvedValues=_,m.isActive&&(d={...d,..._}),r&&e.blockInitialAnimation&&(A=!1),A&&(!(k&&P)||O)&&f.push(...E.map(F=>({animation:F,options:{type:p}})))}if(h.size){const w={};h.forEach(p=>{const m=e.getBaseTarget(p),y=e.getValue(p);y&&(y.liveStyle=!0),w[p]=m??null}),f.push({animation:w})}let v=!!f.length;return r&&(u.initial===!1||u.initial===u.animate)&&!e.manuallyAnimateOnMount&&(v=!1),r=!1,v?t(f):Promise.resolve()}function l(a,u){var c;if(n[a].isActive===u)return Promise.resolve();(c=e.variantChildren)===null||c===void 0||c.forEach(h=>{var d;return(d=h.animationState)===null||d===void 0?void 0:d.setActive(a,u)}),n[a].isActive=u;const f=s(a);for(const h in n)n[h].protectedKeys={};return f}return{animateChanges:s,setActive:l,setAnimateFunction:o,getState:()=>n,reset:()=>{n=wp(),r=!0}}}function KL(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!J0(t,e):!1}function Mn(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function wp(){return{animate:Mn(!0),whileInView:Mn(),whileHover:Mn(),whileTap:Mn(),whileDrag:Mn(),whileFocus:Mn(),exit:Mn()}}class Ln{constructor(t){this.isMounted=!1,this.node=t}update(){}}class QL extends Ln{constructor(t){super(t),t.animationState||(t.animationState=WL(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();ll(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),(t=this.unmountControls)===null||t===void 0||t.call(this)}}let GL=0;class YL extends Ln{constructor(){super(...arguments),this.id=GL++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===r)return;const i=this.node.animationState.setActive("exit",!t);n&&!t&&i.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const qL={animation:{Feature:QL},exit:{Feature:YL}};function Xi(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}function uo(e){return{point:{x:e.pageX,y:e.pageY}}}const XL=e=>t=>mf(t)&&e(t,uo(t));function Pi(e,t,n,r){return Xi(e,t,XL(n),r)}const kp=(e,t)=>Math.abs(e-t);function ZL(e,t){const n=kp(e.x,t.x),r=kp(e.y,t.y);return Math.sqrt(n**2+r**2)}class Bv{constructor(t,n,{transformPagePoint:r,contextWindow:i,dragSnapToOrigin:o=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const f=oa(this.lastMoveEventInfo,this.history),h=this.startEvent!==null,d=ZL(f.offset,{x:0,y:0})>=3;if(!h&&!d)return;const{point:g}=f,{timestamp:v}=De;this.history.push({...g,timestamp:v});const{onStart:w,onMove:p}=this.handlers;h||(w&&w(this.lastMoveEvent,f),this.startEvent=this.lastMoveEvent),p&&p(this.lastMoveEvent,f)},this.handlePointerMove=(f,h)=>{this.lastMoveEvent=f,this.lastMoveEventInfo=ia(h,this.transformPagePoint),ue.update(this.updatePoint,!0)},this.handlePointerUp=(f,h)=>{this.end();const{onEnd:d,onSessionEnd:g,resumeAnimation:v}=this.handlers;if(this.dragSnapToOrigin&&v&&v(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const w=oa(f.type==="pointercancel"?this.lastMoveEventInfo:ia(h,this.transformPagePoint),this.history);this.startEvent&&d&&d(f,w),g&&g(f,w)},!mf(t))return;this.dragSnapToOrigin=o,this.handlers=n,this.transformPagePoint=r,this.contextWindow=i||window;const s=uo(t),l=ia(s,this.transformPagePoint),{point:a}=l,{timestamp:u}=De;this.history=[{...a,timestamp:u}];const{onSessionStart:c}=n;c&&c(t,oa(l,this.history)),this.removeListeners=ao(Pi(this.contextWindow,"pointermove",this.handlePointerMove),Pi(this.contextWindow,"pointerup",this.handlePointerUp),Pi(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),Cn(this.updatePoint)}}function ia(e,t){return t?{point:t(e.point)}:e}function Sp(e,t){return{x:e.x-t.x,y:e.y-t.y}}function oa({point:e},t){return{point:e,delta:Sp(e,Uv(t)),offset:Sp(e,JL(t)),velocity:eb(t,.1)}}function JL(e){return e[0]}function Uv(e){return e[e.length-1]}function eb(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const i=Uv(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>Kt(t)));)n--;if(!r)return{x:0,y:0};const o=Qt(i.timestamp-r.timestamp);if(o===0)return{x:0,y:0};const s={x:(i.x-r.x)/o,y:(i.y-r.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}const $v=1e-4,tb=1-$v,nb=1+$v,Hv=.01,rb=0-Hv,ib=0+Hv;function it(e){return e.max-e.min}function ob(e,t,n){return Math.abs(e-t)<=n}function Cp(e,t,n,r=.5){e.origin=r,e.originPoint=de(t.min,t.max,e.origin),e.scale=it(n)/it(t),e.translate=de(n.min,n.max,e.origin)-e.originPoint,(e.scale>=tb&&e.scale<=nb||isNaN(e.scale))&&(e.scale=1),(e.translate>=rb&&e.translate<=ib||isNaN(e.translate))&&(e.translate=0)}function Ai(e,t,n,r){Cp(e.x,t.x,n.x,r?r.originX:void 0),Cp(e.y,t.y,n.y,r?r.originY:void 0)}function Ep(e,t,n){e.min=n.min+t.min,e.max=e.min+it(t)}function sb(e,t,n){Ep(e.x,t.x,n.x),Ep(e.y,t.y,n.y)}function Tp(e,t,n){e.min=t.min-n.min,e.max=e.min+it(t)}function Di(e,t,n){Tp(e.x,t.x,n.x),Tp(e.y,t.y,n.y)}function lb(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?de(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?de(n,e,r.max):Math.min(e,n)),e}function Pp(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function ab(e,{top:t,left:n,bottom:r,right:i}){return{x:Pp(e.x,n,i),y:Pp(e.y,t,r)}}function Ap(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function ub(e,t){return{x:Ap(e.x,t.x),y:Ap(e.y,t.y)}}function cb(e,t){let n=.5;const r=it(e),i=it(t);return i>r?n=Nr(t.min,t.max-r,e.min):r>i&&(n=Nr(e.min,e.max-i,t.min)),Zt(0,1,n)}function fb(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const Lu=.35;function hb(e=Lu){return e===!1?e=0:e===!0&&(e=Lu),{x:Dp(e,"left","right"),y:Dp(e,"top","bottom")}}function Dp(e,t,n){return{min:Lp(e,t),max:Lp(e,n)}}function Lp(e,t){return typeof e=="number"?e:e[t]||0}const bp=()=>({translate:0,scale:1,origin:0,originPoint:0}),kr=()=>({x:bp(),y:bp()}),Rp=()=>({min:0,max:0}),xe=()=>({x:Rp(),y:Rp()});function at(e){return[e("x"),e("y")]}function Wv({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function db({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function pb(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function sa(e){return e===void 0||e===1}function bu({scale:e,scaleX:t,scaleY:n}){return!sa(e)||!sa(t)||!sa(n)}function On(e){return bu(e)||Kv(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function Kv(e){return Mp(e.x)||Mp(e.y)}function Mp(e){return e&&e!=="0%"}function Fs(e,t,n){const r=e-n,i=t*r;return n+i}function Ip(e,t,n,r,i){return i!==void 0&&(e=Fs(e,i,r)),Fs(e,n,r)+t}function Ru(e,t=0,n=1,r,i){e.min=Ip(e.min,t,n,r,i),e.max=Ip(e.max,t,n,r,i)}function Qv(e,{x:t,y:n}){Ru(e.x,t.translate,t.scale,t.originPoint),Ru(e.y,n.translate,n.scale,n.originPoint)}const _p=.999999999999,Op=1.0000000000001;function mb(e,t,n,r=!1){const i=n.length;if(!i)return;t.x=t.y=1;let o,s;for(let l=0;l<i;l++){o=n[l],s=o.projectionDelta;const{visualElement:a}=o.options;a&&a.props.style&&a.props.style.display==="contents"||(r&&o.options.layoutScroll&&o.scroll&&o!==o.root&&Cr(e,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),s&&(t.x*=s.x.scale,t.y*=s.y.scale,Qv(e,s)),r&&On(o.latestValues)&&Cr(e,o.latestValues))}t.x<Op&&t.x>_p&&(t.x=1),t.y<Op&&t.y>_p&&(t.y=1)}function Sr(e,t){e.min=e.min+t,e.max=e.max+t}function zp(e,t,n,r,i=.5){const o=de(e.min,e.max,i);Ru(e,t,n,o,r)}function Cr(e,t){zp(e.x,t.x,t.scaleX,t.scale,t.originX),zp(e.y,t.y,t.scaleY,t.scale,t.originY)}function Gv(e,t){return Wv(pb(e.getBoundingClientRect(),t))}function gb(e,t,n){const r=Gv(e,n),{scroll:i}=t;return i&&(Sr(r.x,i.offset.x),Sr(r.y,i.offset.y)),r}const Yv=({current:e})=>e?e.ownerDocument.defaultView:null,yb=new WeakMap;class vb{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=xe(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const i=c=>{const{dragSnapToOrigin:f}=this.getProps();f?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(uo(c).point)},o=(c,f)=>{const{drag:h,dragPropagation:d,onDragStart:g}=this.getProps();if(h&&!d&&(this.openDragLock&&this.openDragLock(),this.openDragLock=fD(h),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),at(w=>{let p=this.getAxisMotionValue(w).get()||0;if(zt.test(p)){const{projection:m}=this.visualElement;if(m&&m.layout){const y=m.layout.layoutBox[w];y&&(p=it(y)*(parseFloat(p)/100))}}this.originPoint[w]=p}),g&&ue.postRender(()=>g(c,f)),ku(this.visualElement,"transform");const{animationState:v}=this.visualElement;v&&v.setActive("whileDrag",!0)},s=(c,f)=>{const{dragPropagation:h,dragDirectionLock:d,onDirectionLock:g,onDrag:v}=this.getProps();if(!h&&!this.openDragLock)return;const{offset:w}=f;if(d&&this.currentDirection===null){this.currentDirection=xb(w),this.currentDirection!==null&&g&&g(this.currentDirection);return}this.updateAxis("x",f.point,w),this.updateAxis("y",f.point,w),this.visualElement.render(),v&&v(c,f)},l=(c,f)=>this.stop(c,f),a=()=>at(c=>{var f;return this.getAnimationState(c)==="paused"&&((f=this.getAxisMotionValue(c).animation)===null||f===void 0?void 0:f.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new Bv(t,{onSessionStart:i,onStart:o,onMove:s,onSessionEnd:l,resumeAnimation:a},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:Yv(this.visualElement)})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:o}=this.getProps();o&&ue.postRender(()=>o(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:i}=this.getProps();if(!r||!zo(t,i,this.currentDirection))return;const o=this.getAxisMotionValue(t);let s=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(s=lb(s,this.constraints[t],this.elastic[t])),o.set(s)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,o=this.constraints;n&&xr(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&i?this.constraints=ab(i.layoutBox,n):this.constraints=!1,this.elastic=hb(r),o!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&at(s=>{this.constraints!==!1&&this.getAxisMotionValue(s)&&(this.constraints[s]=fb(i.layoutBox[s],this.constraints[s]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!xr(t))return!1;const r=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const o=gb(r,i.root,this.visualElement.getTransformPagePoint());let s=ub(i.layout.layoutBox,o);if(n){const l=n(db(s));this.hasMutatedConstraints=!!l,l&&(s=Wv(l))}return s}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:i,dragTransition:o,dragSnapToOrigin:s,onDragTransitionEnd:l}=this.getProps(),a=this.constraints||{},u=at(c=>{if(!zo(c,n,this.currentDirection))return;let f=a&&a[c]||{};s&&(f={min:0,max:0});const h=i?200:1e6,d=i?40:1e7,g={type:"inertia",velocity:r?t[c]:0,bounceStiffness:h,bounceDamping:d,timeConstant:750,restDelta:1,restSpeed:10,...o,...f};return this.startAxisValueAnimation(c,g)});return Promise.all(u).then(l)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return ku(this.visualElement,t),r.start(Af(t,r,0,n,this.visualElement,!1))}stopAnimation(){at(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){at(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n=`_drag${t.toUpperCase()}`,r=this.visualElement.getProps(),i=r[n];return i||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){at(n=>{const{drag:r}=this.getProps();if(!zo(n,r,this.currentDirection))return;const{projection:i}=this.visualElement,o=this.getAxisMotionValue(n);if(i&&i.layout){const{min:s,max:l}=i.layout.layoutBox[n];o.set(t[n]-de(s,l,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!xr(n)||!r||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};at(s=>{const l=this.getAxisMotionValue(s);if(l&&this.constraints!==!1){const a=l.get();i[s]=cb({min:a,max:a},this.constraints[s])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),at(s=>{if(!zo(s,t,null))return;const l=this.getAxisMotionValue(s),{min:a,max:u}=this.constraints[s];l.set(de(a,u,i[s]))})}addListeners(){if(!this.visualElement.current)return;yb.set(this.visualElement,this);const t=this.visualElement.current,n=Pi(t,"pointerdown",a=>{const{drag:u,dragListener:c=!0}=this.getProps();u&&c&&this.start(a)}),r=()=>{const{dragConstraints:a}=this.getProps();xr(a)&&a.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,o=i.addEventListener("measure",r);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),ue.read(r);const s=Xi(window,"resize",()=>this.scalePositionWithinConstraints()),l=i.addEventListener("didUpdate",({delta:a,hasLayoutChanged:u})=>{this.isDragging&&u&&(at(c=>{const f=this.getAxisMotionValue(c);f&&(this.originPoint[c]+=a[c].translate,f.set(f.get()+a[c].translate))}),this.visualElement.render())});return()=>{s(),n(),o(),l&&l()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:o=!1,dragElastic:s=Lu,dragMomentum:l=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:i,dragConstraints:o,dragElastic:s,dragMomentum:l}}}function zo(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function xb(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class wb extends Ln{constructor(t){super(t),this.removeGroupControls=nt,this.removeListeners=nt,this.controls=new vb(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||nt}unmount(){this.removeGroupControls(),this.removeListeners()}}const Fp=e=>(t,n)=>{e&&ue.postRender(()=>e(t,n))};class kb extends Ln{constructor(){super(...arguments),this.removePointerDownListener=nt}onPointerDown(t){this.session=new Bv(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Yv(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:i}=this.node.getProps();return{onSessionStart:Fp(t),onStart:Fp(n),onMove:r,onEnd:(o,s)=>{delete this.session,i&&ue.postRender(()=>i(o,s))}}}mount(){this.removePointerDownListener=Pi(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const Jo={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Np(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const si={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(U.test(e))e=parseFloat(e);else return e;const n=Np(e,t.target.x),r=Np(e,t.target.y);return`${n}% ${r}%`}},Sb={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,i=En.parse(e);if(i.length>5)return r;const o=En.createTransformer(e),s=typeof i[0]!="number"?1:0,l=n.x.scale*t.x,a=n.y.scale*t.y;i[0+s]/=l,i[1+s]/=a;const u=de(l,a,.5);return typeof i[2+s]=="number"&&(i[2+s]/=u),typeof i[3+s]=="number"&&(i[3+s]/=u),o(i)}};class Cb extends b.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:i}=this.props,{projection:o}=t;H2(Eb),o&&(n.group&&n.group.add(o),r&&r.register&&i&&r.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),Jo.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:i,isPresent:o}=this.props,s=r.projection;return s&&(s.isPresent=o,i||t.layoutDependency!==n||n===void 0?s.willUpdate():this.safeToRemove(),t.isPresent!==o&&(o?s.promote():s.relegate()||ue.postRender(()=>{const l=s.getStack();(!l||!l.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),tf.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),r&&r.deregister&&r.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function qv(e){const[t,n]=I0(),r=b.useContext(Qc);return V.jsx(Cb,{...e,layoutGroup:r,switchLayoutGroup:b.useContext(V0),isPresent:t,safeToRemove:n})}const Eb={borderRadius:{...si,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:si,borderTopRightRadius:si,borderBottomLeftRadius:si,borderBottomRightRadius:si,boxShadow:Sb};function Tb(e,t,n){const r=ze(e)?e:Yi(e);return r.start(Af("",r,t,n)),r.animation}function Pb(e){return e instanceof SVGElement&&e.tagName!=="svg"}const Ab=(e,t)=>e.depth-t.depth;class Db{constructor(){this.children=[],this.isDirty=!1}add(t){gf(this.children,t),this.isDirty=!0}remove(t){yf(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(Ab),this.isDirty=!1,this.children.forEach(t)}}function Lb(e,t){const n=Ft.now(),r=({timestamp:i})=>{const o=i-n;o>=t&&(Cn(r),e(o-t))};return ue.read(r,!0),()=>Cn(r)}const Xv=["TopLeft","TopRight","BottomLeft","BottomRight"],bb=Xv.length,jp=e=>typeof e=="string"?parseFloat(e):e,Vp=e=>typeof e=="number"||U.test(e);function Rb(e,t,n,r,i,o){i?(e.opacity=de(0,n.opacity!==void 0?n.opacity:1,Mb(r)),e.opacityExit=de(t.opacity!==void 0?t.opacity:1,0,Ib(r))):o&&(e.opacity=de(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let s=0;s<bb;s++){const l=`border${Xv[s]}Radius`;let a=Bp(t,l),u=Bp(n,l);if(a===void 0&&u===void 0)continue;a||(a=0),u||(u=0),a===0||u===0||Vp(a)===Vp(u)?(e[l]=Math.max(de(jp(a),jp(u),r),0),(zt.test(u)||zt.test(a))&&(e[l]+="%")):e[l]=u}(t.rotate||n.rotate)&&(e.rotate=de(t.rotate||0,n.rotate||0,r))}function Bp(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const Mb=Zv(0,.5,gv),Ib=Zv(.5,.95,nt);function Zv(e,t,n){return r=>r<e?0:r>t?1:n(Nr(e,t,r))}function Up(e,t){e.min=t.min,e.max=t.max}function lt(e,t){Up(e.x,t.x),Up(e.y,t.y)}function $p(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function Hp(e,t,n,r,i){return e-=t,e=Fs(e,1/n,r),i!==void 0&&(e=Fs(e,1/i,r)),e}function _b(e,t=0,n=1,r=.5,i,o=e,s=e){if(zt.test(t)&&(t=parseFloat(t),t=de(s.min,s.max,t/100)-s.min),typeof t!="number")return;let l=de(o.min,o.max,r);e===o&&(l-=t),e.min=Hp(e.min,t,n,l,i),e.max=Hp(e.max,t,n,l,i)}function Wp(e,t,[n,r,i],o,s){_b(e,t[n],t[r],t[i],t.scale,o,s)}const Ob=["x","scaleX","originX"],zb=["y","scaleY","originY"];function Kp(e,t,n,r){Wp(e.x,t,Ob,n?n.x:void 0,r?r.x:void 0),Wp(e.y,t,zb,n?n.y:void 0,r?r.y:void 0)}function Qp(e){return e.translate===0&&e.scale===1}function Jv(e){return Qp(e.x)&&Qp(e.y)}function Gp(e,t){return e.min===t.min&&e.max===t.max}function Fb(e,t){return Gp(e.x,t.x)&&Gp(e.y,t.y)}function Yp(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function e1(e,t){return Yp(e.x,t.x)&&Yp(e.y,t.y)}function qp(e){return it(e.x)/it(e.y)}function Xp(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class Nb{constructor(){this.members=[]}add(t){gf(this.members,t),t.scheduleRender()}remove(t){if(yf(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(i=>t===i);if(n===0)return!1;let r;for(let i=n;i>=0;i--){const o=this.members[i];if(o.isPresent!==!1){r=o;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;i===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function jb(e,t,n){let r="";const i=e.x.translate/t.x,o=e.y.translate/t.y,s=(n==null?void 0:n.z)||0;if((i||o||s)&&(r=`translate3d(${i}px, ${o}px, ${s}px) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{transformPerspective:u,rotate:c,rotateX:f,rotateY:h,skewX:d,skewY:g}=n;u&&(r=`perspective(${u}px) ${r}`),c&&(r+=`rotate(${c}deg) `),f&&(r+=`rotateX(${f}deg) `),h&&(r+=`rotateY(${h}deg) `),d&&(r+=`skewX(${d}deg) `),g&&(r+=`skewY(${g}deg) `)}const l=e.x.scale*t.x,a=e.y.scale*t.y;return(l!==1||a!==1)&&(r+=`scale(${l}, ${a})`),r||"none"}const zn={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},di=typeof window<"u"&&window.MotionDebug!==void 0,la=["","X","Y","Z"],Vb={visibility:"hidden"},Zp=1e3;let Bb=0;function aa(e,t,n,r){const{latestValues:i}=t;i[e]&&(n[e]=i[e],t.setStaticValue(e,0),r&&(r[e]=0))}function t1(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;const{visualElement:t}=e.options;if(!t)return;const n=uv(t);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:i,layoutId:o}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",ue,!(i||o))}const{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&t1(r)}function n1({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(s={},l=t==null?void 0:t()){this.id=Bb++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,di&&(zn.totalNodes=zn.resolvedTargetDeltas=zn.recalculatedProjection=0),this.nodes.forEach(Hb),this.nodes.forEach(Yb),this.nodes.forEach(qb),this.nodes.forEach(Wb),di&&window.MotionDebug.record(zn)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=s,this.root=l?l.root||l:this,this.path=l?[...l.path,l]:[],this.parent=l,this.depth=l?l.depth+1:0;for(let a=0;a<this.path.length;a++)this.path[a].shouldResetTransform=!0;this.root===this&&(this.nodes=new Db)}addEventListener(s,l){return this.eventHandlers.has(s)||this.eventHandlers.set(s,new vf),this.eventHandlers.get(s).add(l)}notifyListeners(s,...l){const a=this.eventHandlers.get(s);a&&a.notify(...l)}hasListeners(s){return this.eventHandlers.has(s)}mount(s,l=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=Pb(s),this.instance=s;const{layoutId:a,layout:u,visualElement:c}=this.options;if(c&&!c.current&&c.mount(s),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),l&&(u||a)&&(this.isLayoutDirty=!0),e){let f;const h=()=>this.root.updateBlockedByResize=!1;e(s,()=>{this.root.updateBlockedByResize=!0,f&&f(),f=Lb(h,250),Jo.hasAnimatedSinceResize&&(Jo.hasAnimatedSinceResize=!1,this.nodes.forEach(em))})}a&&this.root.registerSharedNode(a,this),this.options.animate!==!1&&c&&(a||u)&&this.addEventListener("didUpdate",({delta:f,hasLayoutChanged:h,hasRelativeTargetChanged:d,layout:g})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const v=this.options.transition||c.getDefaultTransition()||tR,{onLayoutAnimationStart:w,onLayoutAnimationComplete:p}=c.getProps(),m=!this.targetLayout||!e1(this.targetLayout,g)||d,y=!h&&d;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||y||h&&(m||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(f,y);const C={...hf(v,"layout"),onPlay:w,onComplete:p};(c.shouldReduceMotion||this.options.layoutRoot)&&(C.delay=0,C.type=!1),this.startAnimation(C)}else h||em(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=g})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const s=this.getStack();s&&s.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Cn(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Xb),this.animationId++)}getTransformTemplate(){const{visualElement:s}=this.options;return s&&s.getProps().transformTemplate}willUpdate(s=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&t1(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let c=0;c<this.path.length;c++){const f=this.path[c];f.shouldResetTransform=!0,f.updateScroll("snapshot"),f.options.layoutRoot&&f.willUpdate(!1)}const{layoutId:l,layout:a}=this.options;if(l===void 0&&!a)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),s&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Jp);return}this.isUpdating||this.nodes.forEach(Qb),this.isUpdating=!1,this.nodes.forEach(Gb),this.nodes.forEach(Ub),this.nodes.forEach($b),this.clearAllSnapshots();const l=Ft.now();De.delta=Zt(0,1e3/60,l-De.timestamp),De.timestamp=l,De.isProcessing=!0,Zl.update.process(De),Zl.preRender.process(De),Zl.render.process(De),De.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,tf.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(Kb),this.sharedNodes.forEach(Zb)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,ue.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){ue.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let a=0;a<this.path.length;a++)this.path[a].updateScroll();const s=this.layout;this.layout=this.measure(!1),this.layoutCorrected=xe(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:l}=this.options;l&&l.notify("LayoutMeasure",this.layout.layoutBox,s?s.layoutBox:void 0)}updateScroll(s="measure"){let l=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===s&&(l=!1),l){const a=r(this.instance);this.scroll={animationId:this.root.animationId,phase:s,isRoot:a,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:a}}}resetTransform(){if(!i)return;const s=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,l=this.projectionDelta&&!Jv(this.projectionDelta),a=this.getTransformTemplate(),u=a?a(this.latestValues,""):void 0,c=u!==this.prevTransformTemplateValue;s&&(l||On(this.latestValues)||c)&&(i(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(s=!0){const l=this.measurePageBox();let a=this.removeElementScroll(l);return s&&(a=this.removeTransform(a)),nR(a),{animationId:this.root.animationId,measuredBox:l,layoutBox:a,latestValues:{},source:this.id}}measurePageBox(){var s;const{visualElement:l}=this.options;if(!l)return xe();const a=l.measureViewportBox();if(!(((s=this.scroll)===null||s===void 0?void 0:s.wasRoot)||this.path.some(rR))){const{scroll:c}=this.root;c&&(Sr(a.x,c.offset.x),Sr(a.y,c.offset.y))}return a}removeElementScroll(s){var l;const a=xe();if(lt(a,s),!((l=this.scroll)===null||l===void 0)&&l.wasRoot)return a;for(let u=0;u<this.path.length;u++){const c=this.path[u],{scroll:f,options:h}=c;c!==this.root&&f&&h.layoutScroll&&(f.wasRoot&&lt(a,s),Sr(a.x,f.offset.x),Sr(a.y,f.offset.y))}return a}applyTransform(s,l=!1){const a=xe();lt(a,s);for(let u=0;u<this.path.length;u++){const c=this.path[u];!l&&c.options.layoutScroll&&c.scroll&&c!==c.root&&Cr(a,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),On(c.latestValues)&&Cr(a,c.latestValues)}return On(this.latestValues)&&Cr(a,this.latestValues),a}removeTransform(s){const l=xe();lt(l,s);for(let a=0;a<this.path.length;a++){const u=this.path[a];if(!u.instance||!On(u.latestValues))continue;bu(u.latestValues)&&u.updateSnapshot();const c=xe(),f=u.measurePageBox();lt(c,f),Kp(l,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,c)}return On(this.latestValues)&&Kp(l,this.latestValues),l}setTargetDelta(s){this.targetDelta=s,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(s){this.options={...this.options,...s,crossfade:s.crossfade!==void 0?s.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==De.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(s=!1){var l;const a=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=a.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==a;if(!(s||u&&this.isSharedProjectionDirty||this.isProjectionDirty||!((l=this.parent)===null||l===void 0)&&l.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:f,layoutId:h}=this.options;if(!(!this.layout||!(f||h))){if(this.resolvedRelativeTargetAt=De.timestamp,!this.targetDelta&&!this.relativeTarget){const d=this.getClosestProjectingParent();d&&d.layout&&this.animationProgress!==1?(this.relativeParent=d,this.forceRelativeParentToResolveTarget(),this.relativeTarget=xe(),this.relativeTargetOrigin=xe(),Di(this.relativeTargetOrigin,this.layout.layoutBox,d.layout.layoutBox),lt(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=xe(),this.targetWithTransforms=xe()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),sb(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):lt(this.target,this.layout.layoutBox),Qv(this.target,this.targetDelta)):lt(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const d=this.getClosestProjectingParent();d&&!!d.resumingFrom==!!this.resumingFrom&&!d.options.layoutScroll&&d.target&&this.animationProgress!==1?(this.relativeParent=d,this.forceRelativeParentToResolveTarget(),this.relativeTarget=xe(),this.relativeTargetOrigin=xe(),Di(this.relativeTargetOrigin,this.target,d.target),lt(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}di&&zn.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||bu(this.parent.latestValues)||Kv(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var s;const l=this.getLead(),a=!!this.resumingFrom||this!==l;let u=!0;if((this.isProjectionDirty||!((s=this.parent)===null||s===void 0)&&s.isProjectionDirty)&&(u=!1),a&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===De.timestamp&&(u=!1),u)return;const{layout:c,layoutId:f}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(c||f))return;lt(this.layoutCorrected,this.layout.layoutBox);const h=this.treeScale.x,d=this.treeScale.y;mb(this.layoutCorrected,this.treeScale,this.path,a),l.layout&&!l.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(l.target=l.layout.layoutBox,l.targetWithTransforms=xe());const{target:g}=l;if(!g){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():($p(this.prevProjectionDelta.x,this.projectionDelta.x),$p(this.prevProjectionDelta.y,this.projectionDelta.y)),Ai(this.projectionDelta,this.layoutCorrected,g,this.latestValues),(this.treeScale.x!==h||this.treeScale.y!==d||!Xp(this.projectionDelta.x,this.prevProjectionDelta.x)||!Xp(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",g)),di&&zn.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(s=!0){var l;if((l=this.options.visualElement)===null||l===void 0||l.scheduleRender(),s){const a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=kr(),this.projectionDelta=kr(),this.projectionDeltaWithTransform=kr()}setAnimationOrigin(s,l=!1){const a=this.snapshot,u=a?a.latestValues:{},c={...this.latestValues},f=kr();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!l;const h=xe(),d=a?a.source:void 0,g=this.layout?this.layout.source:void 0,v=d!==g,w=this.getStack(),p=!w||w.members.length<=1,m=!!(v&&!p&&this.options.crossfade===!0&&!this.path.some(eR));this.animationProgress=0;let y;this.mixTargetDelta=C=>{const T=C/1e3;tm(f.x,s.x,T),tm(f.y,s.y,T),this.setTargetDelta(f),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Di(h,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Jb(this.relativeTarget,this.relativeTargetOrigin,h,T),y&&Fb(this.relativeTarget,y)&&(this.isProjectionDirty=!1),y||(y=xe()),lt(y,this.relativeTarget)),v&&(this.animationValues=c,Rb(c,u,this.latestValues,T,m,p)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=T},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(s){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Cn(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=ue.update(()=>{Jo.hasAnimatedSinceResize=!0,this.currentAnimation=Tb(0,Zp,{...s,onUpdate:l=>{this.mixTargetDelta(l),s.onUpdate&&s.onUpdate(l)},onComplete:()=>{s.onComplete&&s.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const s=this.getStack();s&&s.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Zp),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const s=this.getLead();let{targetWithTransforms:l,target:a,layout:u,latestValues:c}=s;if(!(!l||!a||!u)){if(this!==s&&this.layout&&u&&r1(this.options.animationType,this.layout.layoutBox,u.layoutBox)){a=this.target||xe();const f=it(this.layout.layoutBox.x);a.x.min=s.target.x.min,a.x.max=a.x.min+f;const h=it(this.layout.layoutBox.y);a.y.min=s.target.y.min,a.y.max=a.y.min+h}lt(l,a),Cr(l,c),Ai(this.projectionDeltaWithTransform,this.layoutCorrected,l,c)}}registerSharedNode(s,l){this.sharedNodes.has(s)||this.sharedNodes.set(s,new Nb),this.sharedNodes.get(s).add(l);const u=l.options.initialPromotionConfig;l.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(l):void 0})}isLead(){const s=this.getStack();return s?s.lead===this:!0}getLead(){var s;const{layoutId:l}=this.options;return l?((s=this.getStack())===null||s===void 0?void 0:s.lead)||this:this}getPrevLead(){var s;const{layoutId:l}=this.options;return l?(s=this.getStack())===null||s===void 0?void 0:s.prevLead:void 0}getStack(){const{layoutId:s}=this.options;if(s)return this.root.sharedNodes.get(s)}promote({needsReset:s,transition:l,preserveFollowOpacity:a}={}){const u=this.getStack();u&&u.promote(this,a),s&&(this.projectionDelta=void 0,this.needsReset=!0),l&&this.setOptions({transition:l})}relegate(){const s=this.getStack();return s?s.relegate(this):!1}resetSkewAndRotation(){const{visualElement:s}=this.options;if(!s)return;let l=!1;const{latestValues:a}=s;if((a.z||a.rotate||a.rotateX||a.rotateY||a.rotateZ||a.skewX||a.skewY)&&(l=!0),!l)return;const u={};a.z&&aa("z",s,u,this.animationValues);for(let c=0;c<la.length;c++)aa(`rotate${la[c]}`,s,u,this.animationValues),aa(`skew${la[c]}`,s,u,this.animationValues);s.render();for(const c in u)s.setStaticValue(c,u[c]),this.animationValues&&(this.animationValues[c]=u[c]);s.scheduleRender()}getProjectionStyles(s){var l,a;if(!this.instance||this.isSVG)return;if(!this.isVisible)return Vb;const u={visibility:""},c=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=Xo(s==null?void 0:s.pointerEvents)||"",u.transform=c?c(this.latestValues,""):"none",u;const f=this.getLead();if(!this.projectionDelta||!this.layout||!f.target){const v={};return this.options.layoutId&&(v.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,v.pointerEvents=Xo(s==null?void 0:s.pointerEvents)||""),this.hasProjected&&!On(this.latestValues)&&(v.transform=c?c({},""):"none",this.hasProjected=!1),v}const h=f.animationValues||f.latestValues;this.applyTransformsToTarget(),u.transform=jb(this.projectionDeltaWithTransform,this.treeScale,h),c&&(u.transform=c(h,u.transform));const{x:d,y:g}=this.projectionDelta;u.transformOrigin=`${d.origin*100}% ${g.origin*100}% 0`,f.animationValues?u.opacity=f===this?(a=(l=h.opacity)!==null&&l!==void 0?l:this.latestValues.opacity)!==null&&a!==void 0?a:1:this.preserveOpacity?this.latestValues.opacity:h.opacityExit:u.opacity=f===this?h.opacity!==void 0?h.opacity:"":h.opacityExit!==void 0?h.opacityExit:0;for(const v in Ms){if(h[v]===void 0)continue;const{correct:w,applyTo:p}=Ms[v],m=u.transform==="none"?h[v]:w(h[v],f);if(p){const y=p.length;for(let C=0;C<y;C++)u[p[C]]=m}else u[v]=m}return this.options.layoutId&&(u.pointerEvents=f===this?Xo(s==null?void 0:s.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(s=>{var l;return(l=s.currentAnimation)===null||l===void 0?void 0:l.stop()}),this.root.nodes.forEach(Jp),this.root.sharedNodes.clear()}}}function Ub(e){e.updateLayout()}function $b(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:i}=e.layout,{animationType:o}=e.options,s=n.source!==e.layout.source;o==="size"?at(f=>{const h=s?n.measuredBox[f]:n.layoutBox[f],d=it(h);h.min=r[f].min,h.max=h.min+d}):r1(o,n.layoutBox,r)&&at(f=>{const h=s?n.measuredBox[f]:n.layoutBox[f],d=it(r[f]);h.max=h.min+d,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[f].max=e.relativeTarget[f].min+d)});const l=kr();Ai(l,r,n.layoutBox);const a=kr();s?Ai(a,e.applyTransform(i,!0),n.measuredBox):Ai(a,r,n.layoutBox);const u=!Jv(l);let c=!1;if(!e.resumeFrom){const f=e.getClosestProjectingParent();if(f&&!f.resumeFrom){const{snapshot:h,layout:d}=f;if(h&&d){const g=xe();Di(g,n.layoutBox,h.layoutBox);const v=xe();Di(v,r,d.layoutBox),e1(g,v)||(c=!0),f.options.layoutRoot&&(e.relativeTarget=v,e.relativeTargetOrigin=g,e.relativeParent=f)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:a,layoutDelta:l,hasLayoutChanged:u,hasRelativeTargetChanged:c})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function Hb(e){di&&zn.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function Wb(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function Kb(e){e.clearSnapshot()}function Jp(e){e.clearMeasurements()}function Qb(e){e.isLayoutDirty=!1}function Gb(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function em(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function Yb(e){e.resolveTargetDelta()}function qb(e){e.calcProjection()}function Xb(e){e.resetSkewAndRotation()}function Zb(e){e.removeLeadSnapshot()}function tm(e,t,n){e.translate=de(t.translate,0,n),e.scale=de(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function nm(e,t,n,r){e.min=de(t.min,n.min,r),e.max=de(t.max,n.max,r)}function Jb(e,t,n,r){nm(e.x,t.x,n.x,r),nm(e.y,t.y,n.y,r)}function eR(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const tR={duration:.45,ease:[.4,0,.1,1]},rm=e=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),im=rm("applewebkit/")&&!rm("chrome/")?Math.round:nt;function om(e){e.min=im(e.min),e.max=im(e.max)}function nR(e){om(e.x),om(e.y)}function r1(e,t,n){return e==="position"||e==="preserve-aspect"&&!ob(qp(t),qp(n),.2)}function rR(e){var t;return e!==e.root&&((t=e.scroll)===null||t===void 0?void 0:t.wasRoot)}const iR=n1({attachResizeListener:(e,t)=>Xi(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ua={current:void 0},i1=n1({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!ua.current){const e=new iR({});e.mount(window),e.setOptions({layoutScroll:!0}),ua.current=e}return ua.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),oR={pan:{Feature:kb},drag:{Feature:wb,ProjectionNode:i1,MeasureLayout:qv}};function sm(e,t,n){const{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover",n==="Start");const i="onHover"+n,o=r[i];o&&ue.postRender(()=>o(t,uo(t)))}class sR extends Ln{mount(){const{current:t}=this.node;t&&(this.unmount=sD(t,n=>(sm(this.node,n,"Start"),r=>sm(this.node,r,"End"))))}unmount(){}}class lR extends Ln{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=ao(Xi(this.node.current,"focus",()=>this.onFocus()),Xi(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function lm(e,t,n){const{props:r}=e;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap",n==="Start");const i="onTap"+(n==="End"?"":n),o=r[i];o&&ue.postRender(()=>o(t,uo(t)))}class aR extends Ln{mount(){const{current:t}=this.node;t&&(this.unmount=cD(t,n=>(lm(this.node,n,"Start"),(r,{success:i})=>lm(this.node,r,i?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const Mu=new WeakMap,ca=new WeakMap,uR=e=>{const t=Mu.get(e.target);t&&t(e)},cR=e=>{e.forEach(uR)};function fR({root:e,...t}){const n=e||document;ca.has(n)||ca.set(n,{});const r=ca.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(cR,{root:e,...t})),r[i]}function hR(e,t,n){const r=fR(t);return Mu.set(e,n),r.observe(e),()=>{Mu.delete(e),r.unobserve(e)}}const dR={some:0,all:1};class pR extends Ln{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:i="some",once:o}=t,s={root:n?n.current:void 0,rootMargin:r,threshold:typeof i=="number"?i:dR[i]},l=a=>{const{isIntersecting:u}=a;if(this.isInView===u||(this.isInView=u,o&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:c,onViewportLeave:f}=this.node.getProps(),h=u?c:f;h&&h(a)};return hR(this.node.current,s,l)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(mR(t,n))&&this.startObserver()}unmount(){}}function mR({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const gR={inView:{Feature:pR},tap:{Feature:aR},focus:{Feature:lR},hover:{Feature:sR}},yR={layout:{ProjectionNode:i1,MeasureLayout:qv}},Iu={current:null},o1={current:!1};function vR(){if(o1.current=!0,!!qc)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Iu.current=e.matches;e.addListener(t),t()}else Iu.current=!1}const xR=[...bv,Ie,En],wR=e=>xR.find(Lv(e)),am=new WeakMap;function kR(e,t,n){for(const r in t){const i=t[r],o=n[r];if(ze(i))e.addValue(r,i);else if(ze(o))e.addValue(r,Yi(i,{owner:e}));else if(o!==i)if(e.hasValue(r)){const s=e.getValue(r);s.liveStyle===!0?s.jump(i):s.hasAnimated||s.set(i)}else{const s=e.getStaticValue(r);e.addValue(r,Yi(s!==void 0?s:i,{owner:e}))}}for(const r in n)t[r]===void 0&&e.removeValue(r);return t}const um=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class SR{scrapeMotionValuesFromProps(t,n,r){return{}}constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:i,blockInitialAnimation:o,visualState:s},l={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Ef,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const d=Ft.now();this.renderScheduledAt<d&&(this.renderScheduledAt=d,ue.render(this.render,!1,!0))};const{latestValues:a,renderState:u,onUpdate:c}=s;this.onUpdate=c,this.latestValues=a,this.baseTarget={...a},this.initialValues=n.initial?{...a}:{},this.renderState=u,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=l,this.blockInitialAnimation=!!o,this.isControllingVariants=al(n),this.isVariantNode=N0(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:f,...h}=this.scrapeMotionValuesFromProps(n,{},this);for(const d in h){const g=h[d];a[d]!==void 0&&ze(g)&&g.set(a[d],!1)}}mount(t){this.current=t,am.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),o1.current||vR(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Iu.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){am.delete(this.current),this.projection&&this.projection.unmount(),Cn(this.notifyUpdate),Cn(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const n=this.features[t];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(t,n){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const r=er.has(t),i=n.on("change",l=>{this.latestValues[t]=l,this.props.onUpdate&&ue.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),o=n.on("renderRequest",this.scheduleRender);let s;window.MotionCheckAppearSync&&(s=window.MotionCheckAppearSync(this,t,n)),this.valueSubscriptions.set(t,()=>{i(),o(),s&&s(),n.owner&&n.stop()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}updateFeatures(){let t="animation";for(t in jr){const n=jr[t];if(!n)continue;const{isEnabled:r,Feature:i}=n;if(!this.features[t]&&i&&r(this.props)&&(this.features[t]=new i(this)),this.features[t]){const o=this.features[t];o.isMounted?o.update():(o.mount(),o.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):xe()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<um.length;r++){const i=um[r];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const o="on"+i,s=t[o];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=kR(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){const r=this.values.get(t);n!==r&&(r&&this.removeValue(t),this.bindToMotionValue(t,n),this.values.set(t,n),this.latestValues[t]=n.get())}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=Yi(n===null?void 0:n,{owner:this}),this.addValue(t,r)),r}readValue(t,n){var r;let i=this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(r=this.getBaseTargetFromProps(this.props,t))!==null&&r!==void 0?r:this.readValueFromInstance(this.current,t,this.options);return i!=null&&(typeof i=="string"&&(Av(i)||vv(i))?i=parseFloat(i):!wR(i)&&En.test(n)&&(i=Ev(t,n)),this.setBaseTarget(t,ze(i)?i.get():i)),ze(i)?i.get():i}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props;let i;if(typeof r=="string"||typeof r=="object"){const s=rf(this.props,r,(n=this.presenceContext)===null||n===void 0?void 0:n.custom);s&&(i=s[t])}if(r&&i!==void 0)return i;const o=this.getBaseTargetFromProps(this.props,t);return o!==void 0&&!ze(o)?o:this.initialValues[t]!==void 0&&i===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new vf),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class s1 extends SR{constructor(){super(...arguments),this.KeyframeResolver=Rv}sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;ze(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function CR(e){return window.getComputedStyle(e)}class ER extends s1{constructor(){super(...arguments),this.type="html",this.renderInstance=Q0}readValueFromInstance(t,n){if(er.has(n)){const r=Cf(n);return r&&r.default||0}else{const r=CR(t),i=(H0(n)?r.getPropertyValue(n):r[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(t,{transformPagePoint:n}){return Gv(t,n)}build(t,n,r){lf(t,n,r.transformTemplate)}scrapeMotionValuesFromProps(t,n,r){return ff(t,n,r)}}class TR extends s1{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=xe}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(er.has(n)){const r=Cf(n);return r&&r.default||0}return n=G0.has(n)?n:ef(n),t.getAttribute(n)}scrapeMotionValuesFromProps(t,n,r){return X0(t,n,r)}build(t,n,r){af(t,n,this.isSVGTag,r.transformTemplate)}renderInstance(t,n,r,i){Y0(t,n,r,i)}mount(t){this.isSVGTag=cf(t.tagName),super.mount(t)}}const PR=(e,t)=>nf(e)?new TR(t):new ER(t,{allowProjection:e!==b.Fragment}),AR=J2({...qL,...gR,...oR,...yR},PR),DR=p2(AR),LR=({title:e,message:t,code:n,showError:r})=>V.jsx(s2,{children:r?V.jsx(DR.div,{initial:{opacity:0,scale:1.1},animate:{opacity:1,scale:1},transition:{duration:.25,type:"tween"},exit:{opacity:0,scale:1.2},className:"error-screen",children:V.jsxs("div",{className:"error-content",children:[V.jsx(t2,{size:60}),V.jsxs("div",{className:"flex flex-col",children:[V.jsx("h2",{className:"error-title",children:e}),V.jsx("p",{className:"error-message",children:t}),V.jsxs("p",{className:"text-xs text-white/50",children:["Code ",n]})]})]})}):null}),bR=({apiUrl:e})=>{const{chats:t,messages:n,inputValue:r,canSend:i,activeChat:o,loadingChat:s,partialMessage:l,isStreaming:a,waitingResponse:u,displayError:c,error:f,handleSetActiveChat:h,handleOnChangeInput:d,handleNewChat:g,handleSendMessage:v,handleRemoveChat:w,config:p}=e2(e);return b.useState(!1),V.jsx("div",{className:"aichat-container",children:V.jsxs("div",{className:"chat-container",children:[V.jsx(LR,{title:f?f.title:"",message:f?f.message:"",code:f?f.code:0,showError:c}),V.jsxs("div",{className:"top-container",children:[s?V.jsx(WA,{}):V.jsx($A,{messages:n,partialMessage:l,isStreaming:a,waitingResponse:u}),n.length===0&&!s&&p.disclaimer!=""&&V.jsx("div",{className:"disclaimer",children:p.disclaimer})]}),V.jsxs("div",{className:"bottom-container",children:[V.jsx("div",{className:"relative w-full flex justify-center",children:V.jsxs("div",{className:"input-container",children:[V.jsx(rk,{maxRows:3,autoComplete:"off",value:r,placeholder:p.translations.front_input_placeholder,onChange:m=>d(m.target.value),onKeyDown:m=>{m.key==="Enter"&&!m.shiftKey&&(m.preventDefault(),v(r))},className:"input"}),V.jsx("button",{className:"absolute bottom-4 right-3 rounded-full bg-[#4c6586] text-white flex items-center justify-center p-2 transition-all ease-in-out disabled:opacity-50",disabled:!i,onClick:()=>v(r),children:V.jsx(fk,{size:20})})]})}),V.jsx("div",{className:"flex justify-center items-center",children:V.jsx("button",{onClick:()=>w(0),className:"text-gray-400 text-xs hover:text-[#4c6586] hover:underline",children:"New conversation"})})]})]})})},fa=document.getElementById("root"),ha=fa==null?void 0:fa.getAttribute("apiurl");ha?da.createRoot(document.getElementById("root")).render(V.jsx(hn.StrictMode,{children:V.jsx(JA,{apiUrl:ha,children:V.jsx(bR,{apiUrl:ha})})})):console.error("API URL not found in the root element.");

}, 50);
</file>

<file path="templates/js/script.js">
$(document).ready(function() {
  const floatButton = $(`<button title="Abrir Chat AI" class="principalButton">
        <img src="Customizing/global/plugins/Services/UIComponent/UserInterfaceHook/AIChatUIHook/templates/img/SVG_Logo.png" alt="AI Chat Icon" />
    </button>`);

  floatButton.on('click', function() {
    $('.floatWindow').toggleClass('active');
  });

  $('body').append(floatButton);
});
</file>

</files>
