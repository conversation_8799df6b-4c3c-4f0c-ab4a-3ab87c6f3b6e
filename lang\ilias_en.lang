obj_xaic#:#AI Chat
objs_xaic#:#AI Chat Objects
xaic_new#:#New AI Chat
objs_xaic_duplicate#:#Duplicate AI Chat Object
xaic_add#:#Add AI Chat
object_content#:#Content
object_settings#:#Settings
object_settings_basic#:#Basic Settings
object_settings_advanced#:#Advanced Settings
object_settings_title#:#Title
object_settings_description#:#Description
object_settings_online#:#Online
object_settings_status#:#Status
object_settings_offline#:#Offline
object_settings_online_info#:#AI Chat will be available to users when this option is enabled.
object_settings_msg_success#:#Settings saved successfully.
chat_default_title#:#New Chat
front_new_chat_button#:#New Chat
front_input_placeholder#:#Type a message...
error_http#:#Error connecting to the API.
error_apikey#:#The API key is invalid.
config_general#:#General
config_openai#:#OpenAI
config_ollama#:#Ollama
config_service_label#:#Service to Use
config_service_info#:#The service that will be used for the chat, remember to save the configuration so you can configure the service below.
config_available_services#:#Available Services
config_prompt_label#:#System Prompt
config_prompt_info#:#This prompt will be used as the initial message in a conversation, it will not be visible to the user, but the model will take it into account.
config_characters_limit_label#:#Character Limit
config_characters_limit_info#:#The maximum number of characters you can send in a single message.
config_max_memory_messages_label#:#Maximum Number of Memory Messages
config_max_memory_messages_info#:#The maximum number of messages that will be stored in the conversation.
config_disclaimer_label#:#Legal Disclaimer
config_disclaimer_info#:#Text that will be displayed in the chat to inform the user that they are talking to an artificial intelligence.
config_openai_models_label#:#Models
config_openai_key_label#:#API Key
config_openai_key_info#:#Your OpenAI API key
config_openai_stream_label#:#Enable Streaming
config_openai_stream_info#:#Enable streaming responses from OpenAI
config_ollama_endpoint_label#:#Server Endpoint
config_ollama_endpoint_info#:#The URL of the Ollama server endpoint
config_ollama_models_label#:#Available Models
config_msg_success#:#Configuration saved successfully.
config_ollama_models_error#:#Error getting available models, please check the server endpoint.
object_no_llm#:#There is no language model selected.
object_offline_info#:#AI Chat is offline.
config_api_section#:#Api configuration
config_general_section#:#General configuration
config_prompt_selection#:#Select a prompt
config_disclaimer_text#:#Disclaimer Text
config_n_memory_messages#:#Number of memory messages
config_characters_limit#:#Character limit
config_prompt_selection_info#:#Prompt information
config_disclaimer_text_info#:#Disclaimer text
config_n_memory_messages_info#:#Number of memory messages
config_gwdg_key_label#:#API Key
config_gwdg_key_info#:#Your GWDG API key
config_gwdg_stream_label#:#Enable Streaming
config_gwdg_stream_info#:#Enable streaming responses from GWDG
config_gwdg_models_label#:#Models
config_gwdg_models_error#:#Error getting available models, please check the api key.